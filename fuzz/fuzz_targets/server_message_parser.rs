#![no_main]

use libfuzzer_sys::fuzz_target;
use indidus_e2ee_server::handlers::websocket::ServerMessage;

fuzz_target!(|data: &[u8]| {
    // Try to deserialize the raw bytes as a JSON string first
    if let Ok(json_str) = std::str::from_utf8(data) {
        // Attempt to deserialize the JSON string into a ServerMessage
        let _result = serde_json::from_str::<ServerMessage>(json_str);
        // We don't care about the result, just that it doesn't panic
    }
    
    // Also try to deserialize directly from bytes (in case it's valid JSON bytes)
    let _result = serde_json::from_slice::<ServerMessage>(data);
    // We don't care about the result, just that it doesn't panic
});