#![no_main]

use libfuzzer_sys::fuzz_target;
use indidus_signal_protocol::session::SessionState;
use indidus_signal_protocol::crypto::keys::KeyPair;
use indidus_signal_protocol::protocol::MessageHeader;

fuzz_target!(|data: &[u8]| {
    // Create a minimal valid session state for fuzzing
    // This simulates a basic session that can receive messages
    let root_key = [0u8; 32]; // Use a fixed root key for reproducibility
    let dh_key = match KeyPair::generate() {
        Ok(key) => key,
        Err(_) => return, // Skip if key generation fails
    };
    
    let mut session_state = SessionState::new(
        root_key,
        dh_key.clone(),
        Some(dh_key.public_key()), // Use our own public key as remote for simplicity
        1000, // max_skip
    );
    
    // Set up minimal receiving chain key to enable decryption
    // In a real scenario, this would be established through proper key exchange
    let dummy_chain_key = [1u8; 32];
    session_state.receiving_chain_key = Some(dummy_chain_key);
    
    // Create a dummy message header for testing
    let header = MessageHeader {
        dh_public_key: dh_key.public_key(),
        previous_chain_length: 0,
        message_number: 0,
    };
    
    // Try to decrypt the fuzzed data
    // We don't care about the result, just that it doesn't panic
    let _result = session_state.decrypt(&header, data);
});