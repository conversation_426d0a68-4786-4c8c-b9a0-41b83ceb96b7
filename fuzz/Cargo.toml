[package]
name = "indidus_signal_protocol-fuzz"
version = "0.0.0"
publish = false
edition = "2021"

[package.metadata]
cargo-fuzz = true

[dependencies]
libfuzzer-sys = "0.4"
serde_json = "1.0"

[dependencies.indidus_signal_protocol]
path = "../indidus_signal_protocol"

[dependencies.indidus_e2ee_server]
path = "../indidus_e2ee_server"

[[bin]]
name = "fuzz_target_1"
path = "fuzz_targets/fuzz_target_1.rs"
test = false
doc = false
bench = false

[[bin]]
name = "server_message_parser"
path = "fuzz_targets/server_message_parser.rs"
test = false
doc = false
bench = false

[[bin]]
name = "session_decrypt"
path = "fuzz_targets/session_decrypt.rs"
test = false
doc = false
bench = false
