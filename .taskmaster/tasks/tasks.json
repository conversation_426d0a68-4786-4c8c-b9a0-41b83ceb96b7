{"master": {"tasks": [{"id": 1, "title": "Decide and Standardize Server Hand<PERSON>", "description": "Evaluate the generic `MessageHandler` abstraction versus framework-specific handlers. Based on the decision, refactor all server examples (`axum_integration`, `actix_integration`) to use the single, chosen pattern for consistency.", "status": "done", "dependencies": [], "priority": "high", "details": "The goal is to choose one consistent pattern for v0.1.0 to provide a clear integration path. The recommended approach is to favor the simpler, more direct framework-specific pattern (e.g., standard `async fn` handlers in Axum/Actix) over the generic `MessageHandler` trait. This avoids trait boilerplate and is easier for new developers to understand. The decision should be time-boxed. After deciding, refactor all examples in the `/examples` directory to conform to this single pattern.", "testStrategy": "Manually review all server-side examples to confirm they use the identical architectural pattern. Ensure all examples compile and run as described in their respective READMEs.", "subtasks": [{"id": 1, "title": "Evaluate and Decide on the Standard Server Handler Pattern", "description": "Formally evaluate the generic `MessageHandler` trait versus framework-specific `async fn` handlers within a time-boxed period. The outcome will be a definitive choice for the handler pattern to be used across all examples.", "dependencies": [], "details": "Analyze the pros and cons of each approach, focusing on developer experience, code simplicity, and maintainability. The recommended approach is to favor framework-specific handlers. The final decision should be documented to guide subsequent refactoring tasks.", "testStrategy": "The decision is validated by a documented conclusion shared with the team, confirming the chosen architectural pattern.", "status": "done"}, {"id": 2, "title": "Refactor `axum_integration` to Use the Standardized Handler Pattern", "description": "Update the `axum_integration` example to implement the server handler pattern chosen in the preceding decision task.", "dependencies": ["1.1"], "details": "Based on the decided pattern, remove the existing handler implementation and replace it. If the framework-specific pattern is chosen, this involves removing the `MessageHandler` trait and using a standard Axum `async fn` handler.", "testStrategy": "Ensure the `axum_integration` example compiles and runs without errors after the refactor. Manually test its functionality as described in its README.", "status": "done"}, {"id": 3, "title": "Refactor `actix_integration` to Use the Standardized Handler Pattern", "description": "Update the `actix_integration` example to align with the standardized server handler pattern.", "dependencies": ["1.1"], "details": "Modify the `actix_integration` example to use the chosen handler pattern. This will ensure its architecture is consistent with the `axum_integration` example and the project's overall design goals.", "testStrategy": "Ensure the `actix_integration` example compiles and runs without errors. This refactor is a prerequisite for Task 6, which involves fixing this example and its documentation.", "status": "done"}, {"id": 4, "title": "Verify Consistency Across All Refactored Server Examples", "description": "Perform a final manual review of all server-side examples (`axum_integration`, `actix_integration`) to confirm they have been successfully and consistently refactored to the chosen pattern.", "dependencies": ["1.2", "1.3"], "details": "Compare the handler implementations in both examples side-by-side to ensure they follow the identical architectural pattern. Confirm that both examples are fully functional and their behavior has not regressed.", "testStrategy": "Manually run both server examples and verify they operate as described in their respective README files. This confirms the standardization goal has been met without breaking functionality.", "status": "done"}]}, {"id": 2, "title": "Implement Strict `peer_id` Validation Utility", "description": "Create a shared utility to validate `peer_id` strings. This utility will enforce a strict format (e.g., regex `^[a-zA-Z0-9_-]{4,64}$`) and be used at all public API boundaries.", "status": "done", "dependencies": [], "priority": "high", "details": "To improve type safety and centralize validation, implement a `PeerId` struct using the newtype pattern: `pub struct PeerId(String);`. Create a `TryFrom<String>` or `new()` method that performs the validation using the `regex` crate (version `1.10.5`). Return a specific `InvalidPeerId` error on failure. The regex to use is `^[a-zA-Z0-9_-]{4,64}$`. This struct should be in a shared utility module accessible by both client and server crates.", "testStrategy": "Add a new unit test module for the `PeerId` type. Test valid inputs (min/max length, allowed characters) and invalid inputs (too short, too long, invalid characters, empty string) to ensure the validation logic is correct and returns the expected errors.", "subtasks": [{"id": 1, "title": "Setup Shared Module and Add Dependencies", "description": "Create a new shared module or locate an existing one that is accessible by both client and server crates. Add the `regex` crate (version `1.10.5`) to the corresponding `Cargo.toml` file.", "dependencies": [], "details": "This initial step prepares the project structure for the new shared utility. A common location would be a new `indus_shared::validation` module. This ensures the `PeerId` type can be used consistently across the workspace.", "testStrategy": "The project should compile successfully after the new module is created and the `regex` dependency is added.", "status": "done"}, {"id": 2, "title": "Define `PeerId` Struct and `InvalidPeerId` Error", "description": "In the shared module, define the `pub struct PeerId(String);` using the newtype pattern. Also, define a public `InvalidPeerId` error type that will be returned when validation fails.", "dependencies": ["2.1"], "details": "The `PeerId` struct will provide compile-time type safety for peer identifiers. The `InvalidPeerId` error will provide a clear and specific reason for validation failures, improving debuggability.", "testStrategy": "Code should compile without errors after the `PeerId` struct and `InvalidPeerId` error type are defined.", "status": "done"}, {"id": 3, "title": "Implement Validation Logic in `TryFrom<String>`", "description": "Implement the core validation logic for the `PeerId` struct. Use the `TryFrom<String>` trait to perform the conversion, validating the input against the regex `^[a-zA-Z0-9_-]{4,64}$`.", "dependencies": ["2.2"], "details": "The implementation should use the `regex` crate to check the input string. If the string is valid, it should return `Ok(PeerId(string))`. If it is invalid (due to characters, length, etc.), it should return `Err(InvalidPeerId)`. Using `OnceCell` or `lazy_static` for the compiled regex is recommended for performance.", "testStrategy": "The implementation will be validated by the unit tests in the subsequent task. The immediate goal is a correct and compiling implementation.", "status": "done"}, {"id": 4, "title": "Write Comprehensive Unit Tests for `PeerId`", "description": "Create a dedicated unit test module for the `PeerId` type. Write tests that cover all validation rules and edge cases.", "dependencies": ["2.3"], "details": "Test cases should include: valid `peer_id`s with minimum and maximum lengths, valid IDs with hyphens and underscores, and invalid IDs that are too short, too long, contain illegal characters (e.g., '@', '!', ' '), or are empty strings. Ensure that valid inputs produce an `Ok` result and all invalid inputs produce an `Err`.", "testStrategy": "Execute `cargo test` and confirm that all tests in the new `PeerId` test module pass.", "status": "done"}]}, {"id": 3, "title": "Apply `peer_id` Validation Across Public APIs", "description": "Integrate the new `PeerId` validation utility into all public functions and methods that accept a peer identifier, replacing `String` or `&str` types where appropriate.", "status": "done", "dependencies": [2], "priority": "high", "details": "Search the codebase for all function signatures that accept a `peer_id`. Refactor these functions to accept the `PeerId` struct created in the previous task. This will enforce validation at the type level. Key files to update include `indidus_e2ee_server/src/handlers/websocket.rs` and any client-side API that initiates communication.", "testStrategy": "Compile the entire workspace to ensure all function calls have been updated to use the new `PeerId` type. Review existing tests that use these APIs and update them to construct a valid `PeerId`. Verify that attempting to use an invalidly formatted ID string results in a compile-time or clear runtime error from the `PeerId` constructor.", "subtasks": [{"id": 1, "title": "Identify and Catalog All Public APIs Accepting `peer_id`", "description": "Perform a comprehensive search across the entire codebase to identify and list all public functions, methods, and handlers that currently accept a `peer_id` as a `String` or `&str`.", "dependencies": [], "details": "Create a checklist of all affected function signatures in `indidus_e2ee_server`, `indidus_e2ee_client`, and any shared utility crates. This audit will serve as the roadmap for the refactoring process.", "testStrategy": "The output of this task is a documented list of API endpoints that require modification. The task is complete when this list is reviewed and confirmed to be exhaustive.", "status": "done"}, {"id": 2, "title": "Refactor Server-Side APIs to Use `PeerId` Type", "description": "Update all identified server-side APIs, particularly in `indidus_e2ee_server/src/handlers/websocket.rs`, to accept the `PeerId` struct instead of `String` or `&str`.", "dependencies": ["3.1"], "details": "Modify the function signatures according to the checklist from the previous subtask. This change will enforce validation at the API boundary, preventing invalid `peer_id` formats from being processed by the server logic.", "testStrategy": "The server crate (`indidus_e2ee_server`) should compile successfully after the changes. Dependent crates are expected to fail compilation at this stage.", "status": "done"}, {"id": 3, "title": "Refactor Client-Side APIs to Use `PeerId` Type", "description": "Update all identified client-side public APIs that initiate communication or reference peers to accept the `PeerId` struct.", "dependencies": ["3.1"], "details": "Modify the relevant method signatures in the `indidus_e2ee_client` crate. This ensures that any client-side operation targeting a peer must first use a valid `PeerId`.", "testStrategy": "The client crate (`indidus_e2ee_client`) should compile successfully. Dependent examples and tests will likely fail to compile until they are updated.", "status": "done"}, {"id": 4, "title": "Update All Call Sites in Examples and Tests", "description": "Modify all the code that calls the refactored functions—specifically in the examples and test suites—to correctly construct and pass the `PeerId` type.", "dependencies": ["3.2", "3.3"], "details": "Update the `axum_integration` and `actix_integration` examples to create `PeerId` instances, including handling potential validation errors. Similarly, update all unit and integration tests to use `PeerId::try_from(\"...\").unwrap()` for valid IDs in test setups.", "testStrategy": "The entire workspace, including all examples and tests, must compile without errors. Run the full test suite using `cargo test --all` to ensure that all tests pass and the refactored APIs are being used correctly.", "status": "done"}]}, {"id": 4, "title": "Implement `EncryptedMessageFrame` Struct", "description": "Define and implement the `EncryptedMessageFrame` struct in `indidus_e2ee_client` to formally encapsulate the on-the-wire data format for encrypted messages, including serialization and deserialization logic.", "status": "done", "dependencies": [], "priority": "high", "details": "Create a new struct that formally defines the network payload. Use the `serde` crate (version `1.0.203`) for serialization. For a compact binary format, use `bincode` (version `2.0.0-rc.3`).\n\n```rust\n// in indus_e2ee_client/src/client/messaging.rs or a new framing.rs module\nuse serde::{Serialize, Deserialize};\n\n#[derive(Serialize, Deserialize, Debug, PartialEq)]\npub struct EncryptedMessageFrame {\n    // Example fields, adjust based on actual protocol needs\n    pub recipient_key: Vec<u8>,\n    pub ephemeral_key: Vec<u8>,\n    pub ciphertext: Vec<u8>,\n    pub nonce: Vec<u8>,\n}\n\nimpl EncryptedMessageFrame {\n    pub fn serialize(&self) -> Result<Vec<u8>, bincode::Error> {\n        bincode::serialize(self)\n    }\n\n    pub fn deserialize(bytes: &[u8]) -> Result<Self, bincode::Error> {\n        bincode::deserialize(bytes)\n    }\n}\n```", "testStrategy": "Add unit tests for the `EncryptedMessageFrame`. Test the `serialize` and `deserialize` methods to ensure that a serialized frame can be successfully deserialized back into its original form (round-trip test).", "subtasks": [{"id": 1, "title": "Add `serde` and `bincode` Dependencies", "description": "Add the required `serde` (version `1.0.203`) and `bincode` (version `2.0.0-rc.3`) crates to the `Cargo.toml` file of the `indidus_e2ee_client` crate.", "dependencies": [], "details": "This step is a prerequisite for using the serialization and deserialization macros and functions. Ensure the `serde` feature `derive` is enabled.", "testStrategy": "The project should successfully compile after the dependencies are added by running `cargo check -p indidus_e2ee_client`.", "status": "done"}, {"id": 2, "title": "Create the `EncryptedMessageFrame` Struct Definition", "description": "Create a new module, e.g., `indidus_e2ee_client/src/framing.rs`, and define the `EncryptedMessageFrame` struct with the necessary fields and derive macros.", "dependencies": ["4.1"], "details": "The struct should derive `Serialize`, `Deserialize`, `Debug`, and `PartialEq`. It will contain fields such as `recipient_key`, `ephemeral_key`, `ciphertext`, and `nonce`, all of type `Vec<u8>`.", "testStrategy": "The code should compile after the struct is defined and the new module is correctly linked in `lib.rs` or `main.rs`.", "status": "done"}, {"id": 3, "title": "Implement `serialize` and `deserialize` Methods", "description": "Implement the `serialize` and `deserialize` methods for the `EncryptedMessageFrame` struct using the `bincode` library.", "dependencies": ["4.2"], "details": "Create an `impl` block for the struct. The `serialize` method will take `&self` and return `Result<Vec<u8>, bincode::Error>`, while `deserialize` will take a byte slice `&[u8]` and return `Result<Self, bincode::Error>`.", "testStrategy": "The implementation is considered complete when the code compiles. The logic will be validated in the next subtask.", "status": "done"}, {"id": 4, "title": "Write Unit Tests for Round-Trip Serialization", "description": "Add a new unit test module in the same file to verify that the serialization and deserialization logic works correctly.", "dependencies": ["4.3"], "details": "The primary test case is a 'round-trip' test: create an instance of `EncryptedMessageFrame`, serialize it into bytes, then deserialize those bytes back into a new instance. Assert that the original and deserialized instances are equal.", "testStrategy": "Run `cargo test -p indidus_e2ee_client` and ensure the new test for `EncryptedMessageFrame` passes successfully.", "status": "done"}]}, {"id": 5, "title": "Refactor Messaging Logic to Use `EncryptedMessageFrame`", "description": "Update all client-side encryption and decryption logic to use the new `EncryptedMessageFrame` struct, completely removing any manual byte manipulation or magic numbers.", "status": "pending", "dependencies": [4], "priority": "high", "details": "Locate the existing message handling logic, likely in `indidus_e2ee_client/src/client/messaging.rs`. Replace code that manually concatenates or splits byte arrays with calls to `EncryptedMessageFrame::serialize()` before sending and `EncryptedMessageFrame::deserialize()` upon receiving data. This ensures the data format is consistent and robust.", "testStrategy": "Run existing integration tests that involve sending and receiving messages. They should continue to pass after the refactor. If test coverage is insufficient, add a new integration test specifically for sending a message from a client, and verifying the server (or another client) can receive and correctly deserialize the `EncryptedMessageFrame`.", "subtasks": [{"id": 1, "title": "Locate and Audit Existing Message Handling Logic", "description": "Thoroughly review the `indidus_e2ee_client/src/client/messaging.rs` file and any related modules to identify the exact code paths responsible for serializing data before sending and deserializing data upon receipt.", "dependencies": [], "details": "This initial investigation involves pinpointing the functions that perform manual byte array concatenation or splitting. The goal is to create a clear list of all the code that needs to be replaced.", "testStrategy": "The task is complete when a checklist of functions and code blocks for refactoring is created and verified."}, {"id": 2, "title": "Refactor Data Transmission Logic", "description": "Update the client-side logic that prepares and sends encrypted messages. Replace any manual byte concatenation with the creation of an `EncryptedMessageFrame` struct and a call to its `serialize()` method.", "dependencies": ["5.1"], "details": "The components of the message (e.g., recipient key, ephemeral key, ciphertext, nonce) will now be used to populate an `EncryptedMessageFrame` instance. The result of `frame.serialize()` will be the `Vec<u8>` that is sent over the network.", "testStrategy": "The code must compile successfully after the changes. The logic will be validated by integration tests in a later step."}, {"id": 3, "title": "Refactor Data Reception Logic", "description": "Update the client-side logic that handles incoming encrypted messages. Replace any manual byte parsing or splitting with a single call to `EncryptedMessageFrame::deserialize()`.", "dependencies": ["5.1"], "details": "The raw `&[u8]` received from the network will be passed to `EncryptedMessageFrame::deserialize()`. The resulting struct will provide safe and direct access to the message components needed for decryption.", "testStrategy": "The code must compile successfully after the changes. This refactor is the counterpart to the transmission logic change."}, {"id": 4, "title": "Verify Refactor with Existing Integration Tests", "description": "Run the entire suite of existing integration tests that involve sending and receiving messages to ensure that the refactoring has not introduced any regressions.", "dependencies": ["5.2", "5.3"], "details": "The core goal is to confirm that the new serialization/deserialization mechanism is a drop-in replacement for the old manual logic. All tests related to messaging must pass without any modifications to the test logic itself.", "testStrategy": "Execute `cargo test` for the entire workspace. All existing tests, especially those for messaging, must pass."}]}, {"id": 6, "title": "Fix `actix_integration` Example and Sync Documentation", "description": "Repair the `actix_integration` example to ensure it is fully functional and update its `README.md` to be consistent with the standardized server handler pattern and latest API.", "status": "pending", "dependencies": [1, 3], "priority": "high", "details": "First, apply the standardized server handler pattern chosen in task #1 to the `actix_integration` example. Ensure it compiles and runs correctly. Then, review the `README.md` within the example's directory. Update all code snippets, setup instructions, and explanations to be 100% accurate and reflect the final, refactored code.", "testStrategy": "Manually follow the instructions in the `actix_integration` example's `README.md` from start to finish. The example must compile and run without errors. The behavior observed must match the behavior described in the documentation.", "subtasks": [{"id": 1, "title": "Apply Standardized Hand<PERSON> Pattern to `actix_integration`", "description": "Refactor the core logic of the `actix_integration` example to use the standardized server handler pattern decided in Task #1. This includes updating dependencies and function signatures to align with recent API changes from Task #3.", "dependencies": [], "details": "This is the primary code modification step. The goal is to make the example compile and adhere to the new architectural standard. All previous handler logic will be removed and replaced.", "testStrategy": "The subtask is complete when `cargo check --example actix_integration` runs without any errors."}, {"id": 2, "title": "Verify Functional Correctness of the Refactored Example", "description": "Compile and run the refactored `actix_integration` example to ensure it is fully functional and operates as expected before any documentation is updated.", "dependencies": ["6.1"], "details": "This involves running the server and client components of the example and performing a basic interaction to confirm that the core functionality has not regressed after the refactoring.", "testStrategy": "Manually run the example and verify its basic operations. The example must run without panics or runtime errors."}, {"id": 3, "title": "Update Text and Instructions in `README.md`", "description": "Perform a full review of the `actix_integration/README.md` file. Update all non-code text, including the introduction, setup instructions, and explanations, to be consistent with the refactored example.", "dependencies": ["6.2"], "details": "Check for outdated commands, incorrect descriptions, or any instructions that are no longer valid. The goal is to ensure the user guide is 100% accurate.", "testStrategy": "Read the `README.md` from top to bottom and compare its instructions against the known-working state of the refactored example."}, {"id": 4, "title": "Synchronize All Code Snippets in `README.md`", "description": "Replace every code snippet in `actix_integration/README.md` with fresh, verified code from the now-functional example. Ensure all snippets are correct and relevant.", "dependencies": ["6.2"], "details": "This step is critical for documentation accuracy. Copy and paste code directly from the source files into the README to prevent typos or outdated examples.", "testStrategy": "As a verification step, copy each code snippet from the updated README into a temporary file and ensure it compiles."}, {"id": 5, "title": "Perform End-to-End Validation from `README.md`", "description": "Manually follow the updated `actix_integration/README.md` from start to finish as if you were a new user. The example must compile, run, and behave exactly as the documentation describes.", "dependencies": ["6.3", "6.4"], "details": "This is the final acceptance test. It validates that the code and its documentation are perfectly synchronized and provide a seamless developer experience.", "testStrategy": "Execute the steps in the README in a clean environment. The task is successful only if the example runs without error and the observed behavior matches the described behavior."}]}, {"id": 7, "title": "Document `zeroize` Limitation on `SigningKeyPair`", "description": "Add a clear and prominent documentation comment to the `SigningKeyPair` struct explaining the current limitation regarding the `Zeroize` trait not being implemented on its dependencies.", "status": "done", "dependencies": [], "priority": "high", "details": "In the source file where `SigningKeyPair` is defined, add a rustdoc comment block. Explicitly state that while the keypair's own memory is zeroized on drop, underlying cryptographic types from `ed25519-dalek` may not be, and link to the relevant issue or planned upstream contribution. This is critical for security transparency.\n\n```rust\n/// Represents a key pair for signing.\n/// \n/// # Security Warning\n/// The secrets in this struct are zeroized on drop. However, as of ed25519-dalek v2.1,\n/// the underlying `SigningKey` does not implement the `Zeroize` trait. We plan to\n/// contribute this feature upstream. Until then, some key material may remain in memory\n/// longer than intended.\npub struct SigningKeyPair { ... }\n```", "testStrategy": "Run `cargo doc --open` and visually inspect the generated documentation for the `SigningKeyPair` struct. Confirm that the security warning is present, clear, and accurately describes the limitation.", "subtasks": [{"id": 1, "title": "Locate Struct and Research Upstream Issue", "description": "Identify the exact source file where the `SigningKeyPair` struct is defined. Simultaneously, research the `ed25519-dalek` crate's repository to find the specific version number and a link to any existing issue or discussion about the lack of `Zeroize` trait implementation on its key types.", "dependencies": [], "details": "The goal is to gather all necessary information before writing the documentation. A concrete link to an upstream issue provides transparency and credibility to the security warning.", "testStrategy": "The task is complete when the file path for `SigningKeyPair` is confirmed and a URL to the relevant upstream issue (or discussion) is found and documented.", "status": "done"}, {"id": 2, "title": "Draft and Add Documentation Comment", "description": "Compose the rustdoc comment with a prominent `# Security Warning` section. Add this comment directly above the `SigningKeyPair` struct definition in the located source file.", "dependencies": ["7.1"], "details": "The comment must clearly state the limitation: while the wrapper struct is zeroized, the underlying `ed25519-dalek` keys are not. It should include the version of `ed25519-dalek` and the link to the upstream issue found in the previous step.", "testStrategy": "The code containing the new comment must compile successfully using `cargo check`.", "status": "done"}, {"id": 3, "title": "Generate and Visually Verify Documentation", "description": "Run the `cargo doc --open` command to generate the project's documentation and open it in a browser. Navigate to the page for the `SigningKeyPair` struct.", "dependencies": ["7.2"], "details": "This is the final verification step. The goal is to ensure the documentation renders correctly and the security warning is prominent, clear, and provides all the necessary context for a developer to understand the security implications.", "testStrategy": "Visually inspect the generated HTML documentation. The security warning must be present, correctly formatted, and all information (including the link to the upstream issue) must be accurate.", "status": "done"}]}, {"id": 8, "title": "Audit and Synchronize All `README.md` Files and Crate-Level Docs", "description": "Perform a full audit of all `README.md` files across the workspace and crate-level documentation to ensure they are 100% accurate and consistent with the finalized v0.1.0 API.", "status": "pending", "dependencies": [5, 6, 7], "priority": "high", "details": "This task is the final step in solidifying the developer experience before release. Review the root `README.md`, `indidus_e2ee_client/README.md`, `indidus_e2ee_server/README.md`, and all example READMEs. Check for outdated code snippets, incorrect function names, and inconsistent terminology. Ensure CI badges for build status and coverage are present.", "testStrategy": "Manually review each `README.md` file. Copy and paste every code snippet into a temporary local project or example file and ensure it compiles and runs. This guarantees that all documented code is valid against the final API.", "subtasks": [{"id": 1, "title": "Create Master Checklist of Documentation Files", "description": "Compile a definitive checklist of all documentation files that require auditing. This includes the root README, all crate-level READMEs, and every example's README.", "dependencies": [], "details": "The checklist should explicitly list: `README.md`, `indidus_e2ee_client/README.md`, `indidus_e2ee_server/README.md`, `examples/axum_integration/README.md`, `examples/actix_integration/README.md`, and the crate-level doc comments in each `lib.rs`.", "testStrategy": "The checklist is considered complete once it has been created and verified against the current project structure."}, {"id": 2, "title": "Audit and Update Textual Content and Terminology", "description": "Perform a review of all documents in the master checklist. Standardize terminology (e.g., consistently using `PeerId`), update high-level descriptions, and correct any outdated setup or usage instructions.", "dependencies": ["8.1"], "details": "This pass focuses on the non-code content. The goal is to ensure the narrative and explanations are perfectly aligned with the finalized v0.1.0 API and its concepts.", "testStrategy": "Manually read each document and cross-reference its claims with the current state of the codebase. A peer review of the textual changes is recommended."}, {"id": 3, "title": "Validate, Fix, and Synchronize All Code Snippets", "description": "Systematically verify every code snippet across all `README.md` files. Each snippet must be tested to ensure it compiles and runs correctly against the final API.", "dependencies": ["8.1"], "details": "For each snippet, copy it into a temporary local project or an appropriate example file to test it. Update any incorrect function names, types (e.g., `String` to `PeerId`), or logic. This is the most critical step for documentation accuracy.", "testStrategy": "Each code snippet is individually copied, compiled, and run. The subtask is complete only when every single snippet has been validated."}, {"id": 4, "title": "Review and Update Crate-Level Documentation", "description": "Audit the crate-level documentation comments (`//!`) in the `lib.rs` file of each crate to ensure the main examples and descriptions are accurate.", "dependencies": ["8.1"], "details": "This documentation is what appears on the crate's landing page on docs.rs. Ensure the main examples here are correct and reflect the intended primary usage patterns of the library.", "testStrategy": "Run `cargo doc --open` and review the generated documentation for each crate's main page. Verify the content and test the code examples presented."}, {"id": 5, "title": "Verify CI Badges and Final Polish", "description": "Check the root and crate-level `README.md` files to ensure CI badges for build status and test coverage are present, correctly configured, and pointing to the right workflow.", "dependencies": ["8.2", "8.3"], "details": "This is a final quality check to ensure project health indicators are visible and accurate for new developers. Also, perform a final read-through for any typos or formatting errors.", "testStrategy": "Click on each CI badge in the rendered markdown files to ensure they link to the correct and up-to-date status page."}]}, {"id": 9, "title": "Add Tests for Critical Error-Handling Paths", "description": "Increase test coverage by adding specific unit tests for all critical error-handling paths in `indidus_e2ee_client::error`, `config.rs`, and `state.rs`.", "status": "pending", "dependencies": [8], "priority": "medium", "details": "Focus on triggering and verifying every error variant defined in the specified modules. For `config.rs`, test invalid configuration values. For `state.rs`, test illegal state transitions. For `error.rs`, ensure that functions that are supposed to produce specific errors do so under the right conditions. This is the first priority for reaching the 75% coverage goal.", "testStrategy": "Run a coverage tool like `cargo-tarpaulin` before and after implementing the new tests. The coverage percentage for the targeted modules (`error.rs`, `config.rs`, `state.rs`) should increase significantly. Ensure all new tests pass using `cargo test`.", "subtasks": [{"id": 1, "title": "Implement Unit Tests for `config.rs` Error Paths", "description": "Create a new test module in `config.rs` and add unit tests that specifically trigger and verify error conditions, such as providing invalid configuration values.", "dependencies": [], "details": "Focus on functions that parse or validate configuration data. Test cases should include empty strings for required fields, malformed URLs, and any other invalid inputs that should result in a `Result::Err`.", "testStrategy": "Run `cargo test` focusing on the new config tests. Use a coverage tool like `cargo-tarpaulin` to measure the specific coverage increase for the `config.rs` file."}, {"id": 2, "title": "Implement Unit Tests for `state.rs` Illegal Transitions", "description": "Add unit tests to the `state.rs` module that verify the state machine correctly rejects illegal state transitions.", "dependencies": [], "details": "For each state, write tests that attempt to call methods that are only valid in other states. For example, if the state is `Disconnected`, test that a `send_message` call fails with an appropriate error. Assert that the correct error variant is returned.", "testStrategy": "Run the new tests to ensure they pass and correctly catch the illegal state transition errors. Measure the coverage increase for `state.rs` using `cargo-tarpaulin`."}, {"id": 3, "title": "Implement Unit Tests for `error.rs` Variants and Conversions", "description": "Add unit tests for the `error.rs` module to ensure all error variants can be constructed and that any `From` trait implementations work as expected.", "dependencies": [], "details": "This involves testing that errors from dependent crates (e.g., `serde_json::Error`, `std::io::Error`) are correctly converted into the application's custom error enum variants. This validates the error-handling chain.", "testStrategy": "Create instances of external error types and manually convert them using the `?` operator or `Into::into`, then assert that the resulting custom error has the correct variant. Confirm these tests pass and increase coverage for `error.rs`."}, {"id": 4, "title": "Verify Overall Test Success and Final Coverage", "description": "Run the entire test suite for the `indidus_e2ee_client` crate and generate a final coverage report to ensure all new tests pass and the coverage for the targeted modules has significantly increased.", "dependencies": ["9.1", "9.2", "9.3"], "details": "This final step confirms that the new error-handling tests are integrated correctly and haven't caused regressions. The coverage report will provide the data needed to confirm progress towards the 75% goal.", "testStrategy": "Execute `cargo test -p indidus_e2ee_client` to ensure all tests pass. Then run `cargo-tarpaulin -p indidus_e2ee_client` and verify the coverage percentage for `config.rs`, `state.rs`, and `error.rs` has met expectations."}]}, {"id": 10, "title": "Write Integration Tests for Multi-Client File Transfers", "description": "Implement comprehensive integration tests in `tests/integration.rs` for file transfers between multiple clients, including failure scenarios like simulated network issues and out-of-order data chunk delivery.", "status": "pending", "dependencies": [8], "priority": "medium", "details": "In the main `tests/integration.rs` file, create test scenarios that involve at least two clients and a server. Test the happy path of a complete file transfer. Then, add tests that simulate failures: a client disconnecting mid-transfer, data chunks arriving out of order, or corrupted data chunks. The library should handle these cases gracefully.", "testStrategy": "Execute the test suite using `cargo test --test integration`. All tests, including the new failure-simulation tests, must pass. This will validate the robustness of the file transfer state machine and protocol handling.", "subtasks": [{"id": 1, "title": "Establish Multi-Client Test Scaffolding", "description": "In `tests/integration.rs`, create the necessary helper functions to set up a test environment consisting of one server and at least two connected clients. This will serve as the foundation for all subsequent file transfer tests.", "dependencies": [], "details": "The setup helpers should handle server startup, client initialization, and the websocket connection/handshake process, returning initialized client and server instances that are ready for interaction.", "testStrategy": "This task is verified by creating a simple test that uses the helpers to set up the environment and then cleanly tears it down without any panics or errors."}, {"id": 2, "title": "Implement 'Happy Path' Test for a Complete File Transfer", "description": "Write an integration test that verifies a successful file transfer between two clients through the server.", "dependencies": ["10.1"], "details": "The test will involve: 1) Client A initiates a file transfer to Client B. 2) Client A sends all data chunks. 3) Client B receives all data chunks and reconstructs the file. 4) The test asserts that the received file is identical to the original file.", "testStrategy": "Run the new test and verify that it passes. The hash of the sent file must match the hash of the received file."}, {"id": 3, "title": "Implement Failure Test: Sender Disconnects Mid-Transfer", "description": "Create an integration test to simulate a scenario where the sending client disconnects from the server in the middle of a file transfer.", "dependencies": ["10.1"], "details": "The test should initiate a file transfer, send some but not all of the data chunks, and then terminate the sending client's connection. The test must assert that the receiving client handles this gracefully, for example by timing out and cleaning up any partially received data.", "testStrategy": "Verify that the receiving client's transfer state is correctly terminated and that it does not wait indefinitely. The test should pass without deadlocking or panicking."}, {"id": 4, "title": "Implement Failure Test: Corrupted Data Chunk", "description": "Write an integration test where a data chunk is intentionally corrupted before being delivered to the receiving client.", "dependencies": ["10.1"], "details": "This test will intercept a valid data chunk during a transfer and alter its contents (e.g., flip a bit). The test must verify that the receiving client detects the corruption, rejects the chunk, and aborts the file transfer process cleanly.", "testStrategy": "The test should assert that the file transfer fails with a specific error related to data integrity and that no incomplete file is saved on the receiver's end."}, {"id": 5, "title": "Implement Failure Test: Out-of-Order Data Chunk Delivery", "description": "Create an integration test that simulates network conditions causing data chunks to arrive at the receiver in a non-sequential order.", "dependencies": ["10.1"], "details": "During a file transfer, deliberately send the data chunks in a shuffled order (e.g., chunk #1, #3, #2). The test must verify that the library's protocol can either reassemble the chunks correctly or, if not designed for reordering, fail the transfer gracefully.", "testStrategy": "If reassembly is supported, the final file must be verified as correct. If not, the test must assert that the transfer is aborted with an appropriate error indicating an out-of-order chunk was received."}]}, {"id": 11, "title": "Add Unit Tests for Remaining Uncovered Logic", "description": "Analyze the test coverage report and add unit tests for remaining logic paths across all crates to help reach the 75% total coverage target.", "status": "pending", "dependencies": [9, 10], "priority": "low", "details": "Use `cargo-tarpaulin` to generate an HTML coverage report (`cargo tarpaulin --out Html`). Analyze the report to identify modules and functions with low coverage. Write targeted unit tests for simple utility functions, getter methods, and other straightforward logic that was previously untested. This is a broad effort to increase the overall number.", "testStrategy": "Run `cargo tarpaulin` and check that the overall coverage percentage is approaching or has exceeded 75%. Ensure all new and existing tests pass with `cargo test`.", "subtasks": [{"id": 1, "title": "Generate Baseline Coverage Report and Identify Targets", "description": "Execute `cargo tarpaulin` across the entire workspace to generate a detailed HTML coverage report. Analyze this report to create a prioritized checklist of modules and functions with low test coverage.", "dependencies": [], "details": "The command `cargo tarpaulin --workspace --out Html` should be used. The analysis should focus on identifying simple, uncovered logic like utility functions, getters, and constructors that provide an easy way to increase the coverage score.", "testStrategy": "The task is complete when the HTML report is generated and a documented checklist of specific functions to be tested is created."}, {"id": 2, "title": "Implement Unit Tests for `indidus_e2ee_client`", "description": "Based on the checklist, add new unit tests to the `indidus_e2ee_client` crate, targeting the identified low-coverage functions and logic paths.", "dependencies": ["11.1"], "details": "This involves creating new `#[test]` functions that instantiate structs, call the targeted methods with various inputs, and assert the expected outcomes. The focus is on covering lines of code that are currently missed by the test suite.", "testStrategy": "Run `cargo test -p indidus_e2ee_client` to ensure all new and existing tests for the client crate pass successfully."}, {"id": 3, "title": "Implement Unit Tests for `indidus_e2ee_server`", "description": "Following the coverage report analysis, add new unit tests to the `indidus_e2ee_server` crate for its simple, untested logic.", "dependencies": ["11.1"], "details": "Similar to the client crate, this involves writing targeted tests for helper functions, configuration handlers, or any other straightforward logic that was identified as having low or zero coverage.", "testStrategy": "Run `cargo test -p indidus_e2ee_server` to verify that all new and existing server-side tests pass."}, {"id": 4, "title": "Implement Unit Tests for Shared Utility Crates", "description": "Address any remaining low-coverage areas in shared utility modules or crates as identified by the coverage report.", "dependencies": ["11.1"], "details": "This task ensures that common code used by both the client and server is also well-tested, contributing to the overall stability and coverage of the project.", "testStrategy": "Run `cargo test` for the relevant shared crates or run the full workspace test suite to ensure no regressions have been introduced."}, {"id": 5, "title": "Generate Final Coverage Report and Verify Goal", "description": "After adding all new unit tests, run the full test suite for the workspace one last time. Then, generate a final `cargo tarpaulin` report to measure the new total coverage percentage.", "dependencies": ["11.2", "11.3", "11.4"], "details": "This subtask serves as the final validation for the entire effort. The goal is to confirm that the total test coverage has approached or exceeded the 75% target and that all tests are passing.", "testStrategy": "Execute `cargo test --workspace`. Upon success, run `cargo tarpaulin --workspace` and check that the final reported coverage percentage meets the project's goal."}]}, {"id": 12, "title": "Update CI Pipeline to Enforce 75% Test Coverage", "description": "Modify the `.github/workflows/ci.yml` file to set the minimum test coverage threshold to 75.0%, making it a mandatory quality gate for all future pull requests.", "status": "pending", "dependencies": [11], "priority": "high", "details": "Use `cargo-tarpaulin` for coverage checking in the CI workflow. Update the relevant step in `ci.yml` to include the `--fail-under 75.0` flag. This will cause the CI build to fail if a pull request causes the project's test coverage to drop below 75%.\n\nExample YAML snippet:\n```yaml\n- name: Run tests and check coverage\n  run: cargo tarpaulin --verbose --workspace --timeout 120 --out Xml --fail-under 75.0\n```", "testStrategy": "After committing the change to `ci.yml`, push it to a branch and open a pull request. The CI build for that pull request must run and pass, specifically the coverage check step. To be certain, temporarily set the threshold to a value higher than the current coverage to watch it fail, then revert to 75.0 to see it pass.", "subtasks": [{"id": 1, "title": "Modify `ci.yml` to Add `--fail-under` Flag", "description": "Locate the `cargo tarpaulin` execution step in the `.github/workflows/ci.yml` workflow file and append the `--fail-under 75.0` argument to the command.", "dependencies": [], "details": "This change is the core implementation of the task. The final command in the YAML file should resemble the example provided, enforcing a strict 75% minimum test coverage.", "testStrategy": "The correctness of the YAML syntax and the flag's functionality will be validated by a live CI run in the subsequent steps."}, {"id": 2, "title": "Create a Pull Request to <PERSON><PERSON> and Test the CI Workflow", "description": "Commit the modified `ci.yml` file to a new feature branch and open a pull request. This action will trigger the CI pipeline and allow for validation of the new coverage check.", "dependencies": ["12.1"], "details": "The pull request serves as the test environment for the CI change. As per the test strategy, one could optionally push a commit with an intentionally high threshold (e.g., 99%) first to watch the build fail, providing confidence that the gate is active.", "testStrategy": "The CI build must be automatically triggered by the creation of the pull request."}, {"id": 3, "title": "Confirm the CI Pipeline Passes with the 75% Threshold", "description": "Monitor the CI build for the pull request. Verify that all jobs, especially the test coverage step, complete successfully with the `fail-under` flag set to 75.0.", "dependencies": ["12.2"], "details": "This is the final acceptance test. A successful run confirms that the project's current test coverage is above the new minimum threshold and that the workflow is configured correctly. The CI logs for the coverage step should be reviewed to ensure the check was performed.", "testStrategy": "The pull request must show a 'successful' status for the CI check before it can be merged."}, {"id": 4, "title": "Merge the Pull Request to Enforce the Policy", "description": "After the CI pipeline has passed successfully, merge the pull request. This will apply the mandatory 75% test coverage quality gate to the main branch for all future pull requests.", "dependencies": ["12.3"], "details": "Merging the change concludes the task and formally establishes the new, stricter quality standard for the codebase.", "testStrategy": "After this task is complete, any new pull request that causes the project's test coverage to drop below 75% should automatically fail its CI build."}]}], "metadata": {"created": "2025-08-17T18:23:48.180Z", "updated": "2025-08-17T19:57:15.010Z", "description": "Tasks for master context"}}}