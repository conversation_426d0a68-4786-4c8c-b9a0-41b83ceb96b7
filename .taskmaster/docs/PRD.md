<context>
# Overview  
**Product:** Indidus E2EE Library - v0.1.0 Release Readiness

**Problem:** The `indidus_e2ee` library has been developed to a high standard but has not yet been published. Before its inaugural public release, it must undergo a final hardening phase to ensure its architecture is consistent, its APIs are safe and ergonomic, and its quality is demonstrably high. A strong `v0.1.0` release is critical for establishing credibility and attracting adoption within the security-conscious Rust ecosystem.

**Solution:** This initiative outlines the final engineering tasks required to prepare the `indidus_e2ee` workspace for its first public release. We will focus on solidifying the API, standardizing architectural patterns, increasing test coverage to a respectable baseline, and ensuring all developer-facing materials (documentation, examples) are flawless. This work will deliver a polished, trustworthy, and enterprise-ready E2EE library from day one.

**For:**
*   **Potential Adopters & the Rust Community:** To provide them with a high-quality, secure, and easy-to-use library that they can trust for their own projects.
*   **Internal Engineering & Security Teams:** To finalize a stable, maintainable foundation for all future product development.
*   **Future Open-Source Contributors:** To establish clear patterns and a high quality bar for all subsequent contributions.

# Core Features  
The "features" for this pre-launch initiative are engineering-centric improvements aimed at finalizing the initial product offering.

- **Feature: Establish Verifiable Quality & Security**
  - **What it does:** Implements a stringent, automated quality gate for test coverage and formally tracks the library's security posture.
  - **Why it's important:** Demonstrable quality is a key selling point for a security library. Launching with a high, enforced test coverage provides immediate proof of the library's robustness and the project's commitment to quality.
  - **How it works:** We will raise the minimum test coverage threshold in the CI pipeline from its current 35% to a release-ready target of **75%**, with a documented plan to reach 85% post-launch.

- **Feature: Finalize API & Architectural Patterns**
  - **What it does:** Refactors internal data formats and server-side abstractions to be explicit, consistent, and less prone to misuse before the API is stabilized for the `v0.1.0` release.
  - **Why it's important:** We only get one chance to make a first impression with our API. Finalizing these patterns now prevents the need for disruptive breaking changes immediately after launch and ensures a more maintainable long-term architecture.
  - **How it works:** We will introduce dedicated structs for network payloads, implement stricter input validation, and unify the server-side handler abstractions to present a single, clear integration pattern.

- **Feature: Guarantee an Exceptional Developer Experience**
  - **What it does:** Ensures all documentation, examples, and code comments are perfectly synchronized with the final implementation.
  - **Why it's important:** The out-of-the-box experience for a new developer is critical for adoption. Working, accurate examples and clear documentation are the most effective tools for enabling developers to succeed with our library.
  - **How it works:** A full audit of all `README.md` files and code documentation will be performed to correct inconsistencies and align them with the finalized API patterns.

# User Experience  
The "user" of this library is a software engineer. The user journey involves discovery, integration, and debugging.

- **User Personas:**
  - **Application Developer:** Needs to integrate E2EE features quickly and safely. They will judge the library based on its documentation, the clarity of its examples, and its API ergonomics.
  - **Security Engineer:** Needs to audit the library for security vulnerabilities. They will look for a transparent security policy, high test coverage, and clear, robust cryptographic implementations.

- **Key User Flows:**
  1.  **First Impression:** A developer discovers the new `indidus_e2ee` crate on `crates.io`. They see the CI badges showing "passing" security scans and a high test coverage percentage, which builds immediate trust.
  2.  **Seamless Integration:** The developer follows the `axum_integration` example. The code is clean, up-to-date, and works exactly as the `README.md` describes.
  3.  **Safe Implementation:** The developer uses the client APIs. When they provide an invalid `peer_id`, the library returns a compile-time or runtime error with a clear, helpful message, guiding them to a correct implementation.

- **UI/UX Considerations (Developer Experience):**
  - **Clarity is paramount:** The API should feel intuitive.
  - **Consistency is key:** All examples and modules must follow the same architectural patterns.
  - **Errors are a feature:** Errors must be descriptive and help the developer fix their code.
</context>
<PRD>
# Technical Architecture  
The planned changes will finalize the technical architecture for the v0.1.0 release.

- **System Components:**
  - **CI Pipeline:** The `ci.yml` will be modified to set the `min_coverage` threshold to **75%**.
  - **Server Handlers (`indidus_e2ee_server`):** A decision will be made to either adopt the generic `MessageHandler` across all examples or remove it to favor a single, consistent, framework-specific pattern.
  - **Client Messaging (`indidus_e2ee_client`):** A new `EncryptedMessageFrame` struct will be introduced to encapsulate the on-the-wire format, replacing manual byte manipulation.

- **Data Models:**
  - **`EncryptedMessageFrame` (New Struct):** This struct will formally define the network payload, with `serialize` and `deserialize` methods.
  - **`peer_id` (Validation):** A shared utility function will be created to enforce a strict format (e.g., regex `^[a-zA-Z0-9_-]{4,64}$`) for `peer_id` strings.

# Development Roadmap  
This roadmap outlines the complete scope of work required for the `v0.1.0` release. These are not sequential phases but a prioritized list of work packages.

### Tier 1: Foundational Hardening & Consistency (Blockers for Release)
*Goal: Address the highest-impact issues related to security, API consistency, and developer trust.*

1.  **Standardize Server Handler Logic:**
    *   **Decision:** Evaluate the generic `MessageHandler` abstraction.
    *   **Execution:** Either refactor all server examples to use it, or remove it and standardize on the framework-specific pattern. This ensures a single, clear "right way" to integrate the server.
2.  **Stricter Input Validation:**
    *   Define and implement a strict validation format for `peer_id` strings.
    *   Apply this validation at all public API boundaries where `peer_id` is accepted.
3.  **Encapsulate Encrypted Payload Format:**
    *   Implement the `EncryptedMessageFrame` struct in the client SDK.
    *   Refactor all message encryption and decryption logic to use this struct, eliminating magic numbers and manual byte manipulation.
4.  **Documentation & Example Synchronization:**
    *   Audit and update ALL `README.md` files to be 100% accurate.
    *   Fix the `actix_integration` example and its documentation.
    *   Document the `zeroize` limitation on `SigningKeyPair` clearly in the struct's documentation.

### Tier 2: Verifiable Quality Assurance (Required for Release)
*Goal: Achieve a high level of test coverage to ensure the library is robust and reliable.*

1.  **Achieve 75% Test Coverage:**
    *   This is a single, large work package that will be broken down into smaller tasks by module.
    *   **Priority 1:** Add tests to cover all critical error-handling paths in `indidus_e2ee_client::error`, `config.rs`, and `state.rs`.
    *   **Priority 2:** Write comprehensive integration tests in `tests/integration.rs` for multi-client file transfers, simulating failures and out-of-order chunk delivery.
    *   **Priority 3:** Add unit tests for remaining uncovered logic paths across all crates.
    *   **Final Step:** Update the `min_coverage` value in `ci.yml` to `75.0`.

### Tier 3: Post-Launch Goals (Not in Scope for v0.1.0)
*Goal: Document the roadmap for future quality improvements.*

1.  **Increase Test Coverage to 85%+:** Continue the work from Tier 2 to reach the ultimate quality goal.
2.  **Upstream Contributions:** Submit a PR to `ed25519-dalek` to add `Zeroize` support.

# Logical Dependency Chain
1.  **Tier 1 must be completed first.** These tasks define and finalize the public API and core security patterns. It makes no sense to write extensive tests (Tier 2) for an API that is still in flux. Finalizing the architecture and documentation first provides a stable target for the quality assurance effort.
2.  **Tier 2 is the main work effort** that can begin once the API is stable. The process of writing tests will also help validate the API design choices made in Tier 1. The 75% coverage target is the final gate before release.

# Risks and Mitigations  
- **Risk: Scope Creep.**
  - The process of refactoring and adding tests may reveal other areas for improvement, potentially delaying the `v0.1.0` release.
  - **Mitigation:** Be disciplined. Adhere strictly to the scope defined in this PRD. All other potential improvements should be documented and deferred to a `v0.2.0` roadmap. The goal is to ship a high-quality `v0.1.0`, not a "perfect" but eternally delayed one.

- **Risk: Reaching 75% Test Coverage is a Significant Effort.**
  - The work required to increase coverage from ~39% to 75% is non-trivial and may be underestimated.
  - **Mitigation:**
    1.  **Break it down.** Decompose the task by crate and module. Assign ownership and track progress on each sub-task.
    2.  **Prioritize effectively.** Focus on testing the most security-critical and complex parts of the codebase first, as identified in the initial code review (e.g., state management, error paths, file handling).

- **Risk: The Server Handler Abstraction Debate Causes Delays.**
  - Deciding on the "right" architectural pattern for server handlers could lead to prolonged debate.
  - **Mitigation:** Timebox the decision to a single meeting. The primary goal is **consistency for v0.1.0**. Since there are no existing users, the cost of refactoring this pattern in a future release is low. Make a decision and move forward.

# Appendix  
- **Reference:** This PRD is derived from the senior engineering code review performed prior to the planned initial release.
- **Key Files for Tier 1:**
  - `indidus_e2ee_server/src/handlers/websocket.rs` (Peer ID validation)
  - `examples/*` (Doc sync and handler consistency)
  - `indidus_e2ee_client/src/client/messaging.rs` (Payload format)
- **Key Files for Tier 2:**
  - `.github/workflows/ci.yml` (Coverage gate)
  - All `src/**/*.rs` and `tests/**/*.rs` files (Test implementation)
</PRD>