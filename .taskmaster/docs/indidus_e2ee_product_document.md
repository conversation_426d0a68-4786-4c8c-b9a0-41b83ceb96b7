# Indidus E2EE Rust Crates - Product Document

<context>

# Overview

Building end-to-end encryption (E2EE) is notoriously difficult and risky. Developers face complex cryptographic protocols, intricate state management, and the constant threat of security vulnerabilities that could compromise user data. The Rust ecosystem lacks a comprehensive, developer-friendly E2EE solution that provides both security guarantees and ease of integration.

**Target Audience:** Rust developers building secure applications across mobile, desktop, and enterprise backend environments who need reliable encrypted communication without becoming cryptography experts.

**Core Value Propositions:**
- **Security:** Auditable, from-scratch Signal Protocol implementation with enterprise-grade encryption and zero-trust architecture
- **Simplicity:** 3-line integration for basic messaging with framework-agnostic design that works seamlessly with Axum, Actix, Warp, and other frameworks
- **Flexibility:** Unopinionated architecture where developers control HTTP endpoints, authentication, state persistence, and server implementation

The Indidus E2EE ecosystem consists of three production-ready Rust crates that enable secure, relay-based communication through a clean separation between cryptographic functionality and HTTP implementation.

# Core Features

## 1. Secure, Relay-based Messaging
- **What it does:** Enables encrypted message exchange between clients through a relay server using the Signal Protocol
- **Why it's important:** Provides enterprise-grade security without requiring peer-to-peer networking complexity or exposing client IP addresses
- **How it works at a high level:** Uses WebSocket connections to relay encrypted messages through a central server that cannot decrypt content

## 2. Large Encrypted File Transfer
- **What it does:** Supports secure transfer of files up to 10GB+ with real-time progress tracking and chunked delivery
- **Why it's important:** Enables secure sharing of large assets like documents, media, and datasets without compromising on security or user experience
- **How it works at a high level:** Chunks files over HTTP with per-chunk encryption, allowing resumable transfers and efficient bandwidth utilization

## 3. Stateless Client Architecture
- **What it does:** Externalizes all cryptographic state management to developer-controlled storage systems
- **Why it's important:** Provides complete control over data persistence, enabling integration with any storage backend and supporting complex deployment scenarios
- **How it works at a high level:** Serializes client state into opaque blobs that developers save/load using their preferred storage mechanism

## 4. Framework-Agnostic Server Integration
- **What it does:** Provides server SDK that integrates with any Rust web framework through trait-based abstractions
- **Why it's important:** Allows developers to add E2EE capabilities to existing applications without architectural changes or vendor lock-in
- **How it works at a high level:** Uses dependency injection patterns and trait implementations to plug into existing HTTP handlers and WebSocket infrastructure

# User Experience

The "user" of the Indidus E2EE ecosystem is the **developer** integrating these crates into their applications. The entire user experience is focused on Developer Experience (DX).

## User Personas

**Mobile App Developer:** Values efficient state management across app lifecycles and reliable message delivery. Needs simple integration that handles complex cryptographic operations transparently while providing control over when and how state is persisted.

**Desktop Application Developer:** Prioritizes secure file sharing capabilities and straightforward state persistence. Requires reliable relay-based architecture that doesn't expose client networking details while supporting large file transfers with progress tracking.

**Enterprise Backend Developer:** Focuses on secure inter-service communication and minimal configuration overhead. Needs drop-in encrypted messaging with stateless design that integrates seamlessly with existing microservice architectures.

## Key User Flows

### 1. Initializing a Client and Sending a Message
```rust
// Developer journey: Send encrypted message with user-managed state
use indidus_e2ee_client::{Client, ClientConfig, PeerId};

let config = ClientConfig {
    server_url: "https://myserver.com".to_string(),
    peer_id: PeerId::new("alice"),
    ..Default::default()
};

// Load previous state (developer's responsibility)
let saved_state = load_state_from_storage().await?; // Developer implements this

let client = Client::new(config, saved_state).await?;
client.send_message(&PeerId::new("bob"), "Confidential data").await?;

// Save state after operations (developer's responsibility)
let state_blob = client.save_state();
save_state_to_storage(state_blob).await?; // Developer implements this
```

### 2. Receiving Events in an Event Loop
```rust
// Developer journey: Handle incoming messages and events
while let Some(event) = client.next_event().await? {
    match event {
        ClientEvent::MessageReceived { from, content, .. } => {
            println!("Message from {}: {}", from, content);
        }
        ClientEvent::FileTransferProgress { transfer_id, progress } => {
            println!("Transfer {} progress: {}%", transfer_id, progress.percentage());
        }
        ClientEvent::ConnectionStateChanged { state } => {
            println!("Connection state: {:?}", state);
        }
    }
}
```

### 3. Integrating the Server into an Existing Web Framework
```rust
// Developer journey: Add E2EE to existing Axum application
use indidus_e2ee_server::{Server, ServerConfig};
use axum::{Router, routing::post};

let server_config = ServerConfig::default();
let e2ee_server = Server::new(server_config).await?;

let app = Router::new()
    .route("/api/ws", get(websocket_handler))
    .route("/api/upload", post(upload_handler))
    .route("/api/download", post(download_handler))
    .with_state(e2ee_server);
```

## UI/UX Considerations

**Minimal API Surface:** Core operations require only 3-5 method calls, with sensible defaults for common use cases and optional configuration for advanced scenarios.

**Actionable Error Handling:** All errors include specific guidance on resolution, with clear distinction between developer errors (configuration issues) and runtime errors (network failures).

**Comprehensive Documentation:** Extensive examples for common integration patterns, migration guides for different storage backends, and detailed security considerations for production deployments.

</context>

<PRD>

# Technical Architecture

## System Components

The Indidus E2EE ecosystem consists of three interconnected Rust crates forming a complete end-to-end encryption solution:

**`indidus_signal_protocol`:** Core cryptographic implementation providing a from-scratch Signal Protocol implementation with double ratchet encryption, X3DH key agreement, and comprehensive session management. This crate handles all cryptographic operations and maintains no external dependencies on HTTP or networking.

**`indidus_e2ee_client`:** Client SDK that consumes the core protocol to provide developer-friendly APIs for encrypted messaging and file transfer. Features stateless design with externalized state management, WebSocket-based real-time communication, and chunked file transfer capabilities.

**`indidus_e2ee_server`:** Server SDK enabling relay-based message routing and file transfer coordination. Provides framework-agnostic integration through trait abstractions, supports optional storage backends, and maintains no access to encrypted message content.

## Data Models

**Client State Blob:** Serialized, opaque data structure containing all cryptographic state (session keys, ratchet state, identity information). Developers are responsible for persistence using their preferred storage mechanism (database, filesystem, cloud storage).

**Public API Types:** Clean separation between public SDK types (`PeerId`, `MessageContent`, `FileTransfer`) and internal protocol state. Public types focus on developer usability while internal types optimize for cryptographic correctness.

**Message Routing:** Server maintains routing tables mapping peer IDs to active connections without accessing message content. All encryption/decryption occurs client-side.

## APIs and Integrations

**Client SDK Core Methods:**
- `Client::new(config, saved_state)` - Initialize with externalized state
- `send_message(peer_id, content)` - Send encrypted message
- `send_file(peer_id, file_path)` - Initiate encrypted file transfer
- `next_event()` - Receive messages and events
- `save_state()` - Serialize state for persistence

**Server SDK Core Methods:**
- `Server::new(config)` - Initialize relay server
- `handle_websocket(connection)` - Process WebSocket connections
- `handle_file_chunk(request)` - Process file transfer chunks
- `set_storage_backend(backend)` - Configure optional persistence

## Infrastructure Requirements

**Developer Responsibilities:**
- Host and maintain the relay server infrastructure
- Provide TLS termination for all HTTP/WebSocket endpoints
- Implement state persistence mechanism for clients
- Configure authentication and authorization as needed
- Manage file storage for large transfers (optional backend provided)

**Network Requirements:** Standard HTTP/HTTPS and WebSocket support with no special networking configuration required.

# Development Roadmap

## MVP Requirements

The MVP is defined as the completion of Phase 1 and Phase 2, resulting in production-ready crates suitable for security audit.

**Phase 1 Deliverables (`indidus_signal_protocol`):**
- Complete Signal Protocol implementation with X3DH key agreement
- Double ratchet encryption with forward secrecy
- Session management and key rotation
- Comprehensive test suite with cryptographic test vectors
- Memory-safe implementation with zero unsafe code blocks
- API stability suitable for downstream SDK development

**Phase 2 Deliverables (`indidus_e2ee_client` and `indidus_e2ee_server`):**
- Client SDK with stateless architecture and externalized state management
- Server SDK with framework-agnostic integration patterns
- WebSocket-based real-time messaging
- Chunked file transfer with progress tracking up to 10GB+
- Integration examples for major Rust web frameworks (Axum, Actix, Warp)
- Comprehensive error handling with actionable developer guidance

## Future Enhancements

**Post-1.0 Features (Explicitly Out of Scope for MVP):**
- True peer-to-peer communication without relay server
- Group chat functionality with multi-party encryption
- WebAssembly (WASM) compilation for browser environments
- Mobile platform bindings (iOS/Android)
- Advanced file sharing with metadata preservation
- Integration with external identity providers

Do not think about timelines whatsoever -- all that matters is scope and detailing exactly what needs to be built in each phase so it can later be cut up into tasks.

# Logical Dependency Chain

## Step 1: Foundation
`indidus_signal_protocol` is the absolute first dependency and must achieve API stability before any other development can proceed. This crate requires specialized cryptographic expertise and forms the security foundation for the entire ecosystem. No client or server development should begin until the core protocol API is stable and thoroughly tested.

## Step 2: Path to Usability
Once the core protocol API is stable, `indidus_e2ee_client` and `indidus_e2ee_server` can be developed in parallel. The first major milestone is achieving a working end-to-end `basic_chat` example that demonstrates message encryption, relay routing, and state persistence. This milestone validates the entire architecture and provides a foundation for feature expansion.

## Step 3: Pacing and Scoping
Feature implementation within the SDKs follows a specific order to maintain development momentum:
1. **Text Messaging:** Core encrypted message exchange with basic state management
2. **File Transfer:** Chunked file upload/download with progress tracking and resumability
3. **Polish:** Comprehensive error handling, framework integration examples, and developer documentation

Each phase builds upon the previous, ensuring that core functionality remains stable as features are added.

# Risks and Mitigations

## Technical Challenges

**Primary Risk:** Incorrect cryptographic implementation leading to security vulnerabilities that compromise user data and undermine the entire project's value proposition.

**Mitigations:**
- Rigorous testing with established cryptographic test vectors and edge case validation
- Modular design enabling isolated testing of individual cryptographic components
- Mandatory third-party security audit before 1.0 release with remediation of all findings
- Memory-safe Rust implementation with zero unsafe code blocks in cryptographic paths

# Appendix

## Technical Specifications

**Security Architecture:** Trust-on-First-Use (TOFU) model with Signal Protocol implementation providing forward secrecy, post-compromise security, and deniable authentication. All cryptographic operations occur client-side with relay server maintaining no access to plaintext content.

**Developer Responsibilities:**
- State persistence and management using preferred storage backends
- Server hosting and TLS configuration
- Authentication and authorization implementation
- File storage backend configuration for large transfers
- Network infrastructure and scaling considerations

## Dependencies & Technology Stack

**Core Protocol Dependencies (`indidus_signal_protocol`):**
```toml
[dependencies]
curve25519-dalek = "4.0"
ed25519-dalek = "2.0"
x25519-dalek = "2.0"
sha2 = "0.10"
hmac = "0.12"
hkdf = "0.12"
aes-gcm = "0.10"
rand = "0.8"
zeroize = "1.6"
serde = { version = "1.0", features = ["derive"] }
```

**Client Library Dependencies (`indidus_e2ee_client`):**
```toml
[dependencies]
indidus_signal_protocol = { path = "../indidus_signal_protocol" }
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.20"
reqwest = { version = "0.11", features = ["json", "stream"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
futures = "0.3"
thiserror = "1.0"
tracing = "0.1"
```

</PRD>