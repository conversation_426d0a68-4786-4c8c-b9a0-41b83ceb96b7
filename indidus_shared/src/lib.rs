//! # Indidus Shared Utilities
//!
//! This crate contains shared utilities and types used across the Indidus E2EE ecosystem.
//! It provides common functionality that is needed by both client and server components.
//!
//! ## Features
//!
//! - **Validation**: Strict validation utilities for identifiers and data formats
//! - **Type Safety**: Newtype patterns for enhanced type safety
//! - **Error Handling**: Consistent error types across the ecosystem
//!
//! ## Modules
//!
//! - [`validation`]: Validation utilities for identifiers and data formats

pub mod validation;

// Re-export main types for convenience
pub use validation::{PeerId, InvalidPeerId};