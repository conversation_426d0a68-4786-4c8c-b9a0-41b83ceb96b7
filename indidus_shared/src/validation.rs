//! # Validation Utilities
//!
//! This module provides validation utilities for identifiers and data formats
//! used throughout the Indidus E2EE ecosystem.
//!
//! ## Key Types
//!
//! - [`PeerId`]: A validated peer identifier with strict format requirements
//! - [`InvalidPeerId`]: Error type for peer ID validation failures
//!
//! ## Example
//!
//! ```rust
//! use indidus_shared::validation::PeerId;
//!
//! // Valid peer ID
//! let peer_id = PeerId::try_from("user123".to_string())?;
//! println!("Peer ID: {}", peer_id.as_str());
//!
//! // Invalid peer ID (too short)
//! let result = PeerId::try_from("ab".to_string());
//! assert!(result.is_err());
//! # Ok::<(), Box<dyn std::error::Error>>(())
//! ```

use regex::Regex;
use serde::{Deserialize, Serialize};
use std::fmt;
use std::sync::OnceLock;
use thiserror::Error;

/// A validated peer identifier with strict format requirements.
///
/// `PeerId` uses the newtype pattern to provide compile-time type safety
/// for peer identifiers. It enforces a strict format using the regex
/// `^[a-zA-Z0-9_-]{4,64}$`, which allows:
///
/// - Alphanumeric characters (a-z, A-Z, 0-9)
/// - Underscores (_) and hyphens (-)
/// - Length between 4 and 64 characters (inclusive)
///
/// ## Type Safety Benefits
///
/// - Prevents mixing up peer IDs with other string types
/// - Ensures all peer IDs are validated at construction time
/// - Provides clear API boundaries for functions that require valid peer IDs
///
/// ## Examples
///
/// ```rust
/// use indidus_shared::validation::PeerId;
///
/// // Valid peer IDs
/// let peer1 = PeerId::try_from("user123".to_string())?;
/// let peer2 = PeerId::try_from("alice_2024".to_string())?;
/// let peer3 = PeerId::try_from("bot-service-01".to_string())?;
///
/// // Access the underlying string
/// println!("Peer ID: {}", peer1.as_str());
/// println!("Peer ID: {}", peer1); // Display trait
///
/// // Invalid peer IDs
/// assert!(PeerId::try_from("ab".to_string()).is_err()); // Too short
/// assert!(PeerId::try_from("user@domain".to_string()).is_err()); // Invalid character
/// assert!(PeerId::try_from("a".repeat(65)).is_err()); // Too long
/// # Ok::<(), Box<dyn std::error::Error>>(())
/// ```
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct PeerId(String);

impl PeerId {
    /// Returns the peer ID as a string slice.
    ///
    /// This provides access to the underlying validated string without
    /// consuming the `PeerId` instance.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use indidus_shared::validation::PeerId;
    ///
    /// let peer_id = PeerId::try_from("user123".to_string())?;
    /// assert_eq!(peer_id.as_str(), "user123");
    /// # Ok::<(), Box<dyn std::error::Error>>(())
    /// ```
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Consumes the `PeerId` and returns the underlying string.
    ///
    /// This is useful when you need to take ownership of the string
    /// and no longer need the `PeerId` wrapper.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use indidus_shared::validation::PeerId;
    ///
    /// let peer_id = PeerId::try_from("user123".to_string())?;
    /// let string_value = peer_id.into_string();
    /// assert_eq!(string_value, "user123");
    /// # Ok::<(), Box<dyn std::error::Error>>(())
    /// ```
    pub fn into_string(self) -> String {
        self.0
    }
}

impl fmt::Display for PeerId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Constants for peer ID validation
const MIN_PEER_ID_LENGTH: usize = 4;
const MAX_PEER_ID_LENGTH: usize = 64;

/// Cached compiled regex for peer ID validation.
///
/// Uses `OnceLock` to ensure the regex is compiled only once for performance.
/// The regex pattern `^[a-zA-Z0-9_-]{4,64}$` validates:
/// - Only alphanumeric characters, underscores, and hyphens
/// - Length between 4 and 64 characters (inclusive)
static PEER_ID_REGEX: OnceLock<Regex> = OnceLock::new();

/// Get the compiled peer ID validation regex.
///
/// This function ensures the regex is compiled only once and reused
/// for all validation operations.
fn peer_id_regex() -> &'static Regex {
    PEER_ID_REGEX.get_or_init(|| {
        Regex::new(r"^[a-zA-Z0-9_-]{4,64}$")
            .expect("Peer ID regex should be valid")
    })
}

impl TryFrom<String> for PeerId {
    type Error = InvalidPeerId;

    /// Attempts to create a `PeerId` from a string.
    ///
    /// This method validates the input string against the strict peer ID format
    /// requirements using the regex `^[a-zA-Z0-9_-]{4,64}$`.
    ///
    /// # Validation Rules
    ///
    /// - Must not be empty
    /// - Must be between 4 and 64 characters (inclusive)
    /// - Must contain only alphanumeric characters, underscores, and hyphens
    ///
    /// # Errors
    ///
    /// Returns an [`InvalidPeerId`] error if the string fails validation:
    /// - [`InvalidPeerId::Empty`] if the string is empty
    /// - [`InvalidPeerId::TooShort`] if shorter than 4 characters
    /// - [`InvalidPeerId::TooLong`] if longer than 64 characters
    /// - [`InvalidPeerId::InvalidCharacters`] if contains disallowed characters
    ///
    /// # Examples
    ///
    /// ```rust
    /// use indidus_shared::validation::{PeerId, InvalidPeerId};
    ///
    /// // Valid peer IDs
    /// let peer1 = PeerId::try_from("user123".to_string())?;
    /// let peer2 = PeerId::try_from("alice_2024".to_string())?;
    /// let peer3 = PeerId::try_from("bot-service-01".to_string())?;
    ///
    /// // Invalid peer IDs
    /// assert!(matches!(
    ///     PeerId::try_from("".to_string()),
    ///     Err(InvalidPeerId::Empty)
    /// ));
    ///
    /// assert!(matches!(
    ///     PeerId::try_from("ab".to_string()),
    ///     Err(InvalidPeerId::TooShort { length: 2, min_length: 4 })
    /// ));
    ///
    /// assert!(matches!(
    ///     PeerId::try_from("user@domain".to_string()),
    ///     Err(InvalidPeerId::InvalidCharacters { .. })
    /// ));
    /// # Ok::<(), Box<dyn std::error::Error>>(())
    /// ```
    fn try_from(value: String) -> Result<Self, Self::Error> {
        // Check for empty string
        if value.is_empty() {
            return Err(InvalidPeerId::Empty);
        }

        let length = value.len();

        // Check length constraints
        if length < MIN_PEER_ID_LENGTH {
            return Err(InvalidPeerId::TooShort {
                length,
                min_length: MIN_PEER_ID_LENGTH,
            });
        }

        if length > MAX_PEER_ID_LENGTH {
            return Err(InvalidPeerId::TooLong {
                length,
                max_length: MAX_PEER_ID_LENGTH,
            });
        }

        // Check character constraints using regex
        if !peer_id_regex().is_match(&value) {
            return Err(InvalidPeerId::InvalidCharacters {
                peer_id: value,
            });
        }

        Ok(PeerId(value))
    }
}

impl TryFrom<&str> for PeerId {
    type Error = InvalidPeerId;

    /// Attempts to create a `PeerId` from a string slice.
    ///
    /// This is a convenience method that converts the `&str` to a `String`
    /// and then uses the `TryFrom<String>` implementation.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use indidus_shared::validation::PeerId;
    ///
    /// let peer_id = PeerId::try_from("user123")?;
    /// assert_eq!(peer_id.as_str(), "user123");
    /// # Ok::<(), Box<dyn std::error::Error>>(())
    /// ```
    fn try_from(value: &str) -> Result<Self, Self::Error> {
        PeerId::try_from(value.to_string())
    }
}

/// Error type for peer ID validation failures.
///
/// This error provides specific information about why a peer ID validation
/// failed, making it easier to debug issues and provide meaningful error
/// messages to users.
///
/// ## Error Variants
///
/// - [`InvalidPeerId::TooShort`]: Peer ID is shorter than 4 characters
/// - [`InvalidPeerId::TooLong`]: Peer ID is longer than 64 characters  
/// - [`InvalidPeerId::InvalidCharacters`]: Peer ID contains disallowed characters
/// - [`InvalidPeerId::Empty`]: Peer ID is an empty string
///
/// ## Examples
///
/// ```rust
/// use indidus_shared::validation::{PeerId, InvalidPeerId};
///
/// // Handle different error types
/// match PeerId::try_from("ab".to_string()) {
///     Ok(peer_id) => println!("Valid: {}", peer_id),
///     Err(InvalidPeerId::TooShort { length, min_length }) => {
///         println!("Peer ID too short: {} chars (min: {})", length, min_length);
///     }
///     Err(e) => println!("Other error: {}", e),
/// }
/// ```
#[derive(Debug, Clone, PartialEq, Eq, Error)]
pub enum InvalidPeerId {
    /// The peer ID is empty.
    #[error("Peer ID cannot be empty")]
    Empty,

    /// The peer ID is too short (less than 4 characters).
    #[error("Peer ID is too short: {length} characters (minimum: {min_length})")]
    TooShort {
        /// The actual length of the provided peer ID
        length: usize,
        /// The minimum required length (4)
        min_length: usize,
    },

    /// The peer ID is too long (more than 64 characters).
    #[error("Peer ID is too long: {length} characters (maximum: {max_length})")]
    TooLong {
        /// The actual length of the provided peer ID
        length: usize,
        /// The maximum allowed length (64)
        max_length: usize,
    },

    /// The peer ID contains invalid characters.
    #[error("Peer ID contains invalid characters: '{peer_id}' (allowed: a-z, A-Z, 0-9, _, -)")]
    InvalidCharacters {
        /// The peer ID that failed validation
        peer_id: String,
    },
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_valid_peer_ids() {
        // Test minimum length (4 characters)
        let peer_id = PeerId::try_from("abcd".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "abcd");

        // Test maximum length (64 characters)
        let long_id = "a".repeat(64);
        let peer_id = PeerId::try_from(long_id.clone()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), &long_id);

        // Test alphanumeric characters
        let peer_id = PeerId::try_from("user123".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "user123");

        // Test with underscores
        let peer_id = PeerId::try_from("user_123".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "user_123");

        // Test with hyphens
        let peer_id = PeerId::try_from("user-123".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "user-123");

        // Test mixed valid characters
        let peer_id = PeerId::try_from("User_123-ABC".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "User_123-ABC");

        // Test all uppercase
        let peer_id = PeerId::try_from("ABCD".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "ABCD");

        // Test all lowercase
        let peer_id = PeerId::try_from("abcd".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "abcd");

        // Test all numbers
        let peer_id = PeerId::try_from("1234".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "1234");
    }

    #[test]
    fn test_valid_peer_ids_from_str() {
        // Test TryFrom<&str> implementation
        let peer_id = PeerId::try_from("user123").expect("Should be valid");
        assert_eq!(peer_id.as_str(), "user123");

        let peer_id = PeerId::try_from("test_id").expect("Should be valid");
        assert_eq!(peer_id.as_str(), "test_id");
    }

    #[test]
    fn test_empty_peer_id() {
        let result = PeerId::try_from("".to_string());
        assert!(matches!(result, Err(InvalidPeerId::Empty)));

        let result = PeerId::try_from("");
        assert!(matches!(result, Err(InvalidPeerId::Empty)));
    }

    #[test]
    fn test_too_short_peer_id() {
        // Test 1 character
        let result = PeerId::try_from("a".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::TooShort {
                length: 1,
                min_length: 4
            })
        ));

        // Test 2 characters
        let result = PeerId::try_from("ab".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::TooShort {
                length: 2,
                min_length: 4
            })
        ));

        // Test 3 characters
        let result = PeerId::try_from("abc".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::TooShort {
                length: 3,
                min_length: 4
            })
        ));
    }

    #[test]
    fn test_too_long_peer_id() {
        // Test 65 characters (one over the limit)
        let long_id = "a".repeat(65);
        let result = PeerId::try_from(long_id);
        assert!(matches!(
            result,
            Err(InvalidPeerId::TooLong {
                length: 65,
                max_length: 64
            })
        ));

        // Test 100 characters (way over the limit)
        let very_long_id = "a".repeat(100);
        let result = PeerId::try_from(very_long_id);
        assert!(matches!(
            result,
            Err(InvalidPeerId::TooLong {
                length: 100,
                max_length: 64
            })
        ));
    }

    #[test]
    fn test_invalid_characters() {
        // Test with @ symbol
        let result = PeerId::try_from("user@domain".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "user@domain"
        ));

        // Test with ! symbol
        let result = PeerId::try_from("user!123".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "user!123"
        ));

        // Test with space
        let result = PeerId::try_from("user 123".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "user 123"
        ));

        // Test with dot
        let result = PeerId::try_from("user.123".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "user.123"
        ));

        // Test with plus
        let result = PeerId::try_from("user+123".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "user+123"
        ));

        // Test with hash
        let result = PeerId::try_from("user#123".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "user#123"
        ));

        // Test with parentheses
        let result = PeerId::try_from("user(123)".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "user(123)"
        ));

        // Test with brackets
        let result = PeerId::try_from("user[123]".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "user[123]"
        ));

        // Test with unicode characters
        let result = PeerId::try_from("userñ123".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::InvalidCharacters { peer_id }) if peer_id == "userñ123"
        ));
    }

    #[test]
    fn test_peer_id_display() {
        let peer_id = PeerId::try_from("user123".to_string()).expect("Should be valid");
        assert_eq!(format!("{}", peer_id), "user123");
    }

    #[test]
    fn test_peer_id_into_string() {
        let peer_id = PeerId::try_from("user123".to_string()).expect("Should be valid");
        let string_value = peer_id.into_string();
        assert_eq!(string_value, "user123");
    }

    #[test]
    fn test_peer_id_clone_and_equality() {
        let peer_id1 = PeerId::try_from("user123".to_string()).expect("Should be valid");
        let peer_id2 = peer_id1.clone();
        let peer_id3 = PeerId::try_from("user123".to_string()).expect("Should be valid");
        let peer_id4 = PeerId::try_from("user456".to_string()).expect("Should be valid");

        assert_eq!(peer_id1, peer_id2);
        assert_eq!(peer_id1, peer_id3);
        assert_ne!(peer_id1, peer_id4);
    }

    #[test]
    fn test_error_display() {
        // Test Empty error display
        let error = InvalidPeerId::Empty;
        assert_eq!(error.to_string(), "Peer ID cannot be empty");

        // Test TooShort error display
        let error = InvalidPeerId::TooShort {
            length: 2,
            min_length: 4,
        };
        assert_eq!(
            error.to_string(),
            "Peer ID is too short: 2 characters (minimum: 4)"
        );

        // Test TooLong error display
        let error = InvalidPeerId::TooLong {
            length: 100,
            max_length: 64,
        };
        assert_eq!(
            error.to_string(),
            "Peer ID is too long: 100 characters (maximum: 64)"
        );

        // Test InvalidCharacters error display
        let error = InvalidPeerId::InvalidCharacters {
            peer_id: "user@domain".to_string(),
        };
        assert_eq!(
            error.to_string(),
            "Peer ID contains invalid characters: 'user@domain' (allowed: a-z, A-Z, 0-9, _, -)"
        );
    }

    #[test]
    fn test_boundary_conditions() {
        // Test exactly 4 characters (minimum boundary)
        let peer_id = PeerId::try_from("abcd".to_string()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), "abcd");

        // Test exactly 64 characters (maximum boundary)
        let max_id = "a".repeat(64);
        let peer_id = PeerId::try_from(max_id.clone()).expect("Should be valid");
        assert_eq!(peer_id.as_str(), &max_id);

        // Test 3 characters (just under minimum)
        let result = PeerId::try_from("abc".to_string());
        assert!(matches!(
            result,
            Err(InvalidPeerId::TooShort {
                length: 3,
                min_length: 4
            })
        ));

        // Test 65 characters (just over maximum)
        let over_max_id = "a".repeat(65);
        let result = PeerId::try_from(over_max_id);
        assert!(matches!(
            result,
            Err(InvalidPeerId::TooLong {
                length: 65,
                max_length: 64
            })
        ));
    }

    #[test]
    fn test_regex_performance() {
        // Test that the regex is compiled only once by calling validation multiple times
        for i in 0..100 {
            let peer_id = format!("user{:03}", i);
            let result = PeerId::try_from(peer_id.clone());
            assert!(result.is_ok(), "Failed for peer_id: {}", peer_id);
        }
    }

    #[test]
    fn test_serde_serialization() {
        let peer_id = PeerId::try_from("user123".to_string()).expect("Should be valid");
        
        // Test serialization
        let serialized = serde_json::to_string(&peer_id).expect("Should serialize");
        assert_eq!(serialized, "\"user123\"");
        
        // Test deserialization
        let deserialized: PeerId = serde_json::from_str(&serialized).expect("Should deserialize");
        assert_eq!(deserialized, peer_id);
    }
}