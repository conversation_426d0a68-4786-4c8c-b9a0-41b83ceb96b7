
use serde::{Deserialize, Serialize};
use url::Url;
use uuid::Uuid;

use crate::error::ClientError;
use crate::ClientResult;

/// Configuration parameters for the Indidus E2EE Client
///
/// This struct holds all the configuration needed to initialize and operate
/// the client, including server endpoints, identity information, and operational settings.
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ClientConfig {
    /// The base URL of the Indidus E2EE server
    pub server_url: Url,

    /// The client's unique identifier
    pub client_id: Uuid,

    /// The client's display name (optional)
    pub display_name: Option<String>,

    /// Maximum number of pre-keys to maintain on the server
    pub max_prekeys: u32,

    /// Maximum number of skipped message keys to store per session
    pub max_skip_keys: u32,

    /// Connection timeout in seconds
    pub connection_timeout_secs: u64,

    /// Whether to automatically refresh pre-keys when running low
    pub auto_refresh_prekeys: bool,

    /// Whether to enable debug logging
    pub debug_mode: bool,
}

impl Default for ClientConfig {
    fn default() -> Self {
        Self {
            server_url: Url::parse("https://localhost:8080").expect("Valid default URL"),
            client_id: Uuid::new_v4(),
            display_name: None,
            max_prekeys: 100,
            max_skip_keys: 1000,
            connection_timeout_secs: 30,
            auto_refresh_prekeys: true,
            debug_mode: false,
        }
    }
}

impl ClientConfig {
    /// Create a new client configuration with the specified server URL
    ///
    /// # Arguments
    /// * `server_url` - The base URL of the Indidus E2EE server
    ///
    /// # Returns
    /// A new `ClientConfig` with default values and the specified server URL
    pub fn new(server_url: Url) -> Self {
        Self {
            server_url,
            ..Default::default()
        }
    }

    /// Set the client's display name
    pub fn with_display_name(mut self, name: String) -> Self {
        self.display_name = Some(name);
        self
    }

    /// Set the maximum number of pre-keys to maintain
    pub fn with_max_prekeys(mut self, max: u32) -> Self {
        self.max_prekeys = max;
        self
    }

    /// Set the maximum number of skipped message keys per session
    pub fn with_max_skip_keys(mut self, max: u32) -> Self {
        self.max_skip_keys = max;
        self
    }

    /// Set the connection timeout
    pub fn with_timeout(mut self, timeout_secs: u64) -> Self {
        self.connection_timeout_secs = timeout_secs;
        self
    }

    /// Enable or disable debug mode
    pub fn with_debug_mode(mut self, debug: bool) -> Self {
        self.debug_mode = debug;
        self
    }
}

/// Validate client configuration parameters
///
/// This method performs comprehensive validation of all configuration parameters,
/// providing specific error messages for each type of validation failure.
///
/// # Arguments
/// * `config` - The configuration to validate
///
/// # Returns
/// `Ok(())` if the configuration is valid
///
/// # Errors
/// Returns `ClientError::InvalidConfiguration` with specific details about what is invalid
pub(super) fn validate_configuration(config: &ClientConfig) -> ClientResult<()> {
    // Validate server URL
    validate_server_url(&config.server_url)?;

    // Validate timeout values
    validate_timeout_configuration(config)?;

    // Validate pre-key and skip key limits
    validate_key_limits(config)?;

    // Validate display name if provided
    if let Some(ref display_name) = config.display_name {
        validate_display_name(display_name)?;
    }

    // Validate client ID (should not be nil UUID)
    if config.client_id.is_nil() {
        return Err(ClientError::invalid_configuration(
            "client_id",
            "nil UUID",
            "Use Uuid::new_v4() to generate a valid client ID",
        ));
    }

    Ok(())
}

/// Validate server URL format and accessibility
fn validate_server_url(url: &Url) -> ClientResult<()> {
    // Check scheme
    match url.scheme() {
        "https" | "wss" => {
            // Valid schemes for secure communication
        }
        "http" | "ws" => {
            // Allow insecure schemes for testing and development
            #[cfg(not(test))]
            {
                return Err(ClientError::invalid_configuration(
                    "server_url.scheme",
                    url.scheme(),
                    "Use 'https://' or 'wss://' for secure communication. HTTP and WS are not allowed for security reasons.",
                ));
            }
            #[cfg(test)]
            {
                // Allow ws:// and http:// in test mode for mock servers
            }
        }
        scheme => {
            return Err(ClientError::invalid_configuration(
                "server_url.scheme",
                scheme,
                "Only 'https://' and 'wss://' schemes are supported",
            ));
        }
    }

    // Check host
    match url.host_str() {
        Some(host) if !host.is_empty() => {
            // Valid host
            if host == "localhost" || host == "127.0.0.1" || host == "::1" {
                // Allow localhost for development, but warn in production
                // This is acceptable for development and testing
            }
        }
        Some("") => {
            return Err(ClientError::invalid_configuration(
                "server_url.host",
                "empty",
                "Server URL must include a valid hostname or IP address",
            ));
        }
        Some(_) => {
            // Any other non-empty host is valid
        }
        None => {
            return Err(ClientError::invalid_configuration(
                "server_url.host",
                "missing",
                "Server URL must include a hostname or IP address",
            ));
        }
    }

    // Check port if specified
    if let Some(port) = url.port() {
        if port == 0 {
            return Err(ClientError::invalid_configuration(
                "server_url.port",
                "0",
                "Port 0 is not valid. Use a port number between 1 and 65535, or omit for default ports.",
            ));
        }
    }

    Ok(())
}

/// Validate timeout configuration values
fn validate_timeout_configuration(config: &ClientConfig) -> ClientResult<()> {
    // Connection timeout validation
    if config.connection_timeout_secs == 0 {
        return Err(ClientError::invalid_configuration(
            "connection_timeout_secs",
            "0",
            "Connection timeout must be at least 1 second. Recommended: 30-300 seconds.",
        ));
    }

    if config.connection_timeout_secs > 3600 {
        return Err(ClientError::invalid_configuration(
            "connection_timeout_secs",
            &config.connection_timeout_secs.to_string(),
            "Connection timeout should not exceed 1 hour (3600 seconds). Consider using a shorter timeout for better user experience.",
        ));
    }

    // Warn about very short timeouts (but don't fail)
    if config.connection_timeout_secs < 5 {
        // This is a warning case - very short timeouts might cause issues
        // but we'll allow it for testing purposes
    }

    Ok(())
}

/// Validate key management limits
fn validate_key_limits(config: &ClientConfig) -> ClientResult<()> {
    // Max pre-keys validation
    if config.max_prekeys == 0 {
        return Err(ClientError::invalid_configuration(
            "max_prekeys",
            "0",
            "Must maintain at least 1 pre-key. Recommended: 50-200 pre-keys for optimal performance.",
        ));
    }

    if config.max_prekeys > 10000 {
        return Err(ClientError::invalid_configuration(
            "max_prekeys",
            &config.max_prekeys.to_string(),
            "Too many pre-keys can impact performance and storage. Recommended maximum: 10000.",
        ));
    }

    // Max skip keys validation
    if config.max_skip_keys > 10000 {
        return Err(ClientError::invalid_configuration(
            "max_skip_keys",
            &config.max_skip_keys.to_string(),
            "Too many skip keys can impact memory usage. Recommended maximum: 10000.",
        ));
    }

    // Check for reasonable ratios
    if config.max_skip_keys > config.max_prekeys * 10 {
        return Err(ClientError::invalid_configuration(
            "max_skip_keys",
            &config.max_skip_keys.to_string(),
            &format!(
                "Skip keys ({}) should not exceed 10x the number of pre-keys ({}). This can cause excessive memory usage.",
                config.max_skip_keys, config.max_prekeys
            ),
        ));
    }

    Ok(())
}

/// Validate display name format and length
fn validate_display_name(display_name: &str) -> ClientResult<()> {
    if display_name.is_empty() {
        return Err(ClientError::invalid_configuration(
            "display_name",
            "empty string",
            "Display name cannot be empty. Either provide a valid name or set to None.",
        ));
    }

    if display_name.len() > 100 {
        return Err(ClientError::invalid_configuration(
            "display_name",
            &format!("{} characters", display_name.len()),
            "Display name must be 100 characters or less for compatibility with server limits.",
        ));
    }

    // Check for control characters
    if display_name.chars().any(|c| c.is_control()) {
        return Err(ClientError::invalid_configuration(
            "display_name",
            "contains control characters",
            "Display name cannot contain control characters. Use only printable characters.",
        ));
    }

    // Check for leading/trailing whitespace
    if display_name.trim() != display_name {
        return Err(ClientError::invalid_configuration(
            "display_name",
            "has leading/trailing whitespace",
            "Display name should not have leading or trailing whitespace. Consider trimming the name.",
        ));
    }

    Ok(())
}
