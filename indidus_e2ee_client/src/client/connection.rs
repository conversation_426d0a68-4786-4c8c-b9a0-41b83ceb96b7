use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;
use tokio_tungstenite::connect_async;
use uuid::Uuid;

use crate::client::Client;
use crate::error::{ClientError, ClientResult};
use super::events::ClientEvent;
use super::messaging::{ClientMessage, ServerMessage};

/// Connection state for the client
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConnectionState {
    /// C<PERSON> is disconnected from the server
    Disconnected,
    /// <PERSON><PERSON> is attempting to connect to the server
    Connecting,
    /// <PERSON><PERSON> is successfully connected to the server
    Connected,
    /// Connection failed with an error
    ConnectionError(String),
    /// Connection was lost unexpectedly
    ConnectionL<PERSON>,
    /// <PERSON><PERSON> is reconnecting after a connection loss
    Reconnecting,
}

impl Client {
    /// Connect to the WebSocket server and start the event loop
    ///
    /// This method establishes a WebSocket connection to the server and spawns
    /// a background task to handle incoming messages and events. The connection
    /// URL is constructed from the client configuration.
    ///
    /// # Returns
    /// Success or connection error
    ///
    /// # Errors
    /// - `ClientError::WebSocket` if the WebSocket connection fails
    /// - `ClientError::InvalidConfig` if the server URL is invalid
    /// - `ClientError::General` if the event loop setup fails
    ///
    /// # Example
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    /// let mut client = Client::new(config).expect("Failed to create client");
    ///
    /// // Connect to the server
    /// client.connect().await?;
    ///
    /// // Client is now connected and ready to receive events
    /// assert!(client.is_connected());
    /// # Ok(())
    /// # }
    /// ```
    pub async fn connect(&mut self) -> ClientResult<()> {
        // Check if already connected
        if self.is_connected() {
            return Ok(());
        }

        // Update connection state
        self.connection_state = ConnectionState::Connecting;

        // Construct WebSocket URL from HTTP URL
        let mut ws_url = self.config.server_url.clone();
        match ws_url.scheme() {
            "https" => ws_url.set_scheme("wss").map_err(|_| {
                ClientError::invalid_configuration("URL scheme", "https", "Failed to convert to wss")
            })?,
            "http" => ws_url.set_scheme("ws").map_err(|_| {
                ClientError::invalid_configuration("URL scheme", "http", "Failed to convert to ws")
            })?,
            "ws" | "wss" => {
                // Already a WebSocket URL, no conversion needed
            }
            _ => {
                return Err(ClientError::invalid_configuration(
                    "URL scheme",
                    ws_url.scheme(),
                    "Only http, https, ws, or wss schemes are supported",
                ));
            }
        }

        // Add WebSocket endpoint path only if not already present and not a direct WS URL
        if ws_url.path() == "/" || ws_url.path().is_empty() {
            if matches!(self.config.server_url.scheme(), "http" | "https") {
                ws_url.set_path("/ws");
            }
        }

        // Establish WebSocket connection
        let (ws_stream, _) = connect_async(&ws_url).await.map_err(|e| {
            self.connection_state = ConnectionState::ConnectionError(e.to_string());
            // Use the new contextual error type for better error reporting
            ClientError::websocket_with_context("connection establishment", e)
        })?;

        // Create MPSC channels for communication
        let (event_tx, event_rx) = mpsc::channel::<ClientEvent>(100);
        let (message_tx, message_rx) = mpsc::channel::<ClientMessage>(100);

        // Store the receivers and senders
        self.event_receiver = Some(event_rx);
        self.message_sender = Some(message_tx);

        // Update connection state
        self.connection_state = ConnectionState::Connected;

        // Spawn the event loop task
        let client_id = self.config.client_id;
        let display_name = self.config.display_name.clone();
        let event_loop_handle = tokio::spawn(Self::event_loop_task(
            ws_stream,
            event_tx,
            message_rx,
            client_id,
            display_name,
        ));

        self.event_loop_handle = Some(event_loop_handle);

        Ok(())
    }

    /// Disconnect from the WebSocket server
    ///
    /// This method closes the WebSocket connection and stops the event loop task.
    ///
    /// # Returns
    /// Success or disconnection error
    pub async fn disconnect(&mut self) -> ClientResult<()> {
        // Update connection state
        self.connection_state = ConnectionState::Disconnected;

        // Close the message sender to signal the event loop to stop
        self.message_sender.take();

        // Wait for the event loop task to complete
        if let Some(handle) = self.event_loop_handle.take() {
            let _ = handle.await;
        }

        // Clear the event receiver
        self.event_receiver.take();

        Ok(())
    }

    /// Event loop task that handles WebSocket communication
    ///
    /// This is the core event loop that runs in a background task. It handles:
    /// - Incoming WebSocket messages from the server
    /// - Outgoing messages from the client
    /// - Connection management and error handling
    ///
    /// # Arguments
    /// * `ws_stream` - The WebSocket stream
    /// * `event_tx` - Sender for client events
    /// * `message_rx` - Receiver for outgoing messages
    /// * `client_id` - The client's unique identifier
    /// * `display_name` - Optional display name for the client
    async fn event_loop_task(
        ws_stream: tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        event_tx: mpsc::Sender<ClientEvent>,
        mut message_rx: mpsc::Receiver<ClientMessage>,
        client_id: Uuid,
        display_name: Option<String>,
    ) {
        use futures_util::{SinkExt, StreamExt};
        use tokio_tungstenite::tungstenite::Message;

        let (mut ws_sink, mut ws_stream) = ws_stream.split();

        // Send initial authentication message
        let auth_message = ClientMessage::Authenticate {
            client_id,
            credentials: vec![], // Empty credentials for test server
            display_name,
        };

        if let Ok(auth_json) = serde_json::to_string(&auth_message) {
            if let Err(e) = ws_sink.send(Message::Text(auth_json)).await {
                let _ = event_tx
                    .send(ClientEvent::ConnectionStateChanged(
                        ConnectionState::ConnectionError(format!(
                            "WebSocket authentication failed: {}. Check your network connection and server availability.",
                            e
                        )),
                    ))
                    .await;
                return;
            }
        } else {
            let _ = event_tx
                .send(ClientEvent::ConnectionStateChanged(
                    ConnectionState::ConnectionError(
                        "Failed to serialize authentication message. This indicates a client-side issue.".to_string()
                    ),
                ))
                .await;
            return;
        }

        // Main event loop
        loop {
            tokio::select! {
                // Handle incoming WebSocket messages
                ws_msg = ws_stream.next() => {
                    match ws_msg {
                        Some(Ok(Message::Text(text))) => {
                            if let Err(e) = Self::handle_server_message(&event_tx, &text).await {
                                let _ = event_tx.send(ClientEvent::ServerError {
                                    error_code: "MESSAGE_PARSE_ERROR".to_string(),
                                    error_message: format!("Failed to parse server message: {}. The server sent malformed data that could not be parsed.", e),
                                }).await;
                            }
                        },
                        Some(Ok(Message::Binary(_data))) => {
                            // Handle binary messages if needed
                            let _ = event_tx.send(ClientEvent::ServerError {
                                error_code: "UNSUPPORTED_MESSAGE_TYPE".to_string(),
                                error_message: "Binary messages not supported".to_string(),
                            }).await;
                        },
                        Some(Ok(Message::Close(_))) => {
                            let _ = event_tx.send(ClientEvent::ConnectionStateChanged(
                                ConnectionState::Disconnected
                            )).await;
                            break;
                        },
                        Some(Ok(Message::Ping(data))) => {
                            // Respond to ping with pong
                            if let Err(e) = ws_sink.send(Message::Pong(data)).await {
                                let error_message = format!(
                                    "Failed to respond to server ping: {}. The connection may be unstable.", e
                                );
                                let _ = event_tx.send(ClientEvent::ConnectionStateChanged(
                                    ConnectionState::ConnectionError(error_message)
                                )).await;
                                break;
                            }
                        },
                        Some(Ok(Message::Pong(_))) => {
                            // Handle pong response (for keepalive)
                        },
                        Some(Ok(Message::Frame(_))) => {
                            // Handle raw frames (usually not needed at application level)
                        },
                        Some(Err(e)) => {
                            // Provide contextual error messages based on the type of WebSocket error
                            let error_message = match &e {
                                tokio_tungstenite::tungstenite::Error::ConnectionClosed => {
                                    "WebSocket connection was closed unexpectedly. The server may have disconnected or there may be a network issue.".to_string()
                                },
                                tokio_tungstenite::tungstenite::Error::AlreadyClosed => {
                                    "WebSocket connection is already closed. Cannot send or receive messages.".to_string()
                                },
                                tokio_tungstenite::tungstenite::Error::Io(io_err) => {
                                    format!("WebSocket I/O error: {}. Check your network connection and server availability.", io_err)
                                },
                                tokio_tungstenite::tungstenite::Error::Protocol(protocol_err) => {
                                    format!("WebSocket protocol error: {}. This may indicate server compatibility issues.", protocol_err)
                                },
                                tokio_tungstenite::tungstenite::Error::Capacity(capacity_err) => {
                                    format!("WebSocket capacity error: {}. The message may be too large or buffers are full.", capacity_err)
                                },
                                _ => {
                                    format!("WebSocket error during message processing: {}. Check your network connection and server availability.", e)
                                }
                            };
                            
                            let _ = event_tx.send(ClientEvent::ConnectionStateChanged(
                                ConnectionState::ConnectionError(error_message)
                            )).await;
                            break;
                        },
                        None => {
                            // WebSocket stream ended
                            let _ = event_tx.send(ClientEvent::ConnectionStateChanged(
                                ConnectionState::ConnectionLost
                            )).await;
                            break;
                        }
                    }
                },

                // Handle outgoing messages
                client_msg = message_rx.recv() => {
                    match client_msg {
                        Some(msg) => {
                            match serde_json::to_string(&msg) {
                                Ok(msg_json) => {
                                    if let Err(e) = ws_sink.send(Message::Text(msg_json)).await {
                                        // Provide contextual error messages for outgoing message failures
                                        let error_message = match &e {
                                            tokio_tungstenite::tungstenite::Error::ConnectionClosed => {
                                                "Cannot send message: WebSocket connection was closed. Try reconnecting to the server.".to_string()
                                            },
                                            tokio_tungstenite::tungstenite::Error::AlreadyClosed => {
                                                "Cannot send message: WebSocket connection is already closed. Try reconnecting to the server.".to_string()
                                            },
                                            tokio_tungstenite::tungstenite::Error::Io(io_err) => {
                                                format!("Failed to send message due to I/O error: {}. Check your network connection.", io_err)
                                            },
                                            _ => {
                                                format!("Failed to send message: {}. Check your network connection and server availability.", e)
                                            }
                                        };
                                        
                                        let _ = event_tx.send(ClientEvent::ConnectionStateChanged(
                                            ConnectionState::ConnectionError(error_message)
                                        )).await;
                                        break;
                                    }
                                },
                                Err(e) => {
                                    let _ = event_tx.send(ClientEvent::ConnectionStateChanged(
                                        ConnectionState::ConnectionError(format!(
                                            "Failed to serialize outgoing message: {}. This indicates a client-side issue.", e
                                        ))
                                    )).await;
                                    break;
                                }
                            }
                        },
                        None => {
                            // Message channel closed, time to exit
                            break;
                        }
                    }
                }
            }
        }
    }

    async fn handle_server_message(
        event_tx: &mpsc::Sender<ClientEvent>,
        text: &str,
    ) -> Result<(), ClientError> {
        match serde_json::from_str::<ServerMessage>(text) {
            Ok(server_msg) => {
                let client_event = match server_msg {
                    ServerMessage::MessageRelay { sender_id, encrypted_payload, metadata, message_type, message_id, .. } => {
                        ClientEvent::MessageReceived {
                            sender_id,
                            encrypted_payload,
                            metadata,
                            message_type,
                            message_id,
                        }
                    }
                    ServerMessage::FileTransferInitiated { sender_id, transfer_id, file_name, file_size, file_hash, .. } => {
                        ClientEvent::FileTransferInitiated {
                            sender_id,
                            transfer_id,
                            file_name,
                            file_size,
                            file_hash,
                        }
                    }
                    ServerMessage::FileTransferStatus { transfer_id, status } => {
                        ClientEvent::FileTransferStatusUpdate {
                            transfer_id,
                            status,
                        }
                    }
                    ServerMessage::PreKeyRequest { requester_id, count, .. } => {
                        ClientEvent::PreKeyRequested {
                            requester_id,
                            count,
                        }
                    }
                    ServerMessage::MessageDelivered { message_id, recipient_id } => {
                        ClientEvent::MessageDelivered {
                            message_id,
                            recipient_id,
                        }
                    }
                    ServerMessage::ClientPresence { client_id, online, .. } => {
                        ClientEvent::ClientPresenceUpdate {
                            client_id,
                            online,
                        }
                    }
                    ServerMessage::Error { error_code, error_message, .. } => {
                        ClientEvent::ServerError {
                            error_code,
                            error_message,
                        }
                    }
                    ServerMessage::ChunkData { transfer_id, chunk_index, encrypted_data, is_final, original_size } => {
                        ClientEvent::ChunkDataReceived {
                            transfer_id,
                            chunk_index,
                            encrypted_data,
                            is_final,
                            original_size,
                        }
                    }
                    ServerMessage::FileTransferOffer { sender_id: _, transfer_id: _, file_name: _, file_size: _, file_hash: _, chunk_count: _, encryption_key: _, expires_at: _ } => {
                        // This is a placeholder. In a real implementation, you would handle this offer.
                        // For now, we just log it as a server error to indicate it's unhandled.
                        ClientEvent::ServerError {
                            error_code: "UNHANDLED_OFFER".to_string(),
                            error_message: "File transfer offer received but not handled".to_string(),
                        }
                    }
                    _ => {
                        // For other message types, we can choose to ignore them or log them
                        return Ok(());
                    }
                };

                if event_tx.send(client_event).await.is_err() {
                    // Receiver has been dropped, which means the client is shutting down.
                    // This is not a critical error, but we can log it for debugging.
                    eprintln!("Failed to send client event: receiver dropped.");
                }
            }
            Err(e) => {
                return Err(ClientError::json_error("server message deserialization", e.to_string()));
            }
        }
        Ok(())
    }

    pub async fn next_event(&mut self) -> Option<ClientEvent> {
        loop {
            match &mut self.event_receiver {
                Some(receiver) => {
                    if let Some(event) = receiver.recv().await {
                        // Handle ChunkDataReceived events automatically
                        match &event {
                            ClientEvent::ChunkDataReceived {
                                transfer_id,
                                chunk_index,
                                encrypted_data,
                                is_final,
                                original_size,
                            } => {
                                // Process the chunk data automatically
                                if let Err(e) = self
                                    .handle_chunk_data(
                                        *transfer_id,
                                        *chunk_index,
                                        encrypted_data.clone(),
                                        *is_final,
                                        *original_size,
                                    )
                                    .await
                                {
                                    if self.config.debug_mode {
                                        println!("Error processing chunk data: {}", e);
                                    }

                                    // Emit a proper failure event with transfer details
                                    let (file_name, chunks_received, total_chunks) = {
                                        if let Some(download) =
                                            self.active_downloads.get(transfer_id)
                                        {
                                            (
                                                Some(download.file_name.clone()),
                                                download.received_chunks.len() as u64,
                                                download.chunk_count,
                                            )
                                        } else {
                                            (None, 0, 0)
                                        }
                                    };

                                    // Clean up the failed download
                                    if let Some(download) =
                                        self.active_downloads.remove(transfer_id)
                                    {
                                        // Clean up temporary file
                                        let temp_path = download.save_path.with_extension("tmp");
                                        let _ = tokio::fs::remove_file(&temp_path).await;
                                    }

                                    return Some(ClientEvent::FileTransferFailed {
                                        transfer_id: *transfer_id,
                                        file_name,
                                        error_code: "CHUNK_PROCESSING_ERROR".to_string(),
                                        error_message: e.to_string(),
                                        chunks_received,
                                        total_chunks,
                                    });
                                }
                                // Continue to next event (don't return ChunkDataReceived to user)
                                continue;
                            }
                            _ => return Some(event),
                        }
                    } else {
                        return None;
                    }
                }
                None => return None,
            }
        }
    }
}