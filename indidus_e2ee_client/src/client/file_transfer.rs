
use serde::{Deserialize, Serialize};
use tokio::fs::File;
use uuid::Uuid;
use indidus_shared::validation::PeerId;
use crate::client::Client;
use crate::error::{ClientError, ClientResult};
use std::path::Path;
use tokio::io::{AsyncReadExt, AsyncSeekExt, AsyncWriteExt};
use sha2::{Digest, Sha256};
use aes_gcm::aead::{Aead, KeyInit};
use aes_gcm::{Aes256Gcm, Key, Nonce};

/// Default chunk size for file transfers (1MB)
const DEFAULT_CHUNK_SIZE: usize = 1024 * 1024;

/// Request structure for initiating a file transfer
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTransferInitiationRequest {
    /// ID of the recipient client
    pub recipient_id: Uuid,
    /// Name of the file being transferred
    pub file_name: String,
    /// Total size of the file in bytes
    pub file_size: u64,
    /// Number of chunks the file will be split into
    pub chunk_count: u64,
    /// Size of each chunk (except possibly the last one)
    pub chunk_size: u64,
    /// SHA-256 hash of the entire file (optional for now)
    pub file_hash: Option<String>,
}

/// Response structure from file transfer initiation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTransferInitiationResponse {
    /// Unique transfer ID assigned by the server
    pub transfer_id: Uuid,
    /// Upload URL for sending chunks
    pub upload_url: String,
    /// Expiration time for the transfer
    pub expires_at: std::time::SystemTime,
}

/// A memory-efficient iterator for reading file chunks
///
/// This iterator reads a file in chunks of a specified size, loading only
/// one chunk into memory at a time. It properly handles the final chunk
/// which may be smaller than the standard chunk size.
pub struct FileChunkIterator {
    /// The file being read
    file: File,
    /// Size of each chunk (except possibly the last one)
    chunk_size: usize,
    /// Current chunk index (0-based)
    current_chunk: u64,
    /// Total number of chunks
    total_chunks: u64,
    /// Total file size
    file_size: u64,
    /// Number of bytes read so far
    bytes_read: u64,
}

/// A single file chunk with metadata
#[derive(Debug, Clone)]
pub struct FileChunk {
    /// The chunk data
    pub data: Vec<u8>,
    /// Chunk index (0-based)
    pub chunk_index: u64,
    /// Total number of chunks
    pub total_chunks: u64,
    /// Whether this is the final chunk
    pub is_final: bool,
    /// Actual size of this chunk
    pub size: usize,
}

/// Active download state for tracking ongoing file downloads
#[derive(Debug)]
pub struct ActiveDownload {
    /// Unique transfer ID
    pub transfer_id: Uuid,
    /// ID of the sender client
    pub sender_id: Uuid,
    /// File metadata
    pub file_name: String,
    pub file_size: u64,
    pub file_hash: String,
    /// Total number of chunks
    pub chunk_count: u64,
    /// Encryption key for decryption
    pub encryption_key: [u8; 32],
    /// Local file path where the file should be saved
    pub save_path: std::path::PathBuf,
    /// Set of received chunk indices
    pub received_chunks: std::collections::HashSet<u64>,
    /// Number of bytes downloaded so far
    pub bytes_downloaded: u64,
    /// Download start time
    pub started_at: std::time::SystemTime,
    /// Expiration time for the transfer
    pub expires_at: std::time::SystemTime,
    /// Temporary file for reassembly
    pub temp_file: Option<tokio::fs::File>,
}

/// File transfer status information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTransferStatus {
    /// Unique transfer ID
    pub transfer_id: Uuid,
    /// Current status of the transfer
    pub status: TransferStatus,
    /// Number of bytes uploaded
    pub bytes_uploaded: u64,
    /// Number of bytes downloaded
    pub bytes_downloaded: u64,
    /// Total file size
    pub total_size: u64,
    /// Transfer creation time
    pub created_at: std::time::SystemTime,
    /// Transfer expiration time
    pub expires_at: std::time::SystemTime,
}

/// Transfer status enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransferStatus {
    /// Transfer initiated, waiting for upload
    Pending,
    /// File is being uploaded
    Uploading,
    /// File uploaded, ready for download
    Ready,
    /// File is being downloaded
    Downloading,
    /// Transfer completed successfully
    Completed,
    /// Transfer was cancelled
    Cancelled,
    /// Transfer expired
    Expired,
    /// Transfer failed due to error
    Failed(String),
}

impl FileChunkIterator {
    /// Create a new file chunk iterator
    ///
    /// # Arguments
    /// * `file_path` - Path to the file to be chunked
    /// * `chunk_size` - Size of each chunk in bytes
    ///
    /// # Returns
    /// A new `FileChunkIterator` instance
    ///
    /// # Errors
    /// - `ClientError::FileIo` if the file cannot be opened or read
    /// - `ClientError::FileNotFound` if the file doesn't exist
    /// - `ClientError::FileTransfer` if the path is not a file
    pub async fn new(file_path: impl AsRef<Path>, chunk_size: usize) -> ClientResult<Self> {
        let file_path = file_path.as_ref();

        // Get file metadata
        let file_metadata = tokio::fs::metadata(file_path).await.map_err(|e| {
            if e.kind() == std::io::ErrorKind::NotFound {
                ClientError::FileNotFound {
                    path: file_path.display().to_string(),
                }
            } else {
                ClientError::FileIo {
                    operation: "read metadata".to_string(),
                    path: file_path.display().to_string(),
                    reason: e.to_string(),
                }
            }
        })?;

        // Ensure it's a file
        if !file_metadata.is_file() {
            return Err(ClientError::FileIo {
                operation: "read file".to_string(),
                path: file_path.display().to_string(),
                reason: "path is not a file".to_string(),
            });
        }

        let file_size = file_metadata.len();

        // Calculate total chunks
        let total_chunks = if file_size == 0 {
            1 // Even empty files need at least one chunk
        } else {
            (file_size + chunk_size as u64 - 1) / chunk_size as u64 // Ceiling division
        };

        // Open the file
        let file = File::open(file_path).await.map_err(|e| ClientError::FileIo {
            operation: "open file".to_string(),
            path: file_path.display().to_string(),
            reason: e.to_string(),
        })?;

        Ok(Self {
            file,
            chunk_size,
            current_chunk: 0,
            total_chunks,
            file_size,
            bytes_read: 0,
        })
    }

    /// Read the next chunk from the file
    ///
    /// # Returns
    /// The next file chunk, or `None` if all chunks have been read
    ///
    /// # Errors
    /// - `ClientError::FileIo` if there's an error reading from the file
    pub async fn next_chunk(&mut self) -> ClientResult<Option<FileChunk>> {
        // Check if we've read all chunks
        if self.current_chunk >= self.total_chunks {
            return Ok(None);
        }

        // Calculate how many bytes to read for this chunk
        let remaining_bytes = self.file_size - self.bytes_read;
        let bytes_to_read = std::cmp::min(self.chunk_size as u64, remaining_bytes) as usize;

        // Read the chunk data
        let mut buffer = vec![0u8; bytes_to_read];
        if bytes_to_read > 0 {
            self.file
                .read_exact(&mut buffer)
                .await
                .map_err(|e| ClientError::FileIo {
                    operation: "read chunk".to_string(),
                    path: "".to_string(),
                    reason: e.to_string(),
                })?;
        }

        // Update counters
        self.bytes_read += bytes_to_read as u64;
        let chunk_index = self.current_chunk;
        self.current_chunk += 1;

        // Determine if this is the final chunk
        let is_final = self.current_chunk >= self.total_chunks;

        let chunk = FileChunk {
            data: buffer,
            chunk_index,
            total_chunks: self.total_chunks,
            is_final,
            size: bytes_to_read,
        };

        Ok(Some(chunk))
    }

    /// Get the total number of chunks
    pub fn total_chunks(&self) -> u64 {
        self.total_chunks
    }

    /// Get the file size
    pub fn file_size(&self) -> u64 {
        self.file_size
    }

    /// Get the chunk size
    pub fn chunk_size(&self) -> usize {
        self.chunk_size
    }

    /// Get the current chunk index (0-based)
    pub fn current_chunk_index(&self) -> u64 {
        self.current_chunk
    }

    /// Get the number of bytes read so far
    pub fn bytes_read(&self) -> u64 {
        self.bytes_read
    }

    /// Check if all chunks have been read
    pub fn is_complete(&self) -> bool {
        self.current_chunk >= self.total_chunks
    }

    /// Get the progress as a percentage (0.0 to 100.0)
    pub fn progress_percentage(&self) -> f64 {
        if self.file_size == 0 {
            if self.current_chunk > 0 { 100.0 } else { 0.0 }
        } else {
            (self.bytes_read as f64 / self.file_size as f64) * 100.0
        }
    }
}

impl Client {
    pub fn active_download_ids(&self) -> Vec<Uuid> {
        self.active_downloads.keys().copied().collect()
    }

    pub fn download_progress(&self, transfer_id: &Uuid) -> Option<f64> {
        self.active_downloads.get(transfer_id).map(|download| {
            if download.file_size == 0 {
                100.0
            } else {
                (download.bytes_downloaded as f64 / download.file_size as f64) * 100.0
            }
        })
    }

    pub fn decrypt_chunk_data(
        &self,
        encrypted_data: &[u8],
        encryption_key: &[u8; 32],
    ) -> ClientResult<Vec<u8>> {
        if encrypted_data.len() < 12 {
            return Err(ClientError::file_transfer_error(
                Uuid::nil(),
                "Encrypted data too short to contain nonce".to_string(),
            ));
        }

        // Extract nonce and ciphertext
        let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        // Create cipher and decrypt
        let key = Key::<Aes256Gcm>::from_slice(encryption_key);
        let cipher = Aes256Gcm::new(key);

        cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| ClientError::decryption_failed(&PeerId::try_from("unknown").unwrap_or_else(|_| PeerId::try_from("fallback").unwrap()), e.to_string()))
    }

    pub async fn verify_file_hash(
        &self,
        file_path: &Path,
        expected_hash: &str,
    ) -> ClientResult<bool> {
        let mut file = tokio::fs::File::open(file_path)
            .await
            .map_err(|e| ClientError::FileIo {
                operation: "open file for hashing".to_string(),
                path: file_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            })?;
        let mut hasher = <Sha256 as Digest>::new();
        let mut buffer = [0u8; 8192];

        loop {
            let bytes_read = file.read(&mut buffer).await.map_err(|e| ClientError::FileIo {
                operation: "read file for hashing".to_string(),
                path: file_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            })?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }

        let computed_hash = format!("{:x}", hasher.finalize());
        Ok(computed_hash.eq_ignore_ascii_case(expected_hash))
    }

    pub async fn handle_chunk_data(
        &mut self,
        transfer_id: Uuid,
        chunk_index: u64,
        encrypted_data: Vec<u8>,
        _is_final: bool,
        original_size: usize,
    ) -> ClientResult<()> {
        // Get encryption key and check for duplicate chunks first
        let (encryption_key, _already_received) = {
            let download = self.active_downloads.get(&transfer_id).ok_or_else(|| {
                ClientError::file_transfer_error(
                    transfer_id,
                    format!("No active download found for transfer {}", transfer_id),
                )
            })?;

            // Check if we already have this chunk
            if download.received_chunks.contains(&chunk_index) {
                if self.config.debug_mode {
                    println!(
                        "Duplicate chunk {} for transfer {}, ignoring",
                        chunk_index, transfer_id
                    );
                }
                return Ok(());
            }

            (download.encryption_key, false)
        };

        // Decrypt the chunk data
        let decrypted_data = self.decrypt_chunk_data(&encrypted_data, &encryption_key)?;

        // Verify the decrypted size matches expected
        if decrypted_data.len() != original_size {
            return Err(ClientError::file_transfer_error(
                transfer_id,
                format!(
                    "Decrypted chunk size mismatch: expected {}, got {}",
                    original_size,
                    decrypted_data.len()
                ),
            ));
        }

        // Now get mutable access to the download for writing
        let download = self.active_downloads.get_mut(&transfer_id).ok_or_else(|| {
            ClientError::file_transfer_error(
                transfer_id,
                format!("No active download found for transfer {}", transfer_id),
            )
        })?;

        // Write the chunk to the temporary file at the correct offset
        if let Some(ref mut temp_file) = download.temp_file {

            let offset = chunk_index * DEFAULT_CHUNK_SIZE as u64;
            temp_file
                .seek(std::io::SeekFrom::Start(offset))
                .await
                .map_err(|e| ClientError::FileIo {
                    operation: "seek in temp file".to_string(),
                    path: download.save_path.to_string_lossy().to_string(),
                    reason: e.to_string(),
                })?;
            temp_file
                .write_all(&decrypted_data)
                .await
                .map_err(|e| ClientError::FileIo {
                    operation: "write to temp file".to_string(),
                    path: download.save_path.to_string_lossy().to_string(),
                    reason: e.to_string(),
                })?;
            temp_file.flush().await.map_err(|e| ClientError::FileIo {
                operation: "flush temp file".to_string(),
                path: download.save_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            })?;
        }

        // Update download state
        download.received_chunks.insert(chunk_index);
        download.bytes_downloaded += decrypted_data.len() as u64;

        let (chunk_count, file_size, is_complete) = {
            (
                download.chunk_count,
                download.file_size,
                download.received_chunks.len() as u64 == download.chunk_count,
            )
        };

        if self.config.debug_mode {
            let progress = (download.bytes_downloaded as f64 / file_size as f64) * 100.0;
            println!(
                "Downloaded chunk {}/{} for transfer {} ({:.1}% complete)",
                chunk_index + 1,
                chunk_count,
                transfer_id,
                progress
            );
        }

        // Emit progress event
        if let Err(e) = self.emit_progress_event(transfer_id).await {
            if self.config.debug_mode {
                println!("Failed to emit progress event: {}", e);
            }
        }

        // Check if download is complete
        if is_complete {
            self.finalize_download(transfer_id).await?;
        }

        Ok(())
    }

    async fn emit_progress_event(&self, transfer_id: Uuid) -> ClientResult<()> {
        // Get download state
        let download = self.active_downloads.get(&transfer_id).ok_or_else(|| {
            ClientError::file_transfer_error(
                transfer_id,
                format!("No active download found for transfer {}", transfer_id),
            )
        })?;

        // Calculate progress
        let chunks_processed = download.received_chunks.len() as u64;
        let total_chunks = download.chunk_count;
        let bytes_downloaded = download.bytes_downloaded;
        let total_bytes = download.file_size;
        let progress_percentage = if total_bytes == 0 {
            100.0
        } else {
            (bytes_downloaded as f64 / total_bytes as f64) * 100.0
        };

        // Create progress event
        let progress_event = crate::client::events::ClientEvent::FileTransferProgress {
            transfer_id,
            chunks_processed,
            total_chunks,
            bytes_downloaded,
            total_bytes,
            progress_percentage,
        };

        // Emit the event through the event sender if available
        if let Some(ref event_sender) = self.event_sender {
            if let Err(_) = event_sender.send(progress_event).await {
                return Err(ClientError::internal_error(
                    "Failed to send progress event: channel closed".to_string(),
                ));
            }
        }

        Ok(())
    }

    async fn finalize_download(&mut self, transfer_id: Uuid) -> ClientResult<()> {
        let download = self.active_downloads.remove(&transfer_id).ok_or_else(|| {
            ClientError::file_transfer_error(
                transfer_id,
                format!("No active download found for transfer {}", transfer_id),
            )
        })?;

        let transfer_duration = download.started_at.elapsed().unwrap_or_default();

        // Close the temporary file
        if let Some(temp_file) = download.temp_file {
            drop(temp_file);
        }

        let temp_path = download.save_path.with_extension("tmp");

        // Verify file integrity by checking the file size
        match tokio::fs::metadata(&temp_path).await {
            Ok(metadata) => {
                let actual_size = metadata.len();
                if actual_size != download.file_size {
                    // File size mismatch - emit failure event
                    self.emit_transfer_failed_event(
                        transfer_id,
                        Some(download.file_name.clone()),
                        "FILE_SIZE_MISMATCH".to_string(),
                        format!(
                            "File size mismatch: expected {} bytes, got {} bytes",
                            download.file_size, actual_size
                        ),
                        download.received_chunks.len() as u64,
                        download.chunk_count,
                    )
                    .await;

                    // Clean up temporary file
                    let _ = tokio::fs::remove_file(&temp_path).await;

                    return Err(ClientError::file_transfer_error(
                        transfer_id,
                        format!(
                            "File size mismatch: expected {} bytes, got {} bytes",
                            download.file_size, actual_size
                        ),
                    ));
                }
            }
            Err(e) => {
                // Cannot read temporary file - emit failure event
                self.emit_transfer_failed_event(
                    transfer_id,
                    Some(download.file_name.clone()),
                    "FILE_READ_ERROR".to_string(),
                    format!("Cannot read temporary file: {}", e),
                    download.received_chunks.len() as u64,
                    download.chunk_count,
                )
                .await;

                return Err(ClientError::FileIo {
                    operation: "read temp file".to_string(),
                    path: temp_path.to_string_lossy().to_string(),
                    reason: e.to_string(),
                });
            }
        }

        // Verify file hash if provided
        if !download.file_hash.is_empty() {
            match self.verify_file_hash(&temp_path, &download.file_hash).await {
                Ok(true) => {
                    if self.config.debug_mode {
                        println!("File hash verification passed for transfer {}", transfer_id);
                    }
                }
                Ok(false) => {
                    // Hash verification failed - emit failure event
                    self.emit_transfer_failed_event(
                        transfer_id,
                        Some(download.file_name.clone()),
                        "HASH_VERIFICATION_FAILED".to_string(),
                        "File hash verification failed - file may be corrupted".to_string(),
                        download.received_chunks.len() as u64,
                        download.chunk_count,
                    )
                    .await;

                    // Clean up temporary file
                    let _ = tokio::fs::remove_file(&temp_path).await;

                    return Err(ClientError::file_transfer_error(
                        transfer_id,
                        "File hash verification failed".to_string(),
                    ));
                }
                Err(e) => {
                    if self.config.debug_mode {
                        println!(
                            "Hash verification error for transfer {}: {}",
                            transfer_id, e
                        );
                    }
                    // Continue with transfer despite hash verification error
                }
            }
        }

        // Move temporary file to final location
        if let Err(e) = tokio::fs::rename(&temp_path, &download.save_path).await {
            // File move failed - emit failure event
            self.emit_transfer_failed_event(
                transfer_id,
                Some(download.file_name.clone()),
                "FILE_MOVE_ERROR".to_string(),
                format!("Failed to move file to final location: {}", e),
                download.received_chunks.len() as u64,
                download.chunk_count,
            )
            .await;

            return Err(ClientError::FileIo {
                operation: "move temp file".to_string(),
                path: temp_path.to_string_lossy().to_string(),
                reason: e.to_string(),
            });
        }

        if self.config.debug_mode {
            println!(
                "Download completed successfully:
                - Transfer ID: {}
                - File: {} ({} bytes)
                - Duration: {:?}
                - Saved to: {}",
                transfer_id,
                download.file_name,
                download.file_size,
                transfer_duration,
                download.save_path.display()
            );
        }

        // Emit completion event
        self.emit_transfer_complete_event(
            transfer_id,
            download.file_name,
            download.save_path.to_string_lossy().to_string(),
            download.file_size,
            transfer_duration,
        )
        .await;

        Ok(())
    }

    async fn emit_transfer_complete_event(
        &self,
        transfer_id: Uuid,
        file_name: String,
        file_path: String,
        file_size: u64,
        transfer_duration: std::time::Duration,
    ) {
        let complete_event = crate::client::events::ClientEvent::FileTransferComplete {
            transfer_id,
            file_name,
            file_path,
            file_size,
            transfer_duration,
        };

        if let Some(ref event_sender) = self.event_sender {
            if let Err(_) = event_sender.send(complete_event).await {
                if self.config.debug_mode {
                    println!(
                        "Failed to send completion event for transfer {}",
                        transfer_id
                    );
                }
            }
        }
    }

    async fn emit_transfer_failed_event(
        &self,
        transfer_id: Uuid,
        file_name: Option<String>,
        error_code: String,
        error_message: String,
        chunks_received: u64,
        total_chunks: u64,
    ) {
        let failed_event = crate::client::events::ClientEvent::FileTransferFailed {
            transfer_id,
            file_name,
            error_code,
            error_message,
            chunks_received,
            total_chunks,
        };

        if let Some(ref event_sender) = self.event_sender {
            if let Err(_) = event_sender.send(failed_event).await {
                if self.config.debug_mode {
                    println!("Failed to send failure event for transfer {}", transfer_id);
                }
            }
        }
    }
}
