
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use super::connection::ConnectionState;
use super::file_transfer::FileTransferStatus;

/// Events that can be received from the client's event loop
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum ClientEvent {
    /// A message was received from another client
    MessageReceived {
        /// ID of the sender client
        sender_id: Uuid,
        /// Encrypted message payload
        encrypted_payload: Vec<u8>,
        /// Message metadata
        metadata: HashMap<String, String>,
        /// Message type identifier
        message_type: String,
        /// Server-assigned message ID
        message_id: Option<Uuid>,
    },

    /// Connection state has changed
    ConnectionStateChanged(ConnectionState),

    /// A file transfer was initiated by another client
    FileTransferInitiated {
        /// ID of the sender client
        sender_id: Uuid,
        /// Unique transfer ID
        transfer_id: Uuid,
        /// File metadata
        file_name: String,
        file_size: u64,
        file_hash: String,
    },

    /// File transfer status update
    FileTransferStatusUpdate {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// Current transfer status
        status: FileTransferStatus,
    },

    /// Pre-key bundle request from another client
    PreKeyRequested {
        /// ID of the requesting client
        requester_id: Uuid,
        /// Number of pre-keys requested
        count: u32,
    },

    /// Server error occurred
    ServerError {
        /// Error code
        error_code: String,
        /// Error message
        error_message: String,
    },

    /// Delivery confirmation for a sent message
    MessageDelivered {
        /// Server-assigned message ID
        message_id: Uuid,
        /// ID of the recipient client
        recipient_id: Uuid,
    },

    /// Client presence update (online/offline status)
    ClientPresenceUpdate {
        /// ID of the client
        client_id: Uuid,
        /// Whether the client is online
        online: bool,
    },

    /// Chunk data received for file download
    ChunkDataReceived {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// Index of this chunk (0-based)
        chunk_index: u64,
        /// Encrypted chunk data
        encrypted_data: Vec<u8>,
        /// Whether this is the final chunk
        is_final: bool,
        /// Size of the original (unencrypted) chunk
        original_size: usize,
    },

    /// File transfer progress update
    FileTransferProgress {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// Number of chunks processed so far
        chunks_processed: u64,
        /// Total number of chunks
        total_chunks: u64,
        /// Number of bytes downloaded so far
        bytes_downloaded: u64,
        /// Total file size in bytes
        total_bytes: u64,
        /// Progress as a percentage (0.0 to 100.0)
        progress_percentage: f64,
    },

    /// File transfer completed successfully
    FileTransferComplete {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// File name
        file_name: String,
        /// Final file path
        file_path: String,
        /// File size in bytes
        file_size: u64,
        /// Time taken for the transfer
        transfer_duration: std::time::Duration,
    },

    /// File transfer failed
    FileTransferFailed {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// File name (if known)
        file_name: Option<String>,
        /// Error code
        error_code: String,
        /// Detailed error message
        error_message: String,
        /// Number of chunks that were successfully received
        chunks_received: u64,
        /// Total number of chunks expected
        total_chunks: u64,
    },
}
