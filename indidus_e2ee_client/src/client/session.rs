use indidus_signal_protocol::Session;
use indidus_shared::validation::PeerId;

use crate::client::Client;
use crate::error::{ClientError, ClientResult};

impl Client {
    /// Get the session for a specific remote client
    ///
    /// This method returns a reference to the session for the specified remote client ID.
    ///
    /// # Arguments
    /// * `remote_client_id` - The PeerId of the remote client
    ///
    /// # Returns
    /// A reference to the session, or an error if the session doesn't exist
    pub fn get_session(&self, remote_client_id: &PeerId) -> ClientResult<&Session> {
        self.sessions
            .get(remote_client_id)
            .ok_or_else(|| ClientError::session_error(remote_client_id.to_string(), "Session not found"))
    }

    /// Get a mutable reference to the session for a specific remote client
    ///
    /// This method returns a mutable reference to the session for the specified remote client ID.
    ///
    /// # Arguments
    /// * `remote_client_id` - The PeerId of the remote client
    ///
    /// # Returns
    /// A mutable reference to the session, or an error if the session doesn't exist
    pub fn get_session_mut(
        &mut self,
        remote_client_id: &PeerId,
    ) -> ClientResult<&mut Session> {
        self.sessions
            .get_mut(remote_client_id)
            .ok_or_else(|| ClientError::session_error(remote_client_id.to_string(), "Session not found"))
    }

    /// Add or update a session with the given session
    ///
    /// This method stores the session for the specified remote client ID.
    ///
    /// # Arguments
    /// * `remote_client_id` - The PeerId of the remote client
    /// * `session` - The session to store
    pub fn set_session(&mut self, remote_client_id: PeerId, session: Session) {
        self.sessions.insert(remote_client_id, session);
    }

    /// Remove a session
    ///
    /// This method removes the session with the specified remote client ID.
    ///
    /// # Arguments
    /// * `remote_client_id` - The PeerId of the remote client
    ///
    /// # Returns
    /// The removed session, or None if the session didn't exist
    pub fn remove_session(&mut self, remote_client_id: &PeerId) -> Option<Session> {
        self.sessions.remove(remote_client_id)
    }

    /// List all active session client IDs
    ///
    /// # Returns
    /// A vector of PeerIds representing all clients with active sessions
    pub fn list_sessions(&self) -> Vec<PeerId> {
        self.sessions.keys().cloned().collect()
    }
}