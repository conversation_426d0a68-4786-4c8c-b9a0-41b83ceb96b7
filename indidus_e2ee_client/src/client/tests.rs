use super::*;
use crate::client::messaging::{ClientMessage, ServerMessage};
use futures_util::{SinkExt, StreamExt};
use indidus_signal_protocol::{crypto::keys::KeyPair, crypto::keys::SigningKeyPair};
use indidus_signal_protocol::{session::ProtocolState, PreKeyBundle};
use indidus_shared::validation::PeerId;
use std::time::Duration;
use tokio::net::{TcpListener, TcpStream};
use tokio_tungstenite::{accept_async, tungstenite::Message};
use url::Url;

#[test]
fn test_client_config_default() {
    let config = ClientConfig::default();
    assert_eq!(config.max_prekeys, 100);
    assert_eq!(config.max_skip_keys, 1000);
    assert_eq!(config.connection_timeout_secs, 30);
    assert!(config.auto_refresh_prekeys);
    assert!(!config.debug_mode);
}

#[test]
fn test_client_config_builder() {
    let server_url = Url::parse("https://example.com").unwrap();
    let config = ClientConfig::new(server_url.clone())
        .with_display_name("Test Client".to_string())
        .with_max_prekeys(50)
        .with_debug_mode(true);

    assert_eq!(config.server_url, server_url);
    assert_eq!(config.display_name, Some("Test Client".to_string()));
    assert_eq!(config.max_prekeys, 50);
    assert!(config.debug_mode);
}

#[test]
fn test_client_creation() {
    let config = ClientConfig::default();
    let client = Client::new(config.clone()).expect("Failed to create client");

    assert_eq!(client.config().client_id, config.client_id);
    assert!(!client.is_initialized());
    assert!(!client.is_connected());
    assert_eq!(client.session_count(), 0);
}

#[tokio::test]
async fn test_initialize_method_idempotency() {
    println!("🔄 Testing Client::initialize() method idempotency...");

    // Step 1: Instantiate a new Client
    let config = ClientConfig::default();
    let mut client = Client::new(config).expect("Failed to create client");

    // Verify initial state - client should not be initialized
    assert!(!client.is_initialized());
    assert!(client.identity_key.is_none());
    assert!(client.signing_key.is_none());
    println!("   1. Client created in uninitialized state");

    // Step 2: Call initialize() for the first time
    client
        .initialize()
        .await
        .expect("First initialization should succeed");

    // Step 3: Assert that the client's internal identity key state is now populated
    assert!(client.is_initialized());
    assert!(client.identity_key.is_some());
    assert!(client.signing_key.is_some());
    println!("   2. First initialize() call generated identity keys");

    // Step 4: Store a copy of the generated public identity key
    let first_identity_public_key = client.identity_key.as_ref().unwrap().public_key();
    let first_signing_verifying_key = client.signing_key.as_ref().unwrap().verifying_key_bytes();

    // Also store the private key bytes for comparison (to ensure complete idempotency)
    let first_identity_private_bytes = client
        .identity_key
        .as_ref()
        .unwrap()
        .private_key()
        .as_bytes()
        .to_vec();
    let first_signing_private_bytes = client
        .signing_key
        .as_ref()
        .unwrap()
        .signing_key
        .to_bytes()
        .to_vec();

    println!("   3. Stored copies of generated keys for comparison");

    // Step 5: Call initialize() a second time
    client
        .initialize()
        .await
        .expect("Second initialization should succeed");

    // Step 6: Assert that the client's current public identity key is identical to the one stored after the first call
    let second_identity_public_key = client.identity_key.as_ref().unwrap().public_key();
    let second_signing_verifying_key = client.signing_key.as_ref().unwrap().verifying_key_bytes();

    // Compare public keys
    assert_eq!(
        first_identity_public_key.as_bytes(),
        second_identity_public_key.as_bytes(),
        "Identity public key should be identical after second initialize() call"
    );

    assert_eq!(
        first_signing_verifying_key, second_signing_verifying_key,
        "Signing verifying key should be identical after second initialize() call"
    );

    // Compare private keys to ensure complete idempotency
    let second_identity_private_bytes = client
        .identity_key
        .as_ref()
        .unwrap()
        .private_key()
        .as_bytes()
        .to_vec();
    let second_signing_private_bytes = client
        .signing_key
        .as_ref()
        .unwrap()
        .signing_key
        .to_bytes()
        .to_vec();

    assert_eq!(
        first_identity_private_bytes, second_identity_private_bytes,
        "Identity private key should be identical after second initialize() call"
    );

    assert_eq!(
        first_signing_private_bytes, second_signing_private_bytes,
        "Signing private key should be identical after second initialize() call"
    );

    println!("   4. Second initialize() call preserved existing keys");

    // Additional verification: Call initialize() a third time to ensure continued idempotency
    client
        .initialize()
        .await
        .expect("Third initialization should succeed");

    let third_identity_public_key = client.identity_key.as_ref().unwrap().public_key();
    let third_signing_verifying_key = client.signing_key.as_ref().unwrap().verifying_key_bytes();

    assert_eq!(
        first_identity_public_key.as_bytes(),
        third_identity_public_key.as_bytes(),
        "Identity public key should remain identical after third initialize() call"
    );

    assert_eq!(
        first_signing_verifying_key, third_signing_verifying_key,
        "Signing verifying key should remain identical after third initialize() call"
    );

    println!("   5. Third initialize() call confirmed continued idempotency");

    // Verify that the client remains in initialized state
    assert!(client.is_initialized());
    println!("   6. Client remains in initialized state throughout");

    println!("✅ Client::initialize() method idempotency test completed successfully!");
    println!("   - First call generates keys: ✓");
    println!("   - Second call preserves keys: ✓");
    println!("   - Third call maintains idempotency: ✓");
    println!("   - Both identity and signing keys preserved: ✓");
    println!("   - Public and private key consistency: ✓");
}

#[test]
fn test_client_new_with_state_no_saved_state() {
    let config = ClientConfig::default();
    let client = Client::new_with_state(config.clone(), None).expect("Failed to create client");

    assert_eq!(client.config().client_id, config.client_id);
    assert!(!client.is_initialized());
    assert!(!client.is_connected());
    assert_eq!(client.session_count(), 0);
}

#[test]
fn test_save_state_not_initialized() {
    let config = ClientConfig::default();
    let client = Client::new(config).expect("Failed to create client");

    let result = client.save_state_bytes();
    assert!(result.is_err());
    assert!(matches!(
        result.unwrap_err(),
        ClientError::NotInitialized { .. }
    ));
}

#[test]
fn test_session_management() {
    let config = ClientConfig::default();
    let mut client = Client::new(config).expect("Failed to create client");

    // Test session operations
    let remote_client_id = PeerId::try_from("test_peer_123").unwrap();
    let x3dh_secret = [42u8; 32];
    let their_dh_key = KeyPair::generate()
        .expect("Failed to generate DH key")
        .public_key();
    let dummy_signature =
        indidus_signal_protocol::crypto::signature::Ed25519Signature::from_bytes([0u8; 64]);
    let bundle = PreKeyBundle::new_without_timestamp(
        their_dh_key,
        vec![0u8; 32],
        their_dh_key,
        dummy_signature,
        vec![],
    );
    let session =
        Session::new_initiator(x3dh_secret, &bundle, 1000, Some("test-session".to_string()))
            .expect("Failed to create session");

    // Initially no sessions
    assert_eq!(client.session_count(), 0);
    assert!(client.get_session(&remote_client_id).is_err());

    // Add a session
    client.set_session(remote_client_id, session.clone());
    assert_eq!(client.session_count(), 1);

    // Retrieve the session
    let retrieved_session = client
        .get_session(&remote_client_id)
        .expect("Session should exist");
    assert_eq!(retrieved_session.root_key, session.root_key);
    assert_eq!(retrieved_session.max_skip, session.max_skip);

    // List sessions
    let session_list = client.list_sessions();
    assert_eq!(session_list.len(), 1);
    assert!(session_list.contains(&remote_client_id));

    // Remove session
    let removed_state = client.remove_session(&remote_client_id);
    assert!(removed_state.is_some());
    assert_eq!(client.session_count(), 0);
}

#[test]
fn test_end_to_end_state_management_round_trip() {
    // This is the comprehensive end-to-end test for state management
    println!("🔄 Starting End-to-End State Management Round-Trip Test...");

    // Step 1: Create initial client with configuration
    let server_url = Url::parse("https://test-server.example.com").expect("Valid URL");
    let config = ClientConfig::new(server_url)
        .with_display_name("Test Client".to_string())
        .with_max_prekeys(50)
        .with_max_skip_keys(500)
        .with_debug_mode(true);

    let mut client1 = Client::new(config.clone()).expect("Failed to create client1");
    println!("   1. Created initial client with config");

    // Step 2: Initialize client with identity keys
    let identity_key = KeyPair::generate().expect("Failed to generate identity key");
    let signing_key = SigningKeyPair::generate().expect("Failed to generate signing key");

    // Manually set the keys (in real usage, this would be done through an initialization method)
    client1.identity_key = Some(identity_key);
    client1.signing_key = Some(signing_key);
    println!("   2. Initialized client with identity keys");

    // Step 3: Create multiple sessions with different remote clients
    let remote_clients = [Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];

    for (i, &remote_client_id) in remote_clients.iter().enumerate() {
        let root_key = [(i as u8 + 1) * 10; 32]; // Different root keys for each session
        let dh_key = KeyPair::generate().expect("Failed to generate DH key");
        let remote_key = KeyPair::generate()
            .expect("Failed to generate remote key")
            .public_key();

        let protocol_state = ProtocolState::new_with_metadata(
            root_key,
            dh_key.clone(),
            Some(remote_key),
            1000,
            Some(remote_key),          // Use as identity key
            Some(dh_key.public_key()), // Use as local identity key
            Some(remote_key),          // Use as signed prekey
            None,                      // No onetime prekey
            Some(format!("session-{}", i + 1)),
        );

        // Convert to session state and simulate some session activity
        let mut session = protocol_state.to_session_state();
        session.sending_message_number = (i as u32 + 1) * 5;
        session.receiving_message_number = (i as u32 + 1) * 3;
        session.update_activity();

        client1.set_session(remote_client_id, session);
    }

    assert_eq!(client1.session_count(), 3);
    println!(
        "   3. Created {} sessions with different remote clients",
        remote_clients.len()
    );

    // Step 4: Save the client state
    let saved_state_blob = client1
        .save_state_bytes()
        .expect("Failed to save client state");
    assert!(!saved_state_blob.is_empty());
    println!("   4. Saved client state: {} bytes", saved_state_blob.len());

    // Step 5: Create a new client instance and restore from saved state
    let client2 = Client::new_with_state(config.clone(), Some(saved_state_blob))
        .expect("Failed to restore client from saved state");
    println!("   5. Restored client from saved state");

    // Step 6: Verify that all state was preserved

    // Check client initialization status
    assert!(client2.is_initialized());
    assert_eq!(client2.session_count(), 3);
    println!("   6a. Client initialization status preserved");

    // Check identity keys
    assert!(client2.identity_key.is_some());
    assert!(client2.signing_key.is_some());
    assert_eq!(
        client1
            .identity_key
            .clone()
            .unwrap()
            .public_key()
            .as_bytes(),
        client2
            .identity_key
            .clone()
            .unwrap()
            .public_key()
            .as_bytes()
    );
    println!("   6b. Identity keys preserved");

    // Check configuration
    assert_eq!(client1.config().client_id, client2.config().client_id);
    assert_eq!(client1.config().display_name, client2.config().display_name);
    assert_eq!(client1.config().max_prekeys, client2.config().max_prekeys);
    println!("   6c. Configuration preserved");

    // Check all sessions
    for (i, &remote_client_id) in remote_clients.iter().enumerate() {
        let state1 = client1
            .get_session(&remote_client_id)
            .expect("Original client should have session");
        let state2 = client2
            .get_session(&remote_client_id)
            .expect("Restored client should have session");

        // Verify cryptographic state
        assert_eq!(state1.root_key, state2.root_key);
        assert_eq!(
            state1.dh_ratchet_key.public_key().as_bytes(),
            state2.dh_ratchet_key.public_key().as_bytes()
        );
        assert_eq!(
            state1.dh_ratchet_key.private_key().as_bytes(),
            state2.dh_ratchet_key.private_key().as_bytes()
        );
        assert_eq!(state1.remote_dh_public_key, state2.remote_dh_public_key);
        assert_eq!(state1.sending_chain_key, state2.sending_chain_key);
        assert_eq!(state1.receiving_chain_key, state2.receiving_chain_key);

        // Verify message counters
        assert_eq!(state1.sending_message_number, state2.sending_message_number);
        assert_eq!(
            state1.receiving_message_number,
            state2.receiving_message_number
        );
        assert_eq!(state1.previous_chain_length, state2.previous_chain_length);

        // Verify metadata
        assert_eq!(state1.max_skip, state2.max_skip);
        assert_eq!(state1.remote_identity_key, state2.remote_identity_key);
        assert_eq!(state1.local_identity_key, state2.local_identity_key);
        assert_eq!(state1.session_id, state2.session_id);
        assert_eq!(state1.created_at, state2.created_at);
        assert_eq!(state1.last_activity, state2.last_activity);

        // Verify skipped message keys
        assert_eq!(state1.skipped_message_keys, state2.skipped_message_keys);

        println!("   6d. Session {} state fully preserved", i + 1);
    }

    // Step 7: Test that the restored client can save state again
    let second_save_blob = client2
        .save_state_bytes()
        .expect("Failed to save restored client state");
    assert!(!second_save_blob.is_empty());
    println!(
        "   7. Restored client can save state again: {} bytes",
        second_save_blob.len()
    );

    // Step 8: Verify the second save produces equivalent state
    let client3 = Client::new_with_state(config, Some(second_save_blob))
        .expect("Failed to restore from second save");

    assert_eq!(client2.session_count(), client3.session_count());
    for &remote_client_id in &remote_clients {
        let state2 = client2.get_session(&remote_client_id).unwrap();
        let state3 = client3.get_session(&remote_client_id).unwrap();
        assert_eq!(state2.root_key, state3.root_key);
        assert_eq!(state2.sending_message_number, state3.sending_message_number);
        assert_eq!(state2.session_id, state3.session_id);
    }
    println!("   8. Second round-trip produces equivalent state");

    println!("✅ End-to-End State Management Round-Trip Test Completed Successfully!");
    println!("   - Initial client creation: ✓");
    println!("   - Identity key initialization: ✓");
    println!("   - Multiple session creation: ✓");
    println!("   - State serialization: ✓");
    println!("   - State deserialization: ✓");
    println!("   - Cryptographic state preservation: ✓");
    println!("   - Message counter preservation: ✓");
    println!("   - Metadata preservation: ✓");
    println!("   - Multiple round-trip stability: ✓");
}

#[test]
fn test_state_management_with_invalid_data() {
    let config = ClientConfig::default();

    // Test with invalid JSON
    let invalid_json = b"{ invalid json }";
    let result = Client::new_with_state(config.clone(), Some(invalid_json.to_vec()));
    assert!(result.is_err());
    assert!(matches!(
        result.unwrap_err(),
        ClientError::StateCorrupted { .. }
    ));

    // Test with valid JSON but wrong structure
    let wrong_structure = b"{\"not_a_client_state\": true}";
    let result = Client::new_with_state(config, Some(wrong_structure.to_vec()));
    assert!(result.is_err());
    assert!(matches!(
        result.unwrap_err(),
        ClientError::StateCorrupted { .. }
    ));
}

#[test]
fn test_state_management_client_id_mismatch() {
    // Create a client and save its state
    let config1 = ClientConfig::default();
    let mut client1 = Client::new(config1).expect("Failed to create client");

    // Initialize the client
    let identity_key = KeyPair::generate().expect("Failed to generate identity key");
    let signing_key = SigningKeyPair::generate().expect("Failed to generate signing key");
    client1.identity_key = Some(identity_key);
    client1.signing_key = Some(signing_key);

    let saved_state = client1.save_state_bytes().expect("Failed to save state");

    // Try to restore with a different client ID
    let config2 = ClientConfig::default(); // This will have a different client_id
    let result = Client::new_with_state(config2, Some(saved_state));

    assert!(result.is_err());
    assert!(matches!(
        result.unwrap_err(),
        ClientError::StateIncompatible { .. }
    ));
}

#[test]
fn test_state_management_empty_sessions() {
    // Test state management with no sessions
    let config = ClientConfig::default();
    let mut client = Client::new(config.clone()).expect("Failed to create client");

    // Initialize the client but don't add any sessions
    let identity_key = KeyPair::generate().expect("Failed to generate identity key");
    let signing_key = SigningKeyPair::generate().expect("Failed to generate signing key");
    client.identity_key = Some(identity_key);
    client.signing_key = Some(signing_key);

    // Save and restore
    let saved_state = client.save_state_bytes().expect("Failed to save state");
    let restored_client =
        Client::new_with_state(config, Some(saved_state)).expect("Failed to restore client");

    assert!(restored_client.is_initialized());
    assert_eq!(restored_client.session_count(), 0);
    assert_eq!(restored_client.list_sessions().len(), 0);
}

#[test]
fn test_state_management_with_complex_sessions() {
    // Test with sessions that have complex state (skipped keys, etc.)
    let config = ClientConfig::default();
    let mut client = Client::new(config.clone()).expect("Failed to create client");

    // Initialize the client
    let identity_key = KeyPair::generate().expect("Failed to generate identity key");
    let signing_key = SigningKeyPair::generate().expect("Failed to generate signing key");
    client.identity_key = Some(identity_key);
    client.signing_key = Some(signing_key);

    // Create a session with complex state
    let remote_client_id = PeerId::try_from("test_peer_123").unwrap();
    let root_key = [123u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let protocol_state = ProtocolState::new_with_metadata(
        root_key,
        dh_key.clone(),
        Some(remote_key),
        100,
        Some(remote_key),
        Some(dh_key.public_key()),
        Some(remote_key),
        Some(remote_key), // Add onetime prekey
        Some("complex-session".to_string()),
    );

    // Convert to session state and add complex state
    let mut session = protocol_state.to_session_state();
    session.sending_chain_key = Some([55u8; 32]);
    session.receiving_chain_key = Some([66u8; 32]);
    session.sending_message_number = 42;
    session.receiving_message_number = 37;
    session.previous_chain_length = 15;

    // Add some skipped keys
    for i in 0..5 {
        let key = (vec![i as u8; 4], i as u32);
        let value = [i as u8; 32];
        session.skipped_message_keys.insert(key, value);
    }

    client.set_session(remote_client_id, session.clone());

    // Save and restore
    let saved_state = client.save_state_bytes().expect("Failed to save state");
    let restored_client =
        Client::new_with_state(config, Some(saved_state)).expect("Failed to restore client");

    // Verify complex state is preserved
    let restored_state = restored_client
        .get_session(&remote_client_id)
        .expect("Session should exist");

    assert_eq!(restored_state.sending_chain_key, session.sending_chain_key);
    assert_eq!(
        restored_state.receiving_chain_key,
        session.receiving_chain_key
    );
    assert_eq!(
        restored_state.sending_message_number,
        session.sending_message_number
    );
    assert_eq!(
        restored_state.receiving_message_number,
        session.receiving_message_number
    );
    assert_eq!(
        restored_state.previous_chain_length,
        session.previous_chain_length
    );
    assert_eq!(
        restored_state.skipped_message_keys.len(),
        session.skipped_message_keys.len()
    );
    // Note: SessionState doesn't have signed_prekey and onetime_prekey fields
    // These are only available in ProtocolState
    // assert_eq!(restored_state.signed_prekey, protocol_state.signed_prekey);
    // assert_eq!(restored_state.onetime_prekey, protocol_state.onetime_prekey);

    // Verify all skipped keys are preserved
    for (key, value) in &session.skipped_message_keys {
        assert_eq!(restored_state.skipped_message_keys.get(key), Some(value));
    }
}

/// Mock WebSocket server for testing
struct MockWebSocketServer {
    listener: TcpListener,
    port: u16,
}

impl MockWebSocketServer {
    async fn new() -> Self {
        let listener = TcpListener::bind("127.0.0.1:0")
            .await
            .expect("Failed to bind listener");
        let port = listener
            .local_addr()
            .expect("Failed to get local addr")
            .port();
        Self { listener, port }
    }

    fn url(&self) -> String {
        format!("ws://127.0.0.1:{}", self.port)
    }

    async fn accept_connection(&self) -> tokio_tungstenite::WebSocketStream<TcpStream> {
        let (stream, _) = self
            .listener
            .accept()
            .await
            .expect("Failed to accept connection");
        accept_async(stream)
            .await
            .expect("Failed to accept WebSocket")
    }

    async fn send_message(
        &self,
        message: ServerMessage,
    ) -> tokio_tungstenite::WebSocketStream<TcpStream> {
        let mut ws_stream = self.accept_connection().await;

        // Skip the authentication message from client
        if let Some(Ok(Message::Text(_auth_msg))) = ws_stream.next().await {
            // Authentication received, now send our message
        }

        let message_json = serde_json::to_string(&message).expect("Failed to serialize message");
        ws_stream
            .send(Message::Text(message_json))
            .await
            .expect("Failed to send message");
        ws_stream
    }

    async fn close_connection(&self) {
        let mut ws_stream = self.accept_connection().await;

        // Skip the authentication message from client
        if let Some(Ok(Message::Text(_auth_msg))) = ws_stream.next().await {
            // Authentication received, now close
        }

        ws_stream
            .close(None)
            .await
            .expect("Failed to close connection");
    }
}

#[tokio::test]
async fn test_websocket_connection_and_message_reception() {
    println!("🔄 Starting WebSocket Connection and Message Reception Test...");

    // Create mock server
    let mock_server = MockWebSocketServer::new().await;
    let server_url = Url::parse(&mock_server.url()).expect("Valid URL");

    // Create client configuration
    let config = ClientConfig::new(server_url).with_display_name("Test Client".to_string());

    let mut client = Client::new(config).expect("Failed to create client");

    // Start the mock server task
    let server_handle = tokio::spawn(async move {
        let test_message = ServerMessage::MessageRelay {
            sender_id: Uuid::new_v4(),
            recipient_id: Uuid::new_v4(),
            encrypted_payload: b"Hello, World!".to_vec(),
            metadata: HashMap::new(),
            message_type: "text".to_string(),
            message_id: Some(Uuid::new_v4()),
        };

        let _ws_stream = mock_server.send_message(test_message).await;

        // Keep the connection alive for a bit
        tokio::time::sleep(Duration::from_millis(100)).await;
    });

    // Connect the client
    client.connect().await.expect("Failed to connect");
    assert!(client.is_connected());
    println!("   1. Client connected successfully");

    // Wait for the message
    let event = tokio::time::timeout(Duration::from_secs(2), client.next_event())
        .await
        .expect("Timeout waiting for event")
        .expect("Expected an event");

    // Verify the received event
    match event {
        ClientEvent::MessageReceived {
            encrypted_payload,
            message_type,
            .. // Ignore sender_id, recipient_id, message_id
        } => {
            assert_eq!(encrypted_payload, b"Hello, World!");
            assert_eq!(message_type, "text");
            println!("   2. Message received correctly");
        }
        other => panic!("Expected MessageReceived, got {:?}", other),
    }

    // Clean up
    client.disconnect().await.expect("Failed to disconnect");
    server_handle.await.expect("Server task failed");

    println!("✅ WebSocket Connection and Message Reception Test Completed!");
}

#[tokio::test]
async fn test_websocket_connection_state_changes() {
    println!("🔄 Starting WebSocket Connection State Changes Test...");

    // Create mock server
    let mock_server = MockWebSocketServer::new().await;
    let server_url = Url::parse(&mock_server.url()).expect("Valid URL");

    // Create client configuration
    let config = ClientConfig::new(server_url);
    let mut client = Client::new(config).expect("Failed to create client");

    // Initially disconnected
    assert_eq!(client.connection_state(), &ConnectionState::Disconnected);
    println!("   1. Initial state: Disconnected");

    // Start the mock server task that will close the connection
    let server_handle = tokio::spawn(async move {
        mock_server.close_connection().await;
    });

    // Connect the client
    client.connect().await.expect("Failed to connect");
    assert!(client.is_connected());
    println!("   2. Connected successfully");

    // Wait for connection state change due to server closing
    let mut connection_closed = false;
    let timeout = tokio::time::timeout(Duration::from_secs(2), async {
        while let Some(event) = client.next_event().await {
            match event {
                ClientEvent::ConnectionStateChanged(ConnectionState::Disconnected)
                | ClientEvent::ConnectionStateChanged(ConnectionState::ConnectionLost) => {
                    connection_closed = true;
                    break;
                }
                _ => continue,
            }
        }
    });

    timeout
        .await
        .expect("Timeout waiting for connection state change");
    assert!(
        connection_closed,
        "Expected connection to be closed by server"
    );
    println!("   3. Connection state change detected");

    // Clean up
    client.disconnect().await.expect("Failed to disconnect");
    server_handle.await.expect("Server task failed");

    println!("✅ WebSocket Connection State Changes Test Completed!");
}

#[tokio::test]
async fn test_websocket_file_transfer_events() {
    println!("🔄 Starting WebSocket File Transfer Events Test...");

    // Create mock server
    let mock_server = MockWebSocketServer::new().await;
    let server_url = Url::parse(&mock_server.url()).expect("Valid URL");

    // Create client configuration
    let config = ClientConfig::new(server_url);
    let mut client = Client::new(config).expect("Failed to create client");

    let transfer_id = Uuid::new_v4();
    let sender_id = Uuid::new_v4();

    // Start the mock server task
    let server_handle = tokio::spawn(async move {
        let file_transfer_message = ServerMessage::FileTransferInitiated {
            sender_id,
            recipient_id: Uuid::new_v4(),
            transfer_id,
            file_name: "test.txt".to_string(),
            file_size: 1024,
            file_hash: "abc123".to_string(),
            upload_url: "https://example.com/upload".to_string(),
            download_url: "https://example.com/download".to_string(),
            expires_at: std::time::SystemTime::now() + Duration::from_secs(3600),
        };

        let _ws_stream = mock_server.send_message(file_transfer_message).await;

        // Keep the connection alive for a bit
        tokio::time::sleep(Duration::from_millis(100)).await;
    });

    // Connect the client
    client.connect().await.expect("Failed to connect");

    // Wait for the file transfer event
    let event = tokio::time::timeout(Duration::from_secs(2), client.next_event())
        .await
        .expect("Timeout waiting for event")
        .expect("Expected an event");

    // Verify the received event
    match event {
        ClientEvent::FileTransferInitiated {
            sender_id: recv_sender_id,
            transfer_id: recv_transfer_id,
            file_name,
            file_size,
            file_hash,
        } => {
            assert_eq!(recv_sender_id, sender_id);
            assert_eq!(recv_transfer_id, transfer_id);
            assert_eq!(file_name, "test.txt");
            assert_eq!(file_size, 1024);
            assert_eq!(file_hash, "abc123");
            println!("   1. File transfer event received correctly");
        }
        other => panic!("Expected FileTransferInitiated, got {:?}", other),
    }

    // Clean up
    client.disconnect().await.expect("Failed to disconnect");
    server_handle.await.expect("Server task failed");

    println!("✅ WebSocket File Transfer Events Test Completed!");
}

#[tokio::test]
async fn test_websocket_error_handling() {
    println!("🔄 Starting WebSocket Error Handling Test...");

    // Create mock server
    let mock_server = MockWebSocketServer::new().await;
    let server_url = Url::parse(&mock_server.url()).expect("Valid URL");

    // Create client configuration
    let config = ClientConfig::new(server_url);
    let mut client = Client::new(config).expect("Failed to create client");

    // Start the mock server task that sends malformed JSON
    let server_handle = tokio::spawn(async move {
        let mut ws_stream = mock_server.accept_connection().await;

        // Skip the authentication message from client
        if let Some(Ok(Message::Text(_auth_msg))) = ws_stream.next().await {
            // Authentication received, now send malformed JSON
        }

        // Send malformed JSON
        ws_stream
            .send(Message::Text("{ invalid json }".to_string()))
            .await
            .expect("Failed to send malformed message");

        // Keep the connection alive for a bit
        tokio::time::sleep(Duration::from_millis(100)).await;
    });

    // Connect the client
    client.connect().await.expect("Failed to connect");

    // Wait for the error event
    let event = tokio::time::timeout(Duration::from_secs(2), client.next_event())
        .await
        .expect("Timeout waiting for event")
        .expect("Expected an event");

    // Verify the received event is a server error
    match event {
        ClientEvent::ServerError {
            error_code,
            error_message,
        } => {
            assert_eq!(error_code, "MESSAGE_PARSE_ERROR");
            assert!(error_message.contains("Failed to parse server message"));
            println!("   1. Error event received correctly");
        }
        other => panic!("Expected ServerError, got {:?}", other),
    }

    // Clean up
    client.disconnect().await.expect("Failed to disconnect");
    server_handle.await.expect("Server task failed");

    println!("✅ WebSocket Error Handling Test Completed!");
}

#[tokio::test]
async fn test_websocket_send_message() {
    println!("🔄 Starting WebSocket Send Message Test...");

    // Create mock server
    let mock_server = MockWebSocketServer::new().await;
    let server_url = Url::parse(&mock_server.url()).expect("Valid URL");

    // Create client configuration
    let config = ClientConfig::new(server_url);
    let mut client = Client::new(config).expect("Failed to create client");

    let recipient_id = PeerId::try_from("recipient_123").unwrap();
    let test_payload = b"Test message payload".to_vec();

    // Start the mock server task that receives messages
    let server_handle = tokio::spawn(async move {
        let mut ws_stream = mock_server.accept_connection().await;

        // Skip the authentication message from client
        if let Some(Ok(Message::Text(_auth_msg))) = ws_stream.next().await {
            // Authentication received
        }

        // Wait for the client message
        if let Some(Ok(Message::Text(message_text))) = ws_stream.next().await {
            let client_message: ClientMessage =
                serde_json::from_str(&message_text).expect("Failed to parse client message");

            match client_message {
                ClientMessage::SendMessage {
                    recipient_id: recv_recipient_id,
                    encrypted_payload,
                    message_type,
                    .. // Ignore message_id and metadata for this assertion
                } => {
                    assert_eq!(recv_recipient_id, recipient_id);
                    assert_eq!(encrypted_payload, b"Test message payload");
                    assert_eq!(message_type, "test");
                    println!("   1. Server received client message correctly");
                }
                other => panic!("Expected SendMessage, got {:?}", other),
            }
        }
    });

    // Connect the client
    client.connect().await.expect("Failed to connect");

    // Send a message
    let mut metadata = HashMap::new();
    metadata.insert("timestamp".to_string(), "2024-01-01T00:00:00Z".to_string());

    client
        .send_encrypted_message(recipient_id, test_payload, metadata, "test".to_string())
        .await
        .expect("Failed to send message");

    println!("   2. Client sent message successfully");

    // Clean up
    client.disconnect().await.expect("Failed to disconnect");
    server_handle.await.expect("Server task failed");

    println!("✅ WebSocket Send Message Test Completed!");
}

#[tokio::test]
async fn test_file_download_functionality() {
    println!("🔄 Testing File Download Functionality...");

    // Create a test client
    let config =
        ClientConfig::new(Url::parse("https://test-server.com").unwrap()).with_debug_mode(true);
    let client = Client::new(config).expect("Failed to create client");

    // Test active downloads tracking
    assert_eq!(client.active_download_ids().len(), 0);
    println!("   1. Initial state: no active downloads");

    // Test download progress for non-existent transfer
    let test_transfer_id = Uuid::new_v4();
    assert!(client.download_progress(&test_transfer_id).is_none());
    println!("   2. Download progress returns None for non-existent transfer");

    // Test chunk data decryption
    let encryption_key = [42u8; 32];
    let test_data = b"Hello, world!";

    // Create encrypted data (nonce + ciphertext)
    use aes_gcm::{
        aead::{Aead, KeyInit, OsRng},
        Aes256Gcm, Key, Nonce,
    };
    use rand::RngCore;

    let key = Key::<Aes256Gcm>::from_slice(&encryption_key);
    let cipher = Aes256Gcm::new(key);

    let mut nonce_bytes = [0u8; 12];
    OsRng.fill_bytes(&mut nonce_bytes);
    let nonce = Nonce::from_slice(&nonce_bytes);

    let encrypted_data = cipher.encrypt(nonce, test_data.as_ref()).unwrap();

    // Prepare the full encrypted payload (nonce + ciphertext)
    let mut full_encrypted_data = Vec::with_capacity(12 + encrypted_data.len());
    full_encrypted_data.extend_from_slice(&nonce_bytes);
    full_encrypted_data.extend_from_slice(&encrypted_data);

    // Test decryption
    let decrypted_data = client
        .decrypt_chunk_data(&full_encrypted_data, &encryption_key)
        .unwrap();
    assert_eq!(decrypted_data, test_data);
    println!("   3. Chunk data encryption/decryption works correctly");

    // Test error cases

    // Test with invalid encrypted data (too short)
    let invalid_data = vec![1, 2, 3]; // Less than 12 bytes
    let result = client.decrypt_chunk_data(&invalid_data, &encryption_key);
    assert!(result.is_err());
    println!("   4. Correctly handles invalid encrypted data");

    // Test with wrong encryption key
    let wrong_key = [99u8; 32];
    let result = client.decrypt_chunk_data(&full_encrypted_data, &wrong_key);
    assert!(result.is_err());
    println!("   5. Correctly handles wrong encryption key");

    println!("✅ File Download Functionality Test Completed!");
}

#[test]
fn test_file_transfer_message_serialization() {
    println!("🔄 Testing File Transfer Message Serialization...");

    // Test ClientMessage serialization
    let request_chunk_msg = ClientMessage::RequestChunk {
        transfer_id: Uuid::new_v4(),
        chunk_index: 42,
    };

    let serialized = serde_json::to_string(&request_chunk_msg).unwrap();
    let deserialized: ClientMessage = serde_json::from_str(&serialized).unwrap();

    match deserialized {
        ClientMessage::RequestChunk { chunk_index, .. } => {
            assert_eq!(chunk_index, 42);
        }
        _ => panic!("Wrong message type"),
    }
    println!("   1. RequestChunk message serialization works");

    // Test AcceptFileTransfer message
    let accept_msg = ClientMessage::AcceptFileTransfer {
        transfer_id: Uuid::new_v4(),
        save_path: "/tmp/test_file.txt".to_string(),
    };

    let serialized = serde_json::to_string(&accept_msg).unwrap();
    let deserialized: ClientMessage = serde_json::from_str(&serialized).unwrap();

    match deserialized {
        ClientMessage::AcceptFileTransfer { save_path, .. } => {
            assert_eq!(save_path, "/tmp/test_file.txt");
        }
        _ => panic!("Wrong message type"),
    }
    println!("   2. AcceptFileTransfer message serialization works");

    // Test RejectFileTransfer message
    let reject_msg = ClientMessage::RejectFileTransfer {
        transfer_id: Uuid::new_v4(),
        reason: Some("File too large".to_string()),
    };

    let serialized = serde_json::to_string(&reject_msg).unwrap();
    let deserialized: ClientMessage = serde_json::from_str(&serialized).unwrap();

    match deserialized {
        ClientMessage::RejectFileTransfer { reason, .. } => {
            assert_eq!(reason, Some("File too large".to_string()));
        }
        _ => panic!("Wrong message type"),
    }
    println!("   3. RejectFileTransfer message serialization works");

    println!("✅ File Transfer Message Serialization Test Completed!");
}

#[tokio::test]
async fn test_file_transfer_progress_reporting() {
    println!("🔄 Testing File Transfer Progress Reporting...");

    // Create a test client
    let config =
        ClientConfig::new(Url::parse("https://test-server.com").unwrap()).with_debug_mode(true);
    let mut client = Client::new(config).expect("Failed to create client");

    // Create a mock active download
    let transfer_id = Uuid::new_v4();
    let sender_id = Uuid::new_v4();
    let encryption_key = [42u8; 32];
    let save_path = std::path::PathBuf::from("/tmp/test_download.txt");

    let mut active_download = ActiveDownload {
        transfer_id,
        sender_id,
        file_name: "test_file.txt".to_string(),
        file_size: 1000,
        file_hash: "abcd1234".to_string(),
        chunk_count: 10,
        encryption_key,
        save_path,
        received_chunks: std::collections::HashSet::new(),
        bytes_downloaded: 0,
        started_at: std::time::SystemTime::now(),
        expires_at: std::time::SystemTime::now() + std::time::Duration::from_secs(3600),
        temp_file: None,
    };

    // Simulate receiving some chunks
    active_download.received_chunks.insert(0);
    active_download.received_chunks.insert(1);
    active_download.received_chunks.insert(2);
    active_download.bytes_downloaded = 300;

    // Insert the download into the client
    client.active_downloads.insert(transfer_id, active_download);

    // Test progress calculation
    let progress = client.download_progress(&transfer_id).unwrap();
    assert_eq!(progress, 30.0); // 300/1000 * 100
    println!("   1. Progress calculation works correctly: {}%", progress);

    // Test active download tracking
    let active_ids = client.active_download_ids();
    assert_eq!(active_ids.len(), 1);
    assert!(active_ids.contains(&transfer_id));
    println!("   2. Active download tracking works correctly");

    // Test progress for non-existent transfer
    let non_existent_id = Uuid::new_v4();
    assert!(client.download_progress(&non_existent_id).is_none());
    println!("   3. Progress returns None for non-existent transfers");

    // Test edge case: zero file size
    let zero_size_download = client.active_downloads.get_mut(&transfer_id).unwrap();
    zero_size_download.file_size = 0;
    let progress = client.download_progress(&transfer_id).unwrap();
    assert_eq!(progress, 100.0); // Should return 100% for zero-size files
    println!("   4. Zero-size file progress handled correctly");

    println!("✅ File Transfer Progress Reporting Test Completed!");
}

#[tokio::test]
async fn test_file_transfer_events() {
    println!("🔄 Testing File Transfer Events...");

    // Test FileTransferProgress event serialization
    let progress_event = ClientEvent::FileTransferProgress {
        transfer_id: Uuid::new_v4(),
        chunks_processed: 5,
        total_chunks: 10,
        bytes_downloaded: 500,
        total_bytes: 1000,
        progress_percentage: 50.0,
    };

    let serialized = serde_json::to_string(&progress_event).unwrap();
    let deserialized: ClientEvent = serde_json::from_str(&serialized).unwrap();

    match deserialized {
        ClientEvent::FileTransferProgress {
            progress_percentage,
            .. // Ignore other fields
        } => {
            assert_eq!(progress_percentage, 50.0);
        }
        _ => panic!("Wrong event type"),
    }
    println!("   1. FileTransferProgress event serialization works");

    // Test FileTransferComplete event serialization
    let complete_event = ClientEvent::FileTransferComplete {
        transfer_id: Uuid::new_v4(),
        file_name: "test.txt".to_string(),
        file_path: "/tmp/test.txt".to_string(),
        file_size: 1024,
        transfer_duration: std::time::Duration::from_secs(30),
    };

    let serialized = serde_json::to_string(&complete_event).unwrap();
    let deserialized: ClientEvent = serde_json::from_str(&serialized).unwrap();

    match deserialized {
        ClientEvent::FileTransferComplete {
            file_name,
            file_size,
            .. // Ignore other fields
        } => {
            assert_eq!(file_name, "test.txt");
            assert_eq!(file_size, 1024);
        }
        _ => panic!("Wrong event type"),
    }
    println!("   2. FileTransferComplete event serialization works");

    // Test FileTransferFailed event serialization
    let failed_event = ClientEvent::FileTransferFailed {
        transfer_id: Uuid::new_v4(),
        file_name: Some("failed.txt".to_string()),
        error_code: "NETWORK_ERROR".to_string(),
        error_message: "Connection lost".to_string(),
        chunks_received: 3,
        total_chunks: 10,
    };

    let serialized = serde_json::to_string(&failed_event).unwrap();
    let deserialized: ClientEvent = serde_json::from_str(&serialized).unwrap();

    match deserialized {
        ClientEvent::FileTransferFailed {
            error_code,
            chunks_received,
            .. // Ignore other fields
        } => {
            assert_eq!(error_code, "NETWORK_ERROR");
            assert_eq!(chunks_received, 3);
        }
        _ => panic!("Wrong event type"),
    }
    println!("   3. FileTransferFailed event serialization works");

    println!("✅ File Transfer Events Test Completed!");
}

#[tokio::test]
async fn test_end_to_end_state_persistence_integration() {
    println!("🔄 Starting End-to-End State Persistence Integration Test...");

    // Step 1: Create a temporary file path for state storage
    let temp_dir = std::env::temp_dir();
    let state_file_path = temp_dir.join("test_client_state_integration.json");

    // Ensure the file doesn't exist from previous test runs
    let _ = tokio::fs::remove_file(&state_file_path).await;

    // Step 2: Create and initialize the first client
    let server_url = Url::parse("https://test-server.example.com").expect("Valid URL");
    let config = ClientConfig::new(server_url)
        .with_display_name("Integration Test Client".to_string())
        .with_max_prekeys(75)
        .with_max_skip_keys(750)
        .with_debug_mode(true);

    let mut client1 = Client::new(config.clone()).expect("Failed to create client1");

    // Initialize the client with identity keys
    client1
        .initialize()
        .await
        .expect("Failed to initialize client1");
    assert!(client1.is_initialized());
    println!("   1. Created and initialized first client");

    // Step 3: Add some session data to make the state more interesting
    let remote_clients = [Uuid::new_v4(), Uuid::new_v4()];

    for (i, &remote_client_id) in remote_clients.iter().enumerate() {
        let root_key = [(i as u8 + 1) * 20; 32]; // Different root keys for each session
        let dh_key = KeyPair::generate().expect("Failed to generate DH key");
        let remote_key = KeyPair::generate()
            .expect("Failed to generate remote key")
            .public_key();

        let protocol_state = ProtocolState::new_with_metadata(
            root_key,
            dh_key.clone(),
            Some(remote_key),
            750,
            Some(remote_key),          // Use as identity key
            Some(dh_key.public_key()), // Use as local identity key
            Some(remote_key),          // Use as signed prekey
            None,                      // No onetime prekey
            Some(format!("integration-session-{}", i + 1)),
        );

        // Convert to session state and simulate some session activity
        let mut session = protocol_state.to_session_state();
        session.sending_message_number = (i as u32 + 1) * 7;
        session.receiving_message_number = (i as u32 + 1) * 4;
        session.update_activity();

        client1.set_session(remote_client_id, session);
    }

    assert_eq!(client1.session_count(), 2);
    println!(
        "   2. Added {} sessions to first client",
        remote_clients.len()
    );

    // Step 4: Save the client state to file
    client1
        .save_state(&state_file_path)
        .await
        .expect("Failed to save client state");

    // Verify the file was created and has content
    let file_metadata = tokio::fs::metadata(&state_file_path)
        .await
        .expect("State file should exist");
    assert!(file_metadata.len() > 0);
    println!(
        "   3. Saved client state to file: {} bytes",
        file_metadata.len()
    );

    // Step 5: Load the state into a new client instance
    let client2 = Client::load_state(&state_file_path, config.clone())
        .await
        .expect("Failed to load client state");

    println!("   4. Loaded client state from file into new client instance");

    // Step 6: Verify that all state was preserved correctly

    // Check client initialization status
    assert!(client2.is_initialized());
    assert_eq!(client2.session_count(), 2);
    println!("   5a. Client initialization status preserved");

    // Check identity keys are identical
    assert!(client1.identity_key.is_some());
    assert!(client2.identity_key.is_some());
    assert_eq!(
        client1
            .identity_key
            .as_ref()
            .unwrap()
            .public_key()
            .as_bytes(),
        client2
            .identity_key
            .as_ref()
            .unwrap()
            .public_key()
            .as_bytes()
    );
    assert_eq!(
        client1
            .identity_key
            .as_ref()
            .unwrap()
            .private_key()
            .as_bytes(),
        client2
            .identity_key
            .as_ref()
            .unwrap()
            .private_key()
            .as_bytes()
    );
    println!("   5b. Identity keys preserved exactly");

    // Check signing keys are identical
    assert!(client1.signing_key.is_some());
    assert!(client2.signing_key.is_some());
    assert_eq!(
        client1.signing_key.as_ref().unwrap().verifying_key_bytes(),
        client2.signing_key.as_ref().unwrap().verifying_key_bytes()
    );
    assert_eq!(
        client1.signing_key.as_ref().unwrap().signing_key.to_bytes(),
        client2.signing_key.as_ref().unwrap().signing_key.to_bytes()
    );
    println!("   5c. Signing keys preserved exactly");

    // Check configuration preservation
    assert_eq!(client1.config().client_id, client2.config().client_id);
    assert_eq!(client1.config().display_name, client2.config().display_name);
    assert_eq!(client1.config().max_prekeys, client2.config().max_prekeys);
    assert_eq!(
        client1.config().max_skip_keys,
        client2.config().max_skip_keys
    );
    println!("   5d. Configuration preserved");

    // Check all sessions in detail
    for (i, &remote_client_id) in remote_clients.iter().enumerate() {
        let state1 = client1
            .get_session(&remote_client_id)
            .expect("Original client should have session");
        let state2 = client2
            .get_session(&remote_client_id)
            .expect("Loaded client should have session");

        // Verify cryptographic state
        assert_eq!(state1.root_key, state2.root_key);
        assert_eq!(
            state1.dh_ratchet_key.public_key().as_bytes(),
            state2.dh_ratchet_key.public_key().as_bytes()
        );
        assert_eq!(
            state1.dh_ratchet_key.private_key().as_bytes(),
            state2.dh_ratchet_key.private_key().as_bytes()
        );
        assert_eq!(state1.remote_dh_public_key, state2.remote_dh_public_key);
        assert_eq!(state1.sending_chain_key, state2.sending_chain_key);
        assert_eq!(state1.receiving_chain_key, state2.receiving_chain_key);

        // Verify message counters
        assert_eq!(state1.sending_message_number, state2.sending_message_number);
        assert_eq!(
            state1.receiving_message_number,
            state2.receiving_message_number
        );
        assert_eq!(state1.previous_chain_length, state2.previous_chain_length);

        // Verify metadata
        assert_eq!(state1.max_skip, state2.max_skip);
        assert_eq!(state1.remote_identity_key, state2.remote_identity_key);
        assert_eq!(state1.local_identity_key, state2.local_identity_key);
        assert_eq!(state1.session_id, state2.session_id);
        assert_eq!(state1.created_at, state2.created_at);
        assert_eq!(state1.last_activity, state2.last_activity);

        // Verify skipped message keys
        assert_eq!(state1.skipped_message_keys, state2.skipped_message_keys);

        println!("   5e. Session {} state fully preserved", i + 1);
    }

    // Step 7: Test that the loaded client can save state again (round-trip test)
    let second_state_file_path = temp_dir.join("test_client_state_integration_round2.json");
    let _ = tokio::fs::remove_file(&second_state_file_path).await;

    client2
        .save_state(&second_state_file_path)
        .await
        .expect("Failed to save loaded client state");

    // Verify the second file was created
    let second_file_metadata = tokio::fs::metadata(&second_state_file_path)
        .await
        .expect("Second state file should exist");
    assert!(second_file_metadata.len() > 0);
    println!(
        "   6. Loaded client can save state again: {} bytes",
        second_file_metadata.len()
    );

    // Step 8: Load from the second save and verify consistency
    let client3 = Client::load_state(&second_state_file_path, config)
        .await
        .expect("Failed to load from second save");

    assert_eq!(client2.session_count(), client3.session_count());
    assert_eq!(
        client2
            .identity_key
            .clone()
            .unwrap()
            .public_key()
            .as_bytes(),
        client3
            .identity_key
            .clone()
            .unwrap()
            .public_key()
            .as_bytes()
    );

    // Verify sessions are still identical after second round-trip
    for &remote_client_id in &remote_clients {
        let state2 = client2.get_session(&remote_client_id).unwrap();
        let state3 = client3.get_session(&remote_client_id).unwrap();
        assert_eq!(state2.root_key, state3.root_key);
        assert_eq!(state2.sending_message_number, state3.sending_message_number);
        assert_eq!(state2.session_id, state3.session_id);
    }
    println!("   7. Second round-trip produces consistent state");

    // Step 9: Test error cases

    // Test loading from non-existent file
    let non_existent_path = temp_dir.join("non_existent_state.json");
    let result = Client::load_state(&non_existent_path, ClientConfig::default()).await;
    assert!(result.is_err());
    assert!(matches!(
        result.unwrap_err(),
        ClientError::FileNotFound { .. }
    ));
    println!("   8a. Correctly handles non-existent file");

    // Test loading with mismatched client ID
    let different_config = ClientConfig::default(); // Different client ID
    let result = Client::load_state(&state_file_path, different_config).await;
    assert!(result.is_err());
    assert!(matches!(
        result.unwrap_err(),
        ClientError::StateIncompatible { .. }
    ));
    println!("   8b. Correctly handles client ID mismatch");

    // Test loading corrupted state file
    let corrupted_file_path = temp_dir.join("corrupted_state.json");
    tokio::fs::write(&corrupted_file_path, b"{ invalid json }")
        .await
        .unwrap();
    let result = Client::load_state(&corrupted_file_path, ClientConfig::default()).await;
    assert!(result.is_err());
    assert!(matches!(
        result.unwrap_err(),
        ClientError::StateCorrupted { .. }
    ));
    println!("   8c. Correctly handles corrupted state file");

    // Step 10: Clean up temporary files
    let _ = tokio::fs::remove_file(&state_file_path).await;
    let _ = tokio::fs::remove_file(&second_state_file_path).await;
    let _ = tokio::fs::remove_file(&corrupted_file_path).await;
    println!("   9. Cleaned up temporary files");

    println!("✅ End-to-End State Persistence Integration Test Completed Successfully!");
    println!("   - File-based state saving: ✓");
    println!("   - File-based state loading: ✓");
    println!("   - Identity key preservation: ✓");
    println!("   - Signing key preservation: ✓");
    println!("   - Session state preservation: ✓");
    println!("   - Configuration preservation: ✓");
    println!("   - Round-trip consistency: ✓");
    println!("   - Error handling: ✓");
    println!("   - File cleanup: ✓");
}

#[tokio::test]
async fn test_file_hash_verification() {
    println!("🔄 Testing File Hash Verification...");

    let config = ClientConfig::new(Url::parse("https://test-server.com").unwrap());
    let client = Client::new(config).expect("Failed to create client");

    // Create a temporary test file
    let test_content = b"Hello, world! This is a test file for hash verification.";
    let temp_file_path = std::path::Path::new("/tmp/test_hash_file.txt");

    tokio::fs::write(&temp_file_path, test_content)
        .await
        .unwrap();

    // Calculate expected SHA-256 hash
    use sha2::{Digest, Sha256};
    let mut hasher = <Sha256 as Digest>::new();
    hasher.update(test_content);
    let expected_hash = format!("{:x}", hasher.finalize());

    // Test hash verification with correct hash
    let result = client
        .verify_file_hash(&temp_file_path, &expected_hash)
        .await
        .unwrap();
    assert!(result);
    println!("   1. Hash verification passes with correct hash");

    // Test hash verification with wrong hash
    let wrong_hash = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef";
    let result = client
        .verify_file_hash(&temp_file_path, wrong_hash)
        .await
        .unwrap();
    assert!(!result);
    println!("   2. Hash verification fails with wrong hash");

    // Test case-insensitive hash comparison
    let uppercase_hash = expected_hash.to_uppercase();
    let result = client
        .verify_file_hash(&temp_file_path, &uppercase_hash)
        .await
        .unwrap();
    assert!(result);
    println!("   3. Hash verification is case-insensitive");

    // Clean up
    let _ = tokio::fs::remove_file(&temp_file_path).await;

    println!("✅ File Hash Verification Test Completed!");
}
