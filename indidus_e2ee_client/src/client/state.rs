use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use indidus_signal_protocol::{
    crypto::keys::{Key<PERSON>air, SigningKeyPair},
    session::Session,
};
use indidus_shared::validation::PeerId;
use crate::client::Client;
use crate::error::{ClientError, ClientResult};
use super::config::ClientConfig;

/// Internal structure for serializing complete client state
///
/// This structure contains all the data needed to restore a client's state,
/// including identity keys, active sessions, and configuration.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub(super) struct ClientState {
    /// The client's unique identifier
    pub(super) client_id: Uuid,

    /// The client's identity key pair (if initialized)
    pub(super) identity_key: Option<KeyPair>,

    /// The client's signing key bytes (if initialized)
    pub(super) signing_key_bytes: Option<[u8; 32]>,

    /// All active sessions keyed by remote client ID
    pub(super) sessions: HashMap<PeerId, Session>,

    /// Client configuration
    pub(super) config: ClientConfig,
}

impl Client {
    /// Create a new client instance with optional saved state restoration
    ///
    /// This method creates a client instance and optionally restores it from
    /// a previously saved state blob. If `saved_state` is provided, the client
    /// will be initialized with all the cryptographic material and sessions
    /// from the saved state.
    ///
    /// # Arguments
    /// * `config` - The client configuration
    /// * `saved_state` - Optional state blob from a previous `save_state()` call
    ///
    /// # Returns
    /// A new `Client` instance, optionally restored from saved state
    ///
    /// # Errors
    /// - `SdkError::Serialization` if the saved state blob is invalid or corrupted
    /// - `SdkError::InvalidConfig` if the saved state is incompatible with the config
    ///
    /// # Example
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    ///
    /// // Create a fresh client
    /// let fresh_client = Client::new_with_state(config.clone(), None)?;
    ///
    /// // Restore from saved state
    /// let saved_state = std::fs::read("client_state.bin")?;
    /// let restored_client = Client::new_with_state(config, Some(saved_state))?;
    /// # Ok(())
    /// # }
    /// ```
    pub fn new_with_state(config: ClientConfig, saved_state: Option<Vec<u8>>) -> ClientResult<Self> {
        // Validate the configuration first
        super::config::validate_configuration(&config)?;

        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(
                config.connection_timeout_secs,
            ))
            .build()
            .map_err(|e| {
                ClientError::internal_error(format!("Failed to create HTTP client: {}", e))
            })?;

        // If no saved state, create a fresh client
        let Some(state_blob) = saved_state else {
            // Create the API client for structured server communication
            let api_client = super::api::ApiClient::new(config.server_url.to_string())?;
            
            return Ok(Self {
                config,
                identity_key: None,
                signing_key: None,
                signed_prekey: None,
                onetime_prekeys: Vec::new(),
                sessions: HashMap::new(),
                http_client,
                api_client,
                connection_state: super::connection::ConnectionState::Disconnected,
                event_receiver: None,
                message_sender: None,
                event_sender: None,
                event_loop_handle: None,
                active_downloads: HashMap::new(),
            });
        };

        // Validate state blob size and basic structure
        if state_blob.is_empty() {
            return Err(ClientError::state_corrupted(
                "Saved state is empty. Cannot restore client from empty state data."
            ));
        }

        if state_blob.len() > 50 * 1024 * 1024 { // 50MB limit
            return Err(ClientError::state_corrupted(
                format!("Saved state is too large ({} bytes). Maximum allowed size is 50MB. The state data may be corrupted.", state_blob.len())
            ));
        }

        // Deserialize the saved state with enhanced error handling
        let client_state: ClientState = serde_json::from_slice(&state_blob).map_err(|e| {
            // Provide specific guidance based on the type of deserialization error
            let error_details = match e.classify() {
                serde_json::error::Category::Io => {
                    format!("I/O error during deserialization: {}. The saved state file may be corrupted or inaccessible.", e)
                },
                serde_json::error::Category::Syntax => {
                    format!("Invalid JSON syntax in saved state: {}. The state file is corrupted or was modified externally.", e)
                },
                serde_json::error::Category::Data => {
                    format!("Invalid data structure in saved state: {}. The state may be from an incompatible version of the client.", e)
                },
                serde_json::error::Category::Eof => {
                    format!("Incomplete saved state data: {}. The state file appears to be truncated or corrupted.", e)
                },
            };
            
            ClientError::state_corrupted(error_details)
        })?;

        // Validate that the saved state is compatible with the provided config
        if client_state.client_id != config.client_id {
            return Err(ClientError::state_incompatible(format!(
                "Client ID mismatch: saved state has {}, but configuration specifies {}. Use the same client ID or create a new client.",
                client_state.client_id, config.client_id
            )));
        }

        // Validate saved state version compatibility
        // Check if the saved config has any incompatible changes
        if client_state.config.max_prekeys > config.max_prekeys * 2 {
            return Err(ClientError::state_incompatible(format!(
                "Saved state has incompatible max_prekeys setting: {} vs current {}. Consider updating your configuration or creating a new client.",
                client_state.config.max_prekeys, config.max_prekeys
            )));
        }

        // Restore the signing key from bytes if available
        let signing_key = if let Some(signing_key_bytes) = client_state.signing_key_bytes {
            let signing_key = ed25519_dalek::SigningKey::from_bytes(&signing_key_bytes);
            Some(SigningKeyPair::from_signing_key(signing_key))
        } else {
            None
        };

        // Validate session data integrity
        if client_state.sessions.len() > 10000 {
            return Err(ClientError::state_corrupted(format!(
                "Saved state contains too many sessions ({}). This may indicate data corruption. Maximum allowed is 10,000 sessions.",
                client_state.sessions.len()
            )));
        }

        // Create the API client for structured server communication
        let api_client = super::api::ApiClient::new(config.server_url.to_string())?;

        // Create the client with restored state
        Ok(Self {
            config, // Use the provided config (allows for config updates)
            identity_key: client_state.identity_key,
            signing_key,
            signed_prekey: None,
            onetime_prekeys: Vec::new(),
            sessions: client_state.sessions,
            http_client,
            api_client,
            connection_state: super::connection::ConnectionState::Disconnected, // Always start disconnected
            event_receiver: None,
            message_sender: None,
            event_sender: None,
            event_loop_handle: None,
            active_downloads: HashMap::new(),
        })
    }

    /// Save the current client state to a file
    ///
    /// This method serializes all active session states and writes them to the specified file path.
    /// The saved state contains all cryptographic material necessary to resume all active sessions.
    ///
    /// # Arguments
    /// * `path` - The file path where the state should be saved
    ///
    /// # Returns
    /// Success or error if the state cannot be saved
    ///
    /// # Errors
    /// - `ClientError::NotInitialized` if the client hasn't been initialized
    /// - `ClientError::StateSaveFailed` if serialization or file writing fails
    /// - `ClientError::FileIo` if there are file system errors
    ///
    /// # Example
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    /// let mut client = Client::new(config)?;
    /// client.initialize().await?;
    ///
    /// // Save the client state to a file
    /// client.save_state("client_state.json").await?;
    /// # Ok(())
    /// # }
    /// ```
    pub async fn save_state(&self, path: impl AsRef<std::path::Path>) -> ClientResult<()> {
        // Check if client is initialized
        if !self.is_initialized() {
            return Err(ClientError::not_initialized("save_state"));
        }

        // Create a comprehensive state structure that includes all sessions
        let client_state = ClientState {
            client_id: self.config.client_id,
            identity_key: self.identity_key.clone(),
            signing_key_bytes: self
                .signing_key
                .as_ref()
                .map(|sk| sk.signing_key.to_bytes()),
            sessions: self.sessions.clone(),
            config: self.config.clone(),
        };

        // Serialize the state to JSON with enhanced error handling
        let serialized = serde_json::to_vec_pretty(&client_state).map_err(|e| {
            // Provide specific guidance based on the type of serialization error
            let error_details = match e.classify() {
                serde_json::error::Category::Io => {
                    format!("I/O error during serialization: {}. Check available disk space and write permissions.", e)
                },
                serde_json::error::Category::Syntax => {
                    format!("Internal serialization syntax error: {}. This indicates a bug in the client library.", e)
                },
                serde_json::error::Category::Data => {
                    format!("Data serialization error: {}. Some session data may be corrupted.", e)
                },
                serde_json::error::Category::Eof => {
                    format!("Unexpected end of data during serialization: {}. This indicates a bug in the client library.", e)
                },
            };
            
            ClientError::state_save_failed(error_details)
        })?;

        // Write the serialized state to the file
        let path = path.as_ref();
        tokio::fs::write(path, &serialized).await.map_err(|e| {
            ClientError::FileIo {
                operation: "write state file".to_string(),
                path: path.display().to_string(),
                reason: e.to_string(),
            }
        })?;

        Ok(())
    }

    /// Load client state from a file and create a new client instance
    ///
    /// This static method reads a previously saved client state from the specified file path
    /// and creates a new client instance with that state restored. All cryptographic material
    /// and session data will be restored from the saved state.
    ///
    /// # Arguments
    /// * `path` - The file path containing the saved client state
    /// * `config` - The client configuration to use (must match the saved state's client ID)
    ///
    /// # Returns
    /// A new `Client` instance with the restored state
    ///
    /// # Errors
    /// - `ClientError::FileNotFound` if the state file doesn't exist
    /// - `ClientError::StateCorrupted` if the state file is invalid or corrupted
    /// - `ClientError::StateIncompatible` if the state is incompatible with the provided config
    /// - `ClientError::FileIo` if there are file system errors
    ///
    /// # Example
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    ///
    /// // Load client from saved state
    /// let client = Client::load_state("client_state.json", config).await?;
    ///
    /// // Client is now ready with restored state
    /// assert!(client.is_initialized());
    /// # Ok(())
    /// # }
    /// ```
    pub async fn load_state(
        path: impl AsRef<std::path::Path>, 
        config: ClientConfig
    ) -> ClientResult<Self> {
        // Validate the configuration first
        super::config::validate_configuration(&config)?;

        let path = path.as_ref();

        // Read the state file
        let state_blob = tokio::fs::read(path).await.map_err(|e| {
            if e.kind() == std::io::ErrorKind::NotFound {
                ClientError::FileNotFound {
                    path: path.display().to_string(),
                }
            } else {
                ClientError::FileIo {
                    operation: "read state file".to_string(),
                    path: path.display().to_string(),
                    reason: e.to_string(),
                }
            }
        })?;

        // Validate state blob size and basic structure
        if state_blob.is_empty() {
            return Err(ClientError::state_corrupted(
                "Saved state file is empty. Cannot restore client from empty state data."
            ));
        }

        if state_blob.len() > 50 * 1024 * 1024 { // 50MB limit
            return Err(ClientError::state_corrupted(
                format!("Saved state file is too large ({} bytes). Maximum allowed size is 50MB. The state data may be corrupted.", state_blob.len())
            ));
        }

        // Deserialize the saved state with enhanced error handling
        let client_state: ClientState = serde_json::from_slice(&state_blob).map_err(|e| {
            // Provide specific guidance based on the type of deserialization error
            let error_details = match e.classify() {
                serde_json::error::Category::Io => {
                    format!("I/O error during deserialization: {}. The saved state file may be corrupted or inaccessible.", e)
                },
                serde_json::error::Category::Syntax => {
                    format!("Invalid JSON syntax in saved state: {}. The state file is corrupted or was modified externally.", e)
                },
                serde_json::error::Category::Data => {
                    format!("Invalid data structure in saved state: {}. The state may be from an incompatible version of the client.", e)
                },
                serde_json::error::Category::Eof => {
                    format!("Incomplete saved state data: {}. The state file appears to be truncated or corrupted.", e)
                },
            };
            
            ClientError::state_corrupted(error_details)
        })?;

        // Validate that the saved state is compatible with the provided config
        if client_state.client_id != config.client_id {
            return Err(ClientError::state_incompatible(format!(
                "Client ID mismatch: saved state has {}, but configuration specifies {}. Use the same client ID or create a new client.",
                client_state.client_id, config.client_id
            )));
        }

        // Validate saved state version compatibility
        // Check if the saved config has any incompatible changes
        if client_state.config.max_prekeys > config.max_prekeys * 2 {
            return Err(ClientError::state_incompatible(format!(
                "Saved state has incompatible max_prekeys setting: {} vs current {}. Consider updating your configuration or creating a new client.",
                client_state.config.max_prekeys, config.max_prekeys
            )));
        }

        // Restore the signing key from bytes if available
        let signing_key = if let Some(signing_key_bytes) = client_state.signing_key_bytes {
            let signing_key = ed25519_dalek::SigningKey::from_bytes(&signing_key_bytes);
            Some(SigningKeyPair::from_signing_key(signing_key))
        } else {
            None
        };

        // Validate session data integrity
        if client_state.sessions.len() > 10000 {
            return Err(ClientError::state_corrupted(format!(
                "Saved state contains too many sessions ({}). This may indicate data corruption. Maximum allowed is 10,000 sessions.",
                client_state.sessions.len()
            )));
        }

        // Create HTTP client
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(
                config.connection_timeout_secs,
            ))
            .build()
            .map_err(|e| {
                ClientError::internal_error(format!("Failed to create HTTP client: {}", e))
            })?;

        // Create the API client for structured server communication
        let api_client = super::api::ApiClient::new(config.server_url.to_string())?;

        // Create the client with restored state
        Ok(Self {
            config, // Use the provided config (allows for config updates)
            identity_key: client_state.identity_key,
            signing_key,
            signed_prekey: None,
            onetime_prekeys: Vec::new(),
            sessions: client_state.sessions,
            http_client,
            api_client,
            connection_state: super::connection::ConnectionState::Disconnected, // Always start disconnected
            event_receiver: None,
            message_sender: None,
            event_sender: None,
            event_loop_handle: None,
            active_downloads: HashMap::new(),
        })
    }

    /// Save the current client state to an opaque byte array (legacy method)
    ///
    /// This method serializes all active session states into a single opaque blob
    /// that can be stored externally and later restored using `Client::new_with_state()`.
    /// The returned byte array contains all cryptographic state necessary to resume
    /// all active sessions.
    ///
    /// **Note**: This method is deprecated in favor of `save_state()` which writes directly to a file.
    /// Use this method only when you need the raw serialized data for custom storage.
    ///
    /// # Returns
    /// An opaque byte array containing the serialized client state
    ///
    /// # Errors
    /// - `ClientError::NotInitialized` if the client hasn't been initialized
    /// - `ClientError::StateSaveFailed` if serialization fails
    ///
    /// # Example
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    /// let client = Client::new(config).expect("Failed to create client");
    ///
    /// // ... initialize client and establish sessions ...
    ///
    /// // Save the client state to a byte array
    /// let state_blob = client.save_state_bytes()?;
    ///
    /// // Store the blob externally (database, file, etc.)
    /// std::fs::write("client_state.bin", &state_blob)?;
    /// # Ok(())
    /// # }
    /// ```
    pub fn save_state_bytes(&self) -> ClientResult<Vec<u8>> {
        // Check if client is initialized
        if !self.is_initialized() {
            return Err(ClientError::not_initialized("save_state"));
        }

        // Create a comprehensive state structure that includes all sessions
        let client_state = ClientState {
            client_id: self.config.client_id,
            identity_key: self.identity_key.clone(),
            signing_key_bytes: self
                .signing_key
                .as_ref()
                .map(|sk| sk.signing_key.to_bytes()),
            sessions: self.sessions.clone(),
            config: self.config.clone(),
        };

        // Serialize the state to JSON with enhanced error handling
        let serialized = serde_json::to_vec(&client_state).map_err(|e| {
            // Provide specific guidance based on the type of serialization error
            let error_details = match e.classify() {
                serde_json::error::Category::Io => {
                    format!("I/O error during serialization: {}. Check available disk space and write permissions.", e)
                },
                serde_json::error::Category::Syntax => {
                    format!("Internal serialization syntax error: {}. This indicates a bug in the client library.", e)
                },
                serde_json::error::Category::Data => {
                    format!("Data serialization error: {}. Some session data may be corrupted.", e)
                },
                serde_json::error::Category::Eof => {
                    format!("Unexpected end of data during serialization: {}. This indicates a bug in the client library.", e)
                },
            };
            
            ClientError::state_save_failed(error_details)
        })?;

        Ok(serialized)
    }

    /// Save the current client state to a binary format using bincode
    ///
    /// This method provides a more compact and performant alternative to JSON
    /// for client state persistence. It serializes all active session states
    /// into a binary format that can be stored externally and later restored.
    ///
    /// # Returns
    /// A binary byte array containing the serialized client state
    ///
    /// # Errors
    /// - `ClientError::NotInitialized` if the client hasn't been initialized
    /// - `ClientError::StateSaveFailed` if binary serialization fails
    ///
    /// # Example
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    /// let client = Client::new(config).expect("Failed to create client");
    ///
    /// // ... initialize client and establish sessions ...
    ///
    /// // Save the client state to a binary format
    /// let state_blob = client.save_state_binary()?;
    ///
    /// // Store the blob externally (database, file, etc.)
    /// std::fs::write("client_state.bin", &state_blob)?;
    /// # Ok(())
    /// # }
    /// ```
    pub fn save_state_binary(&self) -> ClientResult<Vec<u8>> {
        // Check if client is initialized
        if !self.is_initialized() {
            return Err(ClientError::not_initialized("save_state_binary"));
        }

        // Create a comprehensive state structure that includes all sessions
        let client_state = ClientState {
            client_id: self.config.client_id,
            identity_key: self.identity_key.clone(),
            signing_key_bytes: self
                .signing_key
                .as_ref()
                .map(|sk| sk.signing_key.to_bytes()),
            sessions: self.sessions.clone(),
            config: self.config.clone(),
        };

        // Serialize the state to binary format using bincode
        let serialized = bincode::serialize(&client_state).map_err(|e| {
            ClientError::state_save_failed(format!(
                "Binary serialization failed: {}. The client state may contain incompatible data structures.",
                e
            ))
        })?;

        Ok(serialized)
    }

    /// Load client state from binary data and create a new client instance
    ///
    /// This method reads binary-serialized client state data and creates a new client
    /// instance with that state restored. All cryptographic material and session data
    /// will be restored from the binary state. This provides a more compact and performant
    /// alternative to JSON-based state restoration.
    ///
    /// # Arguments
    /// * `data` - The binary data containing the serialized client state
    /// * `config` - The client configuration to use (must match the saved state's client ID)
    ///
    /// # Returns
    /// A new `Client` instance with the restored state
    ///
    /// # Errors
    /// - `ClientError::StateCorrupted` if the binary data is invalid or corrupted
    /// - `ClientError::StateIncompatible` if the state is incompatible with the provided config
    /// - `ClientError::InvalidConfig` if the provided configuration is invalid
    ///
    /// # Example
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    ///
    /// // Load client from binary state
    /// let binary_data = std::fs::read("client_state.bin")?;
    /// let client = Client::load_state_from_binary(&binary_data, config)?;
    ///
    /// // Client is now ready with restored state
    /// assert!(client.is_initialized());
    /// # Ok(())
    /// # }
    /// ```
    pub fn load_state_from_binary(data: &[u8], config: ClientConfig) -> ClientResult<Self> {
        // Validate the configuration first
        super::config::validate_configuration(&config)?;

        // Validate binary data size and basic structure
        if data.is_empty() {
            return Err(ClientError::state_corrupted(
                "Binary state data is empty. Cannot restore client from empty state data."
            ));
        }

        if data.len() > 50 * 1024 * 1024 { // 50MB limit
            return Err(ClientError::state_corrupted(
                format!("Binary state data is too large ({} bytes). Maximum allowed size is 50MB. The state data may be corrupted.", data.len())
            ));
        }

        // Deserialize the saved state with enhanced error handling
        let client_state: ClientState = bincode::deserialize(data).map_err(|e| {
            ClientError::state_corrupted(format!(
                "Binary deserialization failed: {}. The state data may be corrupted or from an incompatible version.",
                e
            ))
        })?;

        // Validate that the saved state is compatible with the provided config
        if client_state.client_id != config.client_id {
            return Err(ClientError::state_incompatible(format!(
                "Client ID mismatch: saved state has {}, but configuration specifies {}. Use the same client ID or create a new client.",
                client_state.client_id, config.client_id
            )));
        }

        // Validate saved state version compatibility
        // Check if the saved config has any incompatible changes
        if client_state.config.max_prekeys > config.max_prekeys * 2 {
            return Err(ClientError::state_incompatible(format!(
                "Saved state has incompatible max_prekeys setting: {} vs current {}. Consider updating your configuration or creating a new client.",
                client_state.config.max_prekeys, config.max_prekeys
            )));
        }

        // Restore the signing key from bytes if available
        let signing_key = if let Some(signing_key_bytes) = client_state.signing_key_bytes {
            let signing_key = ed25519_dalek::SigningKey::from_bytes(&signing_key_bytes);
            Some(SigningKeyPair::from_signing_key(signing_key))
        } else {
            None
        };

        // Validate session data integrity
        if client_state.sessions.len() > 10000 {
            return Err(ClientError::state_corrupted(format!(
                "Saved state contains too many sessions ({}). This may indicate data corruption. Maximum allowed is 10,000 sessions.",
                client_state.sessions.len()
            )));
        }

        // Create HTTP client
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(
                config.connection_timeout_secs,
            ))
            .build()
            .map_err(|e| {
                ClientError::internal_error(format!("Failed to create HTTP client: {}", e))
            })?;

        // Create the API client for structured server communication
        let api_client = super::api::ApiClient::new(config.server_url.to_string())?;

        // Create the client with restored state
        Ok(Self {
            config, // Use the provided config (allows for config updates)
            identity_key: client_state.identity_key,
            signing_key,
            signed_prekey: None,
            onetime_prekeys: Vec::new(),
            sessions: client_state.sessions,
            http_client,
            api_client,
            connection_state: super::connection::ConnectionState::Disconnected, // Always start disconnected
            event_receiver: None,
            message_sender: None,
            event_sender: None,
            event_loop_handle: None,
            active_downloads: HashMap::new(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use url::Url;

    #[tokio::test]
    async fn test_binary_serialization_round_trip() {
        // Create a test configuration
        let config = ClientConfig::new(Url::parse("https://test-server.com").unwrap())
            .with_display_name("Test Client".to_string())
            .with_max_prekeys(50)
            .with_max_skip_keys(500);

        // Create a client and initialize it
        let mut original_client = Client::new(config.clone()).expect("Failed to create client");
        original_client.initialize().await.expect("Failed to initialize client");

        // Verify the client is initialized
        assert!(original_client.is_initialized(), "Client should be initialized");

        // Save the client state to binary format
        let binary_data = original_client
            .save_state_binary()
            .expect("Failed to save state to binary");

        // Verify binary data is not empty
        assert!(!binary_data.is_empty(), "Binary data should not be empty");

        // Load the client state from binary data
        let restored_client = Client::load_state_from_binary(&binary_data, config)
            .expect("Failed to load state from binary");

        // Verify the restored client is initialized
        assert!(restored_client.is_initialized(), "Restored client should be initialized");

        // Compare key state properties
        assert_eq!(
            original_client.config.client_id,
            restored_client.config.client_id,
            "Client IDs should match"
        );

        assert_eq!(
            original_client.config.display_name,
            restored_client.config.display_name,
            "Display names should match"
        );

        assert_eq!(
            original_client.config.max_prekeys,
            restored_client.config.max_prekeys,
            "Max prekeys should match"
        );

        // Verify identity keys match (if present)
        match (&original_client.identity_key, &restored_client.identity_key) {
            (Some(orig), Some(restored)) => {
                assert_eq!(
                    orig.public_key().as_bytes(),
                    restored.public_key().as_bytes(),
                    "Identity public keys should match"
                );
                assert_eq!(
                    orig.private_key().as_bytes(),
                    restored.private_key().as_bytes(),
                    "Identity private keys should match"
                );
            }
            (None, None) => {
                // Both are None, which is fine
            }
            _ => panic!("Identity key presence mismatch between original and restored client"),
        }

        // Verify signing keys match (if present)
        match (&original_client.signing_key, &restored_client.signing_key) {
            (Some(orig), Some(restored)) => {
                assert_eq!(
                    orig.signing_key.to_bytes(),
                    restored.signing_key.to_bytes(),
                    "Signing keys should match"
                );
                assert_eq!(
                    orig.verifying_key.to_bytes(),
                    restored.verifying_key.to_bytes(),
                    "Verifying keys should match"
                );
            }
            (None, None) => {
                // Both are None, which is fine
            }
            _ => panic!("Signing key presence mismatch between original and restored client"),
        }

        // Verify sessions match
        assert_eq!(
            original_client.sessions.len(),
            restored_client.sessions.len(),
            "Session count should match"
        );

        // Test that we can perform another round-trip
        let second_binary_data = restored_client
            .save_state_binary()
            .expect("Failed to save restored state to binary");

        // The binary data should be identical (deterministic serialization)
        assert_eq!(
            binary_data.len(),
            second_binary_data.len(),
            "Binary data length should be consistent across round-trips"
        );
    }

    #[test]
    fn test_binary_serialization_error_handling() {
        let config = ClientConfig::new(Url::parse("https://test-server.com").unwrap());

        // Test empty data
        let result = Client::load_state_from_binary(&[], config.clone());
        assert!(result.is_err(), "Should fail with empty data");
        assert!(result.unwrap_err().to_string().contains("empty"));

        // Test invalid binary data
        let invalid_data = vec![0xFF; 100]; // Random bytes
        let result = Client::load_state_from_binary(&invalid_data, config.clone());
        assert!(result.is_err(), "Should fail with invalid binary data");

        // Test oversized data
        let oversized_data = vec![0u8; 51 * 1024 * 1024]; // 51MB
        let result = Client::load_state_from_binary(&oversized_data, config);
        assert!(result.is_err(), "Should fail with oversized data");
        assert!(result.unwrap_err().to_string().contains("too large"));
    }
}
