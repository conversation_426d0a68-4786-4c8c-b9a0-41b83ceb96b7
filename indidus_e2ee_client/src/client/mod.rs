//! Core client structures for the Indidus E2EE Client SDK
//!
//! This module defines the main `Client` and `ClientConfig` structures that form
//! the foundation of the client SDK for end-to-end encrypted messaging.

use std::collections::HashMap;

use indidus_signal_protocol::{
    crypto::keys::{KeyPair, SigningKeyPair},
    Session,
};
use indidus_shared::validation::PeerId;
use tokio::sync::mpsc;
use uuid::Uuid;

use crate::error::{ClientError, ClientResult};

// Module declarations
pub mod api;
pub mod config;
pub mod connection;
pub mod events;
pub mod file_transfer;
pub mod messaging;
pub mod session;
pub mod state;

#[cfg(test)]
mod tests;

// Re-export important types
pub use api::ApiClient;
pub use config::ClientConfig;
pub use connection::ConnectionState;
pub use events::ClientEvent;
pub use file_transfer::{FileChunk, FileChunkIterator, FileTransferStatus, TransferStatus};

use self::file_transfer::ActiveDownload;

/// The main client structure for the Indidus E2EE Client SDK
///
/// This struct represents an active client instance that can establish sessions,
/// send and receive encrypted messages, and manage cryptographic state.
#[derive(Debug)]
pub struct Client {
    /// Client configuration
    pub(super) config: ClientConfig,

    /// The client's long-term identity key pair
    pub(super) identity_key: Option<KeyPair>,

    /// The client's signing key pair for authentication
    pub(super) signing_key: Option<SigningKeyPair>,

    /// The client's signed pre-key for X3DH
    pub(super) signed_prekey: Option<KeyPair>,

    /// The client's one-time pre-keys for X3DH
    pub(super) onetime_prekeys: Vec<KeyPair>,

    /// Active sessions with other clients (keyed by remote client ID)
    pub(super) sessions: HashMap<PeerId, Session>,

    /// HTTP client for server communication
    #[allow(dead_code)]
    pub(super) http_client: reqwest::Client,

    /// API client for structured server communication
    pub(super) api_client: ApiClient,

    /// Current connection state
    pub(super) connection_state: ConnectionState,

    /// Event receiver for incoming events from the WebSocket connection
    pub(super) event_receiver: Option<mpsc::Receiver<ClientEvent>>,

    /// Event sender for sending messages to the WebSocket connection
    pub(super) message_sender: Option<mpsc::Sender<messaging::ClientMessage>>,

    /// Event sender for emitting client events (for progress reporting)
    pub(super) event_sender: Option<mpsc::Sender<ClientEvent>>,

    /// Handle to the event loop task
    pub(super) event_loop_handle: Option<tokio::task::JoinHandle<()>>,

    /// Active file downloads (keyed by transfer ID)
    pub(super) active_downloads: HashMap<Uuid, ActiveDownload>,
}

impl Client {
    /// Create a new client instance with the given configuration
    ///
    /// This method creates a fresh client instance without any saved state.
    /// For restoring from a saved state, use `Client::new_with_state()`.
    ///
    /// The configuration is thoroughly validated before creating the client instance.
    /// This ensures that any configuration issues are caught early with clear error messages.
    ///
    /// # Arguments
    /// * `config` - The client configuration
    ///
    /// # Returns
    /// A new `Client` instance ready for initialization
    ///
    /// # Errors
    /// Returns `ClientError::InvalidConfiguration` if any configuration parameter is invalid:
    /// - Invalid server URL scheme (must be https:// or wss://)
    /// - Invalid server URL host (must not be empty)
    /// - Invalid timeout values (must be reasonable)
    /// - Invalid pre-key or skip key limits (must be within bounds)
    /// - Invalid display name (if provided, must not be empty or too long)
    ///
    /// # Examples
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    /// let client = Client::new(config)?;
    /// # Ok(())
    /// # }
    /// ```
    pub fn new(config: ClientConfig) -> ClientResult<Self> {
        // Validate the configuration thoroughly
        config::validate_configuration(&config)?;

        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(
                config.connection_timeout_secs,
            ))
            .build()
            .map_err(|e| {
                ClientError::internal_error(format!("Failed to create HTTP client: {}", e))
            })?;

        // Create the API client for structured server communication
        let api_client = ApiClient::new(config.server_url.to_string())?;

        Ok(Self {
            config,
            identity_key: None,
            signing_key: None,
            signed_prekey: None,
            onetime_prekeys: Vec::new(),
            sessions: HashMap::new(),
            http_client,
            api_client,
            connection_state: ConnectionState::Disconnected,
            event_receiver: None,
            message_sender: None,
            event_sender: None,
            event_loop_handle: None,
            active_downloads: HashMap::new(),
        })
    }

    /// Get the client's configuration
    pub fn config(&self) -> &ClientConfig {
        &self.config
    }

    /// Get the client's unique identifier
    pub fn client_id(&self) -> Uuid {
        self.config.client_id
    }

    /// Check if the client has been initialized with identity keys
    pub fn is_initialized(&self) -> bool {
        self.identity_key.is_some() && self.signing_key.is_some()
    }

    /// Check if the client is connected to the server
    pub fn is_connected(&self) -> bool {
        matches!(self.connection_state, ConnectionState::Connected)
    }

    /// Get the current connection state
    pub fn connection_state(&self) -> &ConnectionState {
        &self.connection_state
    }

    /// Initialize the client by generating identity keys if they don't already exist
    ///
    /// This method is a key part of the ergonomic API and handles the generation of the
    /// client's identity keys if no prior state has been loaded. It checks if the client's
    /// identity keypair is already present and, if not, generates a new one using the
    /// underlying crypto primitives.
    ///
    /// The method is idempotent - calling it multiple times will not regenerate existing keys.
    ///
    /// # Returns
    /// Success if initialization completes, or an error if key generation fails
    ///
    /// # Errors
    /// - `ClientError::internal_error` if key generation fails due to cryptographic errors
    ///
    /// # Example
    /// ```rust,no_run
    /// # use indidus_e2ee_client::{Client, ClientConfig};
    /// # use url::Url;
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let config = ClientConfig::new(Url::parse("https://server.com")?);
    /// let mut client = Client::new(config)?;
    ///
    /// // Initialize the client with identity keys
    /// client.initialize().await?;
    ///
    /// // Client is now ready for secure communication
    /// assert!(client.is_initialized());
    /// # Ok(())
    /// # }
    /// ```
    pub async fn initialize(&mut self) -> ClientResult<()> {
        // Check if identity keypair already exists
        if self.identity_key.is_none() {
            // Generate a new identity keypair using the underlying crypto primitives
            let identity_keypair = KeyPair::generate().map_err(|e| {
                ClientError::internal_error(format!(
                    "Failed to generate identity keypair: {}. This indicates a cryptographic system error.",
                    e
                ))
            })?;

            // Store the generated identity keypair
            self.identity_key = Some(identity_keypair);

            if self.config.debug_mode {
                println!("Generated new identity keypair for client {}", self.config.client_id);
            }
        } else if self.config.debug_mode {
            println!("Identity keypair already exists for client {}, skipping generation", self.config.client_id);
        }

        // Check if signing key already exists
        if self.signing_key.is_none() {
            // Generate a new signing keypair using the underlying crypto primitives
            let signing_keypair = SigningKeyPair::generate().map_err(|e| {
                ClientError::internal_error(format!(
                    "Failed to generate signing keypair: {}. This indicates a cryptographic system error.",
                    e
                ))
            })?;

            // Store the generated signing keypair
            self.signing_key = Some(signing_keypair);

            if self.config.debug_mode {
                println!("Generated new signing keypair for client {}", self.config.client_id);
            }
        } else if self.config.debug_mode {
            println!("Signing keypair already exists for client {}, skipping generation", self.config.client_id);
        }

        // Verify that both keys are now present
        if self.identity_key.is_some() && self.signing_key.is_some() {
            if self.config.debug_mode {
                println!("Client {} initialization completed successfully", self.config.client_id);
            }
            Ok(())
        } else {
            // This should never happen given the logic above, but we check for safety
            Err(ClientError::internal_error(
                "Client initialization failed: one or both keypairs are missing after generation attempt".to_string()
            ))
        }
    }

    /// Get the number of active sessions
    pub fn session_count(&self) -> usize {
        self.sessions.len()
    }
}