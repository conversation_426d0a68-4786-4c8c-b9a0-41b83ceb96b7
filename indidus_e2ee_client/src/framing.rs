//! # Message Framing
//!
//! This module defines the on-the-wire data format for encrypted messages.
//! The `EncryptedMessageFrame` struct encapsulates all the necessary components
//! for transmitting encrypted messages over the network.

use serde::{Deserialize, Serialize};

/// Represents the on-the-wire format for encrypted messages.
///
/// This struct contains all the cryptographic components needed to transmit
/// an encrypted message, including the recipient's key identifier, ephemeral
/// key for the key exchange, the encrypted payload, and the nonce used for
/// encryption.
#[derive(Serialize, Deserialize, Debug, PartialEq)]
pub struct EncryptedMessageFrame {
    /// The recipient's public key identifier used to determine which key
    /// should be used for decryption.
    pub recipient_key: Vec<u8>,
    
    /// The ephemeral public key generated for this specific message.
    /// This is used in the key exchange process to derive the shared secret.
    pub ephemeral_key: Vec<u8>,
    
    /// The encrypted message payload. This contains the actual message
    /// content that has been encrypted using the derived shared secret.
    pub ciphertext: Vec<u8>,
    
    /// The nonce (number used once) that was used during the encryption
    /// process. This ensures that identical plaintexts produce different
    /// ciphertexts.
    pub nonce: Vec<u8>,
}

impl EncryptedMessageFrame {
    /// Creates a new `EncryptedMessageFrame` with the provided components.
    ///
    /// # Arguments
    ///
    /// * `recipient_key` - The recipient's public key identifier
    /// * `ephemeral_key` - The ephemeral public key for this message
    /// * `ciphertext` - The encrypted message payload
    /// * `nonce` - The nonce used for encryption
    ///
    /// # Returns
    ///
    /// A new `EncryptedMessageFrame` instance.
    pub fn new(
        recipient_key: Vec<u8>,
        ephemeral_key: Vec<u8>,
        ciphertext: Vec<u8>,
        nonce: Vec<u8>,
    ) -> Self {
        Self {
            recipient_key,
            ephemeral_key,
            ciphertext,
            nonce,
        }
    }

    /// Serializes the `EncryptedMessageFrame` to a binary format.
    ///
    /// This method uses the `bincode` library to serialize the struct into
    /// a compact binary representation suitable for network transmission.
    ///
    /// # Returns
    ///
    /// A `Result` containing the serialized bytes on success, or a `bincode::Error`
    /// if serialization fails.
    ///
    /// # Example
    ///
    /// ```rust
    /// use indidus_e2ee_client::EncryptedMessageFrame;
    ///
    /// let frame = EncryptedMessageFrame::new(
    ///     vec![1, 2, 3],
    ///     vec![4, 5, 6],
    ///     vec![7, 8, 9],
    ///     vec![10, 11, 12],
    /// );
    ///
    /// let serialized = frame.serialize().expect("Failed to serialize");
    /// ```
    pub fn serialize(&self) -> Result<Vec<u8>, bincode::Error> {
        bincode::serialize(self)
    }

    /// Deserializes a byte slice into an `EncryptedMessageFrame`.
    ///
    /// This method uses the `bincode` library to deserialize binary data
    /// back into an `EncryptedMessageFrame` struct.
    ///
    /// # Arguments
    ///
    /// * `bytes` - The byte slice containing the serialized frame data
    ///
    /// # Returns
    ///
    /// A `Result` containing the deserialized `EncryptedMessageFrame` on success,
    /// or a `bincode::Error` if deserialization fails.
    ///
    /// # Example
    ///
    /// ```rust
    /// use indidus_e2ee_client::EncryptedMessageFrame;
    ///
    /// let original = EncryptedMessageFrame::new(
    ///     vec![1, 2, 3],
    ///     vec![4, 5, 6],
    ///     vec![7, 8, 9],
    ///     vec![10, 11, 12],
    /// );
    ///
    /// let serialized = original.serialize().expect("Failed to serialize");
    /// let deserialized = EncryptedMessageFrame::deserialize(&serialized)
    ///     .expect("Failed to deserialize");
    ///
    /// assert_eq!(original, deserialized);
    /// ```
    pub fn deserialize(bytes: &[u8]) -> Result<Self, bincode::Error> {
        bincode::deserialize(bytes)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_round_trip_serialization() {
        // Create a test frame with sample data
        let original_frame = EncryptedMessageFrame::new(
            vec![1, 2, 3, 4, 5],           // recipient_key
            vec![10, 20, 30, 40, 50],      // ephemeral_key
            vec![100, 200, 255, 128, 64],  // ciphertext
            vec![0, 1, 2, 3, 4, 5, 6, 7],  // nonce
        );

        // Serialize the frame
        let serialized = original_frame
            .serialize()
            .expect("Failed to serialize EncryptedMessageFrame");

        // Ensure we actually got some bytes
        assert!(!serialized.is_empty(), "Serialized data should not be empty");

        // Deserialize the frame
        let deserialized_frame = EncryptedMessageFrame::deserialize(&serialized)
            .expect("Failed to deserialize EncryptedMessageFrame");

        // Verify that the original and deserialized frames are identical
        assert_eq!(
            original_frame, deserialized_frame,
            "Original and deserialized frames should be equal"
        );
    }

    #[test]
    fn test_empty_fields_serialization() {
        // Test with empty vectors to ensure edge cases work
        let frame_with_empty_fields = EncryptedMessageFrame::new(
            vec![],  // empty recipient_key
            vec![],  // empty ephemeral_key
            vec![],  // empty ciphertext
            vec![],  // empty nonce
        );

        let serialized = frame_with_empty_fields
            .serialize()
            .expect("Failed to serialize frame with empty fields");

        let deserialized = EncryptedMessageFrame::deserialize(&serialized)
            .expect("Failed to deserialize frame with empty fields");

        assert_eq!(frame_with_empty_fields, deserialized);
    }

    #[test]
    fn test_large_data_serialization() {
        // Test with larger data to ensure it handles bigger payloads
        let large_data = vec![42u8; 1024]; // 1KB of data
        let frame_with_large_data = EncryptedMessageFrame::new(
            large_data.clone(),
            large_data.clone(),
            large_data.clone(),
            vec![1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], // typical nonce size
        );

        let serialized = frame_with_large_data
            .serialize()
            .expect("Failed to serialize frame with large data");

        let deserialized = EncryptedMessageFrame::deserialize(&serialized)
            .expect("Failed to deserialize frame with large data");

        assert_eq!(frame_with_large_data, deserialized);
    }

    #[test]
    fn test_invalid_data_deserialization() {
        // Test that invalid data properly returns an error
        let invalid_data = vec![0xFF, 0xFE, 0xFD]; // Some random invalid bytes
        
        let result = EncryptedMessageFrame::deserialize(&invalid_data);
        assert!(
            result.is_err(),
            "Deserializing invalid data should return an error"
        );
    }

    #[test]
    fn test_frame_creation() {
        // Test the new() constructor
        let recipient_key = vec![1, 2, 3];
        let ephemeral_key = vec![4, 5, 6];
        let ciphertext = vec![7, 8, 9];
        let nonce = vec![10, 11, 12];

        let frame = EncryptedMessageFrame::new(
            recipient_key.clone(),
            ephemeral_key.clone(),
            ciphertext.clone(),
            nonce.clone(),
        );

        assert_eq!(frame.recipient_key, recipient_key);
        assert_eq!(frame.ephemeral_key, ephemeral_key);
        assert_eq!(frame.ciphertext, ciphertext);
        assert_eq!(frame.nonce, nonce);
    }
}