# Indidus E2EE Client SDK

A Rust client SDK for end-to-end encrypted messaging using the Indidus Signal Protocol. This crate provides high-level APIs for establishing secure sessions, sending encrypted messages, and managing cryptographic state.

## Features

- **🔐 End-to-End Encryption**: Messages are encrypted using the Signal Protocol with perfect forward secrecy
- **📱 Easy Session Management**: Simple APIs for establishing and managing encrypted sessions
- **📁 Secure File Transfer**: Built-in support for encrypted file sharing with chunked uploads
- **🔑 Identity Management**: Handle identity keys and authentication seamlessly
- **🌐 Server Integration**: Built-in support for Indidus E2EE server communication
- **⚡ Real-time Communication**: WebSocket-based real-time messaging
- **💾 State Persistence**: Save and restore client state across application restarts

## Quick Start

Add this to your `Cargo.toml`:

```toml
[dependencies]
indidus_e2ee_client = "0.1.0"
tokio = { version = "1.0", features = ["full"] }
url = "2.0"
```

### Basic Usage

```rust
use indidus_e2ee_client::{Client, ClientConfig};
use url::Url;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create a client configuration
    let config = ClientConfig::new(Url::parse("https://your-server.com")?)
        .with_display_name("My Client".to_string())
        .with_debug_mode(true);

    // Create and initialize a new client instance
    let mut client = Client::new(config)?;
    client.initialize().await?;

    println!("Client ID: {}", client.client_id());
    println!("Client initialized successfully!");

    Ok(())
}
```

### Sending Messages

```rust
use indidus_e2ee_client::{Client, ClientConfig};
use url::Url;
use uuid::Uuid;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ClientConfig::new(Url::parse("https://your-server.com")?);
    let mut client = Client::new(config)?;
    client.initialize().await?;

    // Connect to the server
    client.connect().await?;

    // Send a message to another client
    let recipient_id = Uuid::parse_str("550e8400-e29b-41d4-a716-************")?;
    let message = b"Hello, secure world!";
    
    client.send_message(recipient_id, message, "text").await?;
    println!("Message sent successfully!");

    Ok(())
}
```

### Receiving Messages

```rust
use indidus_e2ee_client::{Client, ClientConfig, ClientEvent};
use url::Url;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ClientConfig::new(Url::parse("https://your-server.com")?);
    let mut client = Client::new(config)?;
    client.initialize().await?;
    client.connect().await?;

    // Listen for incoming events
    while let Some(event) = client.next_event().await? {
        match event {
            ClientEvent::MessageReceived { sender_id, encrypted_payload, .. } => {
                // Decrypt the message
                let decrypted = client.decrypt_message(sender_id, &encrypted_payload).await?;
                println!("Received message from {}: {}", sender_id, String::from_utf8_lossy(&decrypted));
            }
            ClientEvent::ConnectionStateChanged(state) => {
                println!("Connection state changed: {:?}", state);
            }
            _ => {
                println!("Other event: {:?}", event);
            }
        }
    }

    Ok(())
}
```

### File Transfer

```rust
use indidus_e2ee_client::{Client, ClientConfig, ClientEvent};
use url::Url;
use uuid::Uuid;
use std::path::Path;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ClientConfig::new(Url::parse("https://your-server.com")?);
    let mut client = Client::new(config)?;
    client.initialize().await?;
    client.connect().await?;

    let recipient_id = Uuid::parse_str("550e8400-e29b-41d4-a716-************")?;
    
    // Send a file
    let file_path = Path::new("document.pdf");
    let transfer_id = client.send_file(recipient_id, file_path).await?;
    println!("File transfer initiated: {}", transfer_id);

    // Monitor transfer progress
    while let Some(event) = client.next_event().await? {
        match event {
            ClientEvent::FileTransferProgress { transfer_id, progress_percentage, .. } => {
                println!("Transfer {}: {:.1}% complete", transfer_id, progress_percentage);
            }
            ClientEvent::FileTransferComplete { transfer_id, file_name, .. } => {
                println!("Transfer {} completed: {}", transfer_id, file_name);
                break;
            }
            ClientEvent::FileTransferFailed { transfer_id, error_message, .. } => {
                println!("Transfer {} failed: {}", transfer_id, error_message);
                break;
            }
            _ => {}
        }
    }

    Ok(())
}
```

## Configuration

The `ClientConfig` struct provides comprehensive configuration options:

```rust
use indidus_e2ee_client::ClientConfig;
use url::Url;

let config = ClientConfig::new(Url::parse("https://your-server.com")?)
    .with_display_name("My Application".to_string())
    .with_max_prekeys(100)                    // Maximum pre-keys to maintain
    .with_max_skip_keys(1000)                 // Maximum skipped message keys
    .with_timeout(30)                         // Connection timeout in seconds
    .with_debug_mode(true);                   // Enable debug logging
```

### Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `server_url` | Base URL of the Indidus E2EE server | Required |
| `display_name` | Human-readable client name | None |
| `max_prekeys` | Maximum pre-keys to maintain on server | 100 |
| `max_skip_keys` | Maximum skipped message keys per session | 1000 |
| `connection_timeout_secs` | Connection timeout in seconds | 30 |
| `auto_refresh_prekeys` | Automatically refresh pre-keys when low | true |
| `debug_mode` | Enable debug logging | false |

## State Management

The client supports persistent state management to maintain sessions across application restarts:

```rust
use indidus_e2ee_client::{Client, ClientConfig};
use url::Url;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ClientConfig::new(Url::parse("https://your-server.com")?);
    
    // Try to restore from saved state
    let mut client = match Client::load_state("client_state.json", config.clone()).await {
        Ok(client) => {
            println!("Restored client from saved state");
            client
        }
        Err(_) => {
            println!("Creating new client");
            let mut client = Client::new(config)?;
            client.initialize().await?;
            client
        }
    };

    // Use the client...
    client.connect().await?;

    // Save state before shutdown
    client.save_state("client_state.json").await?;
    println!("Client state saved");

    Ok(())
}
```

## Error Handling

The client provides comprehensive error handling with detailed error types:

```rust
use indidus_e2ee_client::{Client, ClientConfig, ClientError};
use url::Url;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ClientConfig::new(Url::parse("https://your-server.com")?);
    let mut client = Client::new(config)?;

    match client.initialize().await {
        Ok(_) => println!("Client initialized successfully"),
        Err(ClientError::Network(e)) => {
            println!("Network error: {}. Check your internet connection.", e);
        }
        Err(ClientError::InvalidConfiguration { parameter, value, suggestion }) => {
            println!("Configuration error: {} = '{}'. {}", parameter, value, suggestion);
        }
        Err(ClientError::KeyManagement { operation, reason }) => {
            println!("Key management error during {}: {}", operation, reason);
        }
        Err(e) => {
            println!("Other error: {}", e);
        }
    }

    Ok(())
}
```

## Security Considerations

- **Key Storage**: Identity keys are stored in memory and should be persisted securely
- **State Files**: Client state files contain sensitive cryptographic material and should be protected
- **Network Security**: Always use HTTPS/WSS connections to the server
- **Forward Secrecy**: The Signal Protocol provides perfect forward secrecy for all messages
- **Pre-key Rotation**: Pre-keys are automatically rotated to maintain security

## Examples

See the [`examples/`](../examples/) directory for complete working examples:

- [`basic_messaging.rs`](../examples/basic_messaging.rs) - Basic message sending and receiving
- [`file_transfer.rs`](../examples/file_transfer.rs) - File transfer with progress monitoring
- [`actix_integration/`](../examples/actix_integration/) - Integration with Actix Web
- [`axum_integration/`](../examples/axum_integration/) - Integration with Axum
- [`warp_server/`](../examples/warp_server/) - Integration with Warp

## API Reference

For detailed API documentation, run:

```bash
cargo doc --open
```

## Requirements

- Rust 1.70 or later
- Tokio runtime for async operations
- Network connectivity to the Indidus E2EE server

## License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## Contributing

Contributions are welcome! Please read our [Contributing Guide](../CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## Support

- **Documentation**: [API Reference](https://docs.rs/indidus_e2ee_client)
- **Issues**: [GitHub Issues](https://github.com/ashim-kr-saha/indidus_e2ee/issues)
- **Discussions**: [GitHub Discussions](https://github.com/ashim-kr-saha/indidus_e2ee/discussions)