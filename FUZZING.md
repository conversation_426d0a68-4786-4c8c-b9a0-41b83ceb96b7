# Fuzzing Guide

This document provides instructions for using the fuzz testing suite in the Indidus E2EE project. Fuzzing helps discover bugs and security vulnerabilities by feeding random, unexpected inputs to critical functions.

## Overview

Fuzzing is an automated testing technique that generates random inputs to find edge cases, crashes, and potential security vulnerabilities. This project uses `cargo-fuzz`, which leverages libFuzzer to provide coverage-guided fuzzing for Rust code.

## Available Fuzz Targets

The project currently includes the following fuzz targets:

### 1. `server_message_parser`
- **Purpose**: Tests the robustness of server message deserialization
- **Target Function**: `ServerMessage::deserialize` (from `indidus_e2ee_server`)
- **Input**: Raw byte slices that are interpreted as JSON
- **Goal**: Discover inputs that cause panics during message parsing

### 2. `session_decrypt`
- **Purpose**: Tests the cryptographic decryption logic
- **Target Function**: `SessionState::decrypt` (from `indidus_signal_protocol`)
- **Input**: Raw byte slices representing encrypted message data
- **Goal**: Discover inputs that cause panics during decryption operations

## Prerequisites

### Install Rust Nightly
Fuzzing requires Rust nightly toolchain for sanitizer support:

```bash
rustup install nightly
rustup default nightly
```

### Install cargo-fuzz
```bash
cargo install cargo-fuzz
```

## Running Fuzz Tests

### Basic Usage

Navigate to the project root and run a specific fuzz target:

```bash
# Run the server message parser fuzz target
cargo fuzz run server_message_parser

# Run the session decrypt fuzz target
cargo fuzz run session_decrypt
```

### Time-Limited Fuzzing

Run fuzzing for a specific duration:

```bash
# Run for 60 seconds
cargo fuzz run server_message_parser -- -max_total_time=60

# Run for 5 minutes (300 seconds)
cargo fuzz run session_decrypt -- -max_total_time=300
```

### Advanced Options

```bash
# Run with specific number of iterations
cargo fuzz run server_message_parser -- -runs=10000

# Run with multiple jobs (parallel fuzzing)
cargo fuzz run server_message_parser -- -jobs=4

# Run with custom timeout per input
cargo fuzz run server_message_parser -- -timeout=30
```

## Understanding Results

### Successful Run
If fuzzing completes without finding issues, you'll see output like:
```
INFO: Seed: 1234567890
INFO: Loaded 1 modules   (12345 inline 8-bit counters): 12345 [0x..., 0x...)
INFO: -max_total_time=60 seconds
INFO: A corpus is not provided, starting from an empty corpus
#1      INITED cov: 123 ft: 456 corp: 1/1b exec/s: 0 rss: 45Mb
...
INFO: Done 50000 runs in 60 second(s)
```

### When Issues Are Found
If fuzzing discovers a crash or hang, it will:
1. Save the problematic input to `fuzz/artifacts/`
2. Display the crash information
3. Provide a stack trace

Example crash output:
```
ERROR: libFuzzer: deadly signal
    #0 0x... in function_name
    #1 0x... in another_function
...
artifact_prefix='./artifacts/'; Test unit written to ./artifacts/crash-abc123
```

## Reproducing Issues

When a crash is found, reproduce it using:

```bash
# Reproduce a specific crash
cargo fuzz run server_message_parser fuzz/artifacts/crash-abc123
```

## Corpus Management

Fuzzing builds a corpus of interesting inputs over time:

```bash
# View corpus statistics
ls -la fuzz/corpus/server_message_parser/

# Clear corpus to start fresh
rm -rf fuzz/corpus/server_message_parser/*
```

## Integration with CI

The project includes automated fuzz smoke tests in CI that:
- Verify fuzz targets compile correctly
- Run each target for 30 seconds to catch immediate issues
- Ensure fuzzing infrastructure remains functional

## Best Practices

### For Contributors

1. **Run fuzzing locally** before submitting PRs that modify parsing or crypto logic
2. **Add new fuzz targets** when introducing new parsing functions or crypto operations
3. **Report any crashes** found during fuzzing as security issues

### Recommended Fuzzing Schedule

- **Quick check**: 1-2 minutes per target during development
- **Thorough testing**: 30-60 minutes per target before releases
- **Deep fuzzing**: Several hours or overnight for comprehensive testing

### Adding New Fuzz Targets

To add a new fuzz target:

1. Create a new file in `fuzz/fuzz_targets/your_target.rs`
2. Add the target to `fuzz/Cargo.toml`:
   ```toml
   [[bin]]
   name = "your_target"
   path = "fuzz_targets/your_target.rs"
   test = false
   doc = false
   bench = false
   ```
3. Update this documentation
4. Add the target to CI smoke tests

## Troubleshooting

### Common Issues

**"error: the option `Z` is only accepted on the nightly compiler"**
- Solution: Ensure you're using Rust nightly: `rustup default nightly`

**"No such file or directory: cargo-fuzz"**
- Solution: Install cargo-fuzz: `cargo install cargo-fuzz`

**Compilation errors in fuzz targets**
- Solution: Check that all imports are correct and dependencies are properly configured

### Getting Help

- Check the [cargo-fuzz documentation](https://rust-fuzz.github.io/book/cargo-fuzz.html)
- Review libFuzzer options: `cargo fuzz run target -- -help=1`
- Open an issue in the project repository for fuzzing-specific problems

## Security Considerations

- **Never ignore crashes**: All fuzzing crashes should be investigated as potential security vulnerabilities
- **Test with realistic data**: While random inputs are valuable, also test with realistic message formats
- **Regular fuzzing**: Incorporate fuzzing into your regular testing workflow
- **Coordinate disclosure**: Report security issues found through fuzzing responsibly

## Performance Tips

- Use multiple CPU cores: `cargo fuzz run target -- -jobs=$(nproc)`
- Monitor memory usage during long fuzzing sessions
- Consider using faster debug builds for initial fuzzing: `cargo fuzz run target --dev`

---

For more information about the Indidus E2EE project's security practices, see [SECURITY.md](SECURITY.md).