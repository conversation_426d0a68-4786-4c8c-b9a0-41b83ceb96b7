[workspace]
resolver = "2"
members = [
    "indidus_shared",
    "indidus_signal_protocol",
    "indidus_e2ee_client", 
    "indidus_e2ee_server",
    "examples/axum_integration",
    "examples/actix_integration",
    "examples/warp_server",
]
exclude = ["fuzz"]

# Root package for examples
[package]
name = "indidus_e2ee_examples"
version = "0.1.0"
edition = "2021"

[[example]]
name = "basic_messaging_server"
path = "examples/basic_messaging_server.rs"

[[example]]
name = "basic_messaging_client"
path = "examples/basic_messaging_client.rs"

[[example]]
name = "file_transfer_server"
path = "examples/file_transfer_server.rs"

[[example]]
name = "file_transfer_client"
path = "examples/file_transfer_client.rs"

[dependencies]
indidus_e2ee_server = { path = "indidus_e2ee_server" }
indidus_e2ee_client = { path = "indidus_e2ee_client" }
indidus_signal_protocol = { path = "indidus_signal_protocol" }
indidus_shared = { path = "indidus_shared" }
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
uuid = { workspace = true }
url = { workspace = true }
tokio-tungstenite = { workspace = true }
futures-util = { workspace = true }
async-trait = { workspace = true }
rand = { workspace = true }
sha2 = { workspace = true }

[dev-dependencies]
# Integration test dependencies
assert_cmd = "2.0"
futures = "0.3"
tempfile = "3.8"

[workspace.dependencies]
# Shared dependencies across workspace
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
thiserror = "1.0"
uuid = { version = "1.0", features = ["v4", "serde"] }
axum = { version = "0.7", features = ["ws"] }
url = "2.4"
sha2 = "0.10"
rand = "0.8"
tokio-tungstenite = "0.20"
futures-util = "0.3"
async-trait = "0.1"
regex = "1.10.5"