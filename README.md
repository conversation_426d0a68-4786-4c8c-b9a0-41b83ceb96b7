# Indidus End-to-End Encryption Library

[![Rust](https://img.shields.io/badge/rust-1.70%2B-brightgreen.svg)](https://www.rust-lang.org)
[![License](https://img.shields.io/badge/license-MIT%2FApache--2.0-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/ashim-kr-saha/indidus_e2ee)

A comprehensive Rust library for implementing end-to-end encrypted messaging and file transfer using the Signal Protocol. Indidus provides both client and server SDKs for building secure communication applications with zero-knowledge architecture.

## 🚀 Quick Start

```rust
use indidus_e2ee_client::{Client, ClientConfig};
use url::Url;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create and initialize a client
    let config = ClientConfig::new(Url::parse("https://your-server.com")?);
    let mut client = Client::new(config)?;
    client.initialize().await?;
    
    // Start secure messaging
    client.connect().await?;
    println!("Secure client ready!");
    
    Ok(())
}
```

## 📦 Crates

This workspace contains three main crates:

| Crate | Description | Documentation |
|-------|-------------|---------------|
| [`indidus_e2ee_client`](indidus_e2ee_client/) | Client SDK for E2EE messaging | [Client README](indidus_e2ee_client/README.md) |
| [`indidus_e2ee_server`](indidus_e2ee_server/) | Server SDK for message relay | [Server README](indidus_e2ee_server/README.md) |
| [`indidus_signal_protocol`](indidus_signal_protocol/) | Core Signal Protocol implementation | [Protocol Docs](indidus_signal_protocol/) |

## ✨ Features

### 🔐 **Zero-Knowledge Security**
- **End-to-end encryption** using the Signal Protocol
- **Perfect forward secrecy** and post-compromise security
- **Server-side blindness** - relay servers never see message content

### 📱 **Client SDK Features**
- Simple APIs for encrypted messaging
- Built-in state persistence and recovery
- Secure file transfer with chunked uploads
- Real-time communication via WebSockets
- Cross-platform compatibility

### 🌐 **Server SDK Features**
- Framework-agnostic integration (Axum, Warp, Actix, etc.)
- High-performance message relay
- Pre-key distribution and management
- File transfer coordination
- Scalable architecture

### 🛡️ **Security-First Design**
- Based on the proven Signal Protocol
- Cryptographically secure random number generation
- Protection against various attack vectors
- Comprehensive security documentation

## 📚 Documentation

### Getting Started
- [**Getting Started Tutorial**](.docs/GETTING_STARTED.md) - Build your first E2EE app
- [**Client State Management**](.docs/CLIENT_STATE_MANAGEMENT.md) - Persist cryptographic state
- [**Server Deployment Guide**](.docs/guide/RELAY_SERVER_DEPLOYMENT.md) - Production deployment

### Security & Compliance
- [**Security Considerations**](.docs/SECURITY.md) - Threat model and best practices
- [**API Documentation**](https://docs.rs/indidus_e2ee_client) - Complete API reference

### Examples
Comprehensive examples are available in the [`examples/`](examples/) directory:

- [**Basic Messaging**](examples/basic_messaging.rs) - Simple encrypted chat
- [**File Transfer**](examples/file_transfer.rs) - Secure file sharing
- [**Axum Integration**](examples/axum_integration/) - Web framework integration
- [**Warp Integration**](examples/warp_server/) - Alternative web framework
- [**Actix Integration**](examples/actix_integration/) - Actix Web integration

## 🛠️ Installation

Add to your `Cargo.toml`:

```toml
[dependencies]
# For client applications
indidus_e2ee_client = "0.1.0"

# For server applications
indidus_e2ee_server = "0.1.0"

# Common dependencies
tokio = { version = "1.0", features = ["full"] }
url = "2.0"
```

## 💻 Development

### Prerequisites

- **Rust 1.70 or later**
- **Cargo** (included with Rust)

### Building

```bash
# Clone the repository
git clone https://github.com/ashim-kr-saha/indidus_e2ee.git
cd indidus_e2ee

# Build all crates
cargo build --all

# Run tests
cargo test --all

# Generate documentation
cargo doc --open
```

### Running Examples

```bash
# Basic messaging example
cargo run --example basic_messaging

# Axum server integration
cargo run --example axum_integration

# File transfer example
cargo run --example file_transfer
```

## 🏗️ Architecture

The Indidus E2EE library follows a modular architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│   Client SDK    │◄──►│   Server SDK    │◄──►│   Client SDK    │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                     ┌─────────────────┐
                     │                 │
                     │ Signal Protocol │
                     │      Core       │
                     │                 │
                     └─────────────────┘
```

- **Client SDK**: High-level APIs for applications
- **Server SDK**: Relay infrastructure for message routing
- **Signal Protocol Core**: Cryptographic primitives and protocol implementation

## 🔒 Security Model

Indidus implements a **zero-knowledge architecture**:

1. **Client-side encryption**: All encryption happens on client devices
2. **Server blindness**: Relay servers cannot decrypt messages or files
3. **Forward secrecy**: Past messages remain secure even if keys are compromised
4. **Authentic encryption**: Built-in authentication prevents tampering

For detailed security information, see our [Security Considerations](.docs/SECURITY.md) document.

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Style

- Follow standard Rust formatting (`cargo fmt`)
- Add comprehensive documentation for public APIs
- Include unit tests for new features
- Follow the existing code organization patterns

## 📄 License

This project is dual-licensed under:

- [MIT License](LICENSE-MIT)
- [Apache License 2.0](LICENSE-APACHE)

You may choose either license for your use case.

## 🌟 Acknowledgments

- The [Signal Protocol](https://signal.org/docs/) for the cryptographic foundation
- The Rust community for excellent cryptographic libraries
- Contributors and early adopters who helped shape this library

## 📞 Support

- [Documentation](.docs/) - Comprehensive guides and tutorials
- [API Reference](https://docs.rs/indidus_e2ee_client) - Complete API documentation
- [Examples](examples/) - Working code examples
- [Issues](https://github.com/ashim-kr-saha/indidus_e2ee/issues) - Bug reports and feature requests

---

**Built with ❤️ in Rust**
