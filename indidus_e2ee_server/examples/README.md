# File Transfer Example

This example demonstrates end-to-end encrypted file transfer using the Indidus E2EE system. It showcases a complete workflow where one client sends a large file to another client through an encrypted, chunked transfer process.

## Overview

The file transfer example builds upon the basic messaging example to demonstrate:

- **Large file handling**: Transfer of 100MB files with chunked processing
- **Real-time progress tracking**: Live progress updates during upload and download
- **End-to-end encryption**: Files are encrypted in chunks during transfer
- **File integrity verification**: SHA-256 checksums ensure file integrity
- **Automatic reassembly**: Chunks are automatically decrypted and reassembled

## Architecture

```text
┌─────────────┐    WebSocket     ┌─────────────┐    WebSocket     ┌─────────────┐
│   Sender    │ ────────────────▶│   Server    │◀──────────────── │  Receiver   │
│  Client     │                  │  (Relay)    │                  │   Client    │
└─────────────┘                  └─────────────┘                  └─────────────┘
       │                                │                                │
┌─────▼─────┐                          │                                │
│ 100MB File│                          │                                │
│ (Random   │                          │                                │
│  Data)    │                          │                                │
└───────────┘                          │                                │
       │                                │                                │
       │ 1. Calculate SHA-256           │                                │
       │ 2. Split into chunks           │                                │
       │ 3. Encrypt each chunk          │                                │
       │ 4. Send FileTransferInit       │                                │
       │ ──────────────────────────────▶│                                │
       │                                │ 1. FileTransferNotification    │
       │                                │ ──────────────────────────────▶│
       │                                │                                │
       │ 5. Upload encrypted chunks     │                                │
       │ ──────────────────────────────▶│ 2. Download encrypted chunks   │
       │                                │ ──────────────────────────────▶│
       │                                │                                │
       │                                │ 3. Decrypt & reassemble        │
       │                                │                          ┌─────▼─────┐
       │                                │                          │ 100MB File│
       │                                │                          │(Decrypted)│
       │                                │                          │ Verified  │
       │                                │                          └───────────┘
```

## File Transfer Workflow

### 1. File Preparation (Sender)
- Generate a 100MB test file with random data
- Calculate SHA-256 hash for integrity verification
- Display file metadata (name, size, hash)

### 2. Transfer Initiation (Sender)
- Connect to the relay server
- Call `send_file()` API with recipient ID and file path
- Monitor upload progress through events

### 3. Transfer Notification (Receiver)
- Listen for `FileTransferInitiated` events
- Parse file metadata (sender, name, size)
- Accept the transfer and specify download location

### 4. Chunk Transfer (Both)
- **Sender**: File is automatically split into 1MB chunks, encrypted, and uploaded
- **Receiver**: Chunks are downloaded, decrypted, and written to temporary file
- Real-time progress updates show percentage, chunks processed, and transfer speed

### 5. File Reassembly (Receiver)
- All chunks are automatically reassembled into the final file
- Temporary files are cleaned up
- File integrity is verified using SHA-256 hash

### 6. Verification (Receiver)
- Calculate SHA-256 hash of received file
- Compare with expected hash (in production, this would be securely transmitted)
- Display "SUCCESS: File integrity verified" or "FAILURE: Checksum mismatch"

## Running the Example

### Prerequisites

1. Ensure you have Rust installed (1.70+)
2. All dependencies should be available in the workspace

### Step 1: Run the Example

From the workspace root directory:

```bash
cargo run --example file_transfer
```

This will:
1. Start the WebSocket relay server on `127.0.0.1:8080`
2. Create temporary directories for client state
3. Generate a 100MB test file
4. Start both sender and receiver clients
5. Execute the complete file transfer workflow

### Step 2: Monitor the Output

The example provides comprehensive logging throughout the process:

#### Server Startup
```
🚀 Starting File Transfer Example
📁 This example demonstrates end-to-end encrypted file transfer
📂 Created temporary state directories:
   Sender: /tmp/indidus_file_transfer_sender
   Receiver: /tmp/indidus_file_transfer_receiver
🌐 Starting WebSocket relay server
🎯 Server listening on 127.0.0.1:8080
```

#### File Preparation
```
📄 Created large test file: /tmp/large_test_file_for_transfer.bin (104857600 bytes)
🔍 Calculating SHA-256 hash for file integrity verification...
📋 Original file hash (SHA-256): a1b2c3d4e5f6...
```

#### Transfer Progress (Sender)
```
📁 Initiating file transfer:
   File: large_test_file_for_transfer.bin
   Size: 104857600 bytes (100.00 MB)
   SHA-256: a1b2c3d4e5f6...
   Recipient: receiver

📊 Upload Progress: 5.0% (5/100 chunks, 5242880/104857600 bytes)
📊 Upload Progress: 10.0% (10/100 chunks, 10485760/104857600 bytes)
...
✅ File transfer completed successfully!
```

#### Transfer Notification (Receiver)
```
📥 Incoming file transfer from 550e8400-e29b-41d4-a716-446655440000: large_test_file_for_transfer.bin (104857600 bytes). Starting download...
📋 Transfer Details:
   Transfer ID: 12345678-1234-5678-9abc-123456789abc
   File Name: large_test_file_for_transfer.bin
   File Size: 100.00 MB (104857600)
   Sender: 550e8400-e29b-41d4-a716-446655440000
💾 Download destination: /tmp/indidus_file_transfer_receiver/large_test_file_for_transfer.bin
```

#### Download Progress (Receiver)
```
📊 Download Progress [12345678-1234-5678-9abc-123456789abc]: 10.0% (10/100 chunks, 10485760/104857600 bytes) (avg chunk: 1048576 bytes) (ETA: 45s)
🔄 Chunk reassembly in progress - 25 chunks processed and decrypted
📊 Download Progress [12345678-1234-5678-9abc-123456789abc]: 50.0% (50/100 chunks, 52428800/104857600 bytes) (avg chunk: 1048576 bytes) (ETA: 22s)
```

#### Completion and Verification
```
🎉 File download and reassembly completed successfully!
📋 Transfer Summary:
   Transfer ID: 12345678-1234-5678-9abc-123456789abc
   File Name: large_test_file_for_transfer.bin
   Final Location: /tmp/indidus_file_transfer_receiver/large_test_file_for_transfer.bin
   File Size: 104857600 bytes (100.00 MB)
   Transfer Duration: 30.5s
   Average Speed: 3.44 MB/s

🔧 Reassembly Process Completed:
   • All chunks downloaded and decrypted successfully
   • Temporary files cleaned up automatically
   • File reassembled from encrypted chunks
   • Final file written to destination

✅ File integrity verified - size matches expected
🔐 Decryption and reassembly successful
🔍 Calculating SHA-256 hash of received file...
📋 Received file hash (SHA-256): a1b2c3d4e5f6...
✅ SUCCESS: File integrity verified
🎯 Test file header matches expected pattern
🔐 File was successfully transferred, decrypted, and reassembled
```

## Understanding the Output

### Progress Indicators

- **Percentage**: Shows completion percentage (0-100%)
- **Chunks**: Shows processed/total chunks (e.g., "50/100 chunks")
- **Bytes**: Shows transferred/total bytes with human-readable sizes
- **Speed**: Average transfer rate in MB/s or KB/s
- **ETA**: Estimated time remaining for completion

### Success Indicators

- ✅ **File integrity verified**: File size matches expected
- ✅ **SUCCESS: File integrity verified**: SHA-256 hash verification passed
- 🎯 **Test file header verified**: File content structure is correct
- 🔐 **Decryption and reassembly successful**: All cryptographic operations completed

### Error Indicators

- ❌ **File transfer failed**: Network or server error occurred
- ⚠️ **FAILURE: Checksum mismatch**: File integrity verification failed
- 💥 **File transfer failed**: Critical error with detailed troubleshooting

## Key Features Demonstrated

### 1. Chunked File Transfer
- Large files are split into 1MB chunks for efficient transfer
- Each chunk is encrypted independently
- Chunks can be transferred and processed in parallel

### 2. Real-time Progress Tracking
- Live updates during upload and download
- Transfer speed calculation
- Estimated time remaining
- Milestone notifications at 25%, 50%, 75% completion

### 3. End-to-End Encryption
- Each chunk is encrypted using AES-256-GCM
- Unique nonce per chunk for security
- Encryption keys are generated per transfer

### 4. File Integrity Verification
- SHA-256 hash calculation before sending
- Hash verification after receiving
- Additional content verification for test files

### 5. Automatic Error Handling
- Network error recovery
- Chunk reassembly validation
- Temporary file cleanup
- Detailed error reporting with troubleshooting hints

## Technical Implementation Details

### Encryption
- **Algorithm**: AES-256-GCM
- **Key Generation**: Random 256-bit keys per transfer
- **Nonce**: Unique 96-bit nonce per chunk
- **Payload Format**: `[nonce (12 bytes)] + [encrypted_data]`

### Chunking
- **Chunk Size**: 1MB (1,048,576 bytes) by default
- **Final Chunk**: May be smaller than 1MB
- **Indexing**: 0-based chunk indexing
- **Reassembly**: Sequential write to temporary file

### Progress Reporting
- **Update Frequency**: Every 5% for small files, 10% for large files
- **Metrics**: Percentage, chunks, bytes, speed, ETA
- **Events**: `FileTransferProgress`, `FileTransferComplete`, `FileTransferFailed`

### File Verification
- **Hash Algorithm**: SHA-256
- **Verification Points**: File size, hash, content structure
- **Error Detection**: Corruption, truncation, modification

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Ensure server is running on port 8080
   - Check firewall settings
   - Verify network connectivity

2. **Transfer Timeout**
   - Large files may take time to transfer
   - Check network speed and stability
   - Monitor server logs for errors

3. **Integrity Verification Failed**
   - File may have been corrupted during transfer
   - Check disk space on receiver
   - Verify encryption/decryption process

4. **Permission Denied**
   - Ensure write permissions for download directory
   - Check available disk space
   - Verify temporary directory access

### Debug Mode

The example runs with debug mode enabled, providing detailed logging of:
- Chunk encryption/decryption process
- Network communication details
- File I/O operations
- Progress calculation internals

## Production Considerations

This example is for demonstration purposes. In a production environment, consider:

1. **Key Exchange**: Implement secure key exchange using Signal Protocol
2. **Authentication**: Add proper client authentication
3. **Authorization**: Implement file transfer permissions
4. **Persistence**: Add transfer resumption capabilities
5. **Scalability**: Optimize for concurrent transfers
6. **Security**: Add additional security layers and auditing

## Related Examples

- `basic_messaging.rs`: Demonstrates basic encrypted messaging
- Signal Protocol examples in `indidus_signal_protocol/examples/`

## Dependencies

This example uses:
- `indidus_e2ee_client`: Client SDK for E2EE operations
- `indidus_e2ee_server`: Server for message relay
- `tokio`: Async runtime
- `tracing`: Structured logging
- `sha2`: SHA-256 hash calculation
- `rand`: Random data generation
- `axum`: Web server framework
- `tokio-tungstenite`: WebSocket implementation