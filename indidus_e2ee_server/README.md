# Indidus E2EE Server SDK

A Rust server SDK for relaying end-to-end encrypted messages and coordinating file transfers using the Indidus Signal Protocol. This crate provides the infrastructure needed to run a secure messaging relay server that facilitates communication between E2EE clients without having access to message content.

## Features

- **🔐 Zero-Knowledge Message Relay**: Relay encrypted messages without access to content
- **📁 Secure File Transfer Coordination**: Coordinate encrypted file transfers between clients
- **🔑 Pre-key Management**: Store and distribute client pre-keys for session establishment
- **⚡ Real-time Communication**: WebSocket-based real-time message delivery
- **🌐 Web Framework Integration**: Built on Axum with support for other frameworks
- **📊 Connection Management**: Track and manage client sessions and presence
- **🛡️ Security-First Design**: No access to plaintext messages or file content
- **📈 Scalable Architecture**: Designed for high-performance message relay

## Architecture Overview

The Indidus E2EE Server acts as a relay and coordination layer for end-to-end encrypted communication:

```
┌─────────────┐    Encrypted     ┌─────────────┐    Encrypted     ┌─────────────┐
│   Client A  │ ───────────────► │   Server    │ ───────────────► │   Client B  │
│             │                  │   (Relay)   │                  │             │
└─────────────┘                  └─────────────┘                  └─────────────┘
```

**Key Principles:**
- Server never has access to message plaintext
- All encryption/decryption happens on clients
- Server only routes encrypted payloads
- File transfers use temporary encrypted storage

## Quick Start

Add this to your `Cargo.toml`:

```toml
[dependencies]
indidus_e2ee_server = "0.1.0"
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
```

### Basic Server Setup

```rust
use indidus_e2ee_server::{Server, ServerConfig};
use axum::Router;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Create server configuration
    let config = ServerConfig::with_address("0.0.0.0".to_string(), 8080)
        .with_max_connections(1000)
        .with_logging(true, "info".to_string());

    // Create and start the server
    let server = Server::new(config).await?;
    let app = server.create_router().await?;

    println!("Server starting on {}", server.socket_addr()?);

    // Start the server
    let listener = tokio::net::TcpListener::bind(server.socket_addr()?).await?;
    axum::serve(listener, app).await?;

    Ok(())
}
```

### Custom Web Framework Integration

The server can be integrated with different web frameworks:

#### Axum Integration

```rust
use indidus_e2ee_server::{Server, ServerConfig, handle_websocket, handle_file_chunk};
use axum::{
    routing::{get, post},
    Router,
    extract::{WebSocketUpgrade, State},
    response::Response,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ServerConfig::with_address("0.0.0.0".to_string(), 8080);
    let server = Server::new(config).await?;
    let server_state = server.get_state();

    let app = Router::new()
        .route("/ws", get(websocket_handler))
        .route("/upload", post(upload_handler))
        .with_state(server_state);

    let listener = tokio::net::TcpListener::bind("0.0.0.0:8080").await?;
    axum::serve(listener, app).await?;
    Ok(())
}

async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(server_state): State<ServerState>,
) -> Response {
    ws.on_upgrade(move |socket| handle_websocket(socket, server_state))
}

async fn upload_handler(
    State(server_state): State<ServerState>,
    multipart: axum::extract::Multipart,
) -> Result<axum::Json<FileChunkResponse>, axum::http::StatusCode> {
    handle_file_chunk(multipart, server_state).await
        .map(axum::Json)
        .map_err(|_| axum::http::StatusCode::INTERNAL_SERVER_ERROR)
}
```

#### Warp Integration

```rust
use indidus_e2ee_server::{Server, ServerConfig};
use warp::Filter;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ServerConfig::with_address("0.0.0.0".to_string(), 8080);
    let server = Server::new(config).await?;
    let server_state = server.get_state();

    let websocket_route = warp::path("ws")
        .and(warp::ws())
        .and(warp::any().map(move || server_state.clone()))
        .map(|ws: warp::ws::Ws, server_state| {
            ws.on_upgrade(move |websocket| async move {
                // Handle WebSocket connection
                handle_websocket_warp(websocket, server_state).await;
            })
        });

    let routes = websocket_route;
    warp::serve(routes).run(([0, 0, 0, 0], 8080)).await;
    Ok(())
}
```

## Configuration

The `ServerConfig` struct provides comprehensive configuration options:

```rust
use indidus_e2ee_server::ServerConfig;

let config = ServerConfig::with_address("0.0.0.0".to_string(), 8080)
    .with_max_connections(1000)                    // Maximum concurrent connections
    .with_max_message_size(1024 * 1024)           // Maximum message size (1MB)
    .with_file_storage_path("/tmp/indidus_files")  // Temporary file storage
    .with_cleanup_interval(3600)                   // Cleanup interval in seconds
    .with_logging(true, "info".to_string())        // Enable logging with level
    .with_cors_enabled(true);                      // Enable CORS for web clients
```

### Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `host` | Server bind address | "0.0.0.0" |
| `port` | Server port | 8080 |
| `max_connections` | Maximum concurrent connections | 1000 |
| `max_message_size` | Maximum message size in bytes | 1MB |
| `file_storage_path` | Temporary file storage directory | "/tmp/indidus_files" |
| `cleanup_interval_secs` | File cleanup interval | 3600 (1 hour) |
| `enable_logging` | Enable request logging | false |
| `log_level` | Logging level | "info" |
| `cors_enabled` | Enable CORS headers | false |

## Message Flow

### 1. Client Registration
```
Client A ──► Server: WebSocket connection + registration
Server ──► Client A: Registration confirmation
```

### 2. Message Relay
```
Client A ──► Server: Encrypted message for Client B
Server ──► Client B: Relayed encrypted message
Client B ──► Server: Delivery confirmation
Server ──► Client A: Delivery notification
```

### 3. File Transfer
```
Client A ──► Server: File transfer initiation
Server ──► Client B: File transfer notification
Client A ──► Server: Encrypted file chunks
Server ──► Storage: Temporary encrypted storage
Client B ──► Server: Chunk download requests
Server ──► Client B: Encrypted file chunks
```

## API Endpoints

### WebSocket Endpoints

- `GET /ws` - WebSocket connection for real-time messaging

### HTTP Endpoints

- `POST /upload` - Upload encrypted file chunks
- `GET /download/{transfer_id}/{chunk_id}` - Download encrypted file chunks
- `GET /health` - Health check endpoint
- `GET /stats` - Server statistics (if enabled)

## Security Features

### Message Security
- **Zero-Knowledge**: Server never accesses message plaintext
- **Forward Secrecy**: Messages use ephemeral keys
- **Replay Protection**: Message ordering and deduplication
- **Identity Verification**: Client identity key management

### File Transfer Security
- **Encrypted Storage**: Files stored encrypted on server
- **Temporary Storage**: Files automatically cleaned up
- **Access Control**: Only authorized clients can download
- **Chunk Verification**: Integrity checking for file chunks

### Network Security
- **TLS/WSS Support**: Encrypted transport layer
- **CORS Protection**: Configurable cross-origin policies
- **Rate Limiting**: Protection against abuse
- **Connection Limits**: Prevent resource exhaustion

## Deployment

### Docker Deployment

```dockerfile
FROM rust:1.70 as builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/indidus_server /usr/local/bin/
EXPOSE 8080
CMD ["indidus_server"]
```

### Environment Configuration

```bash
# Server configuration
INDIDUS_HOST=0.0.0.0
INDIDUS_PORT=8080
INDIDUS_MAX_CONNECTIONS=1000

# Storage configuration
INDIDUS_STORAGE_PATH=/var/lib/indidus/files
INDIDUS_CLEANUP_INTERVAL=3600

# Logging
INDIDUS_LOG_LEVEL=info
INDIDUS_ENABLE_LOGGING=true

# Security
INDIDUS_CORS_ENABLED=true
INDIDUS_MAX_MESSAGE_SIZE=1048576
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: indidus-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: indidus-server
  template:
    metadata:
      labels:
        app: indidus-server
    spec:
      containers:
      - name: indidus-server
        image: indidus/e2ee-server:latest
        ports:
        - containerPort: 8080
        env:
        - name: INDIDUS_HOST
          value: "0.0.0.0"
        - name: INDIDUS_PORT
          value: "8080"
        volumeMounts:
        - name: storage
          mountPath: /var/lib/indidus/files
      volumes:
      - name: storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: indidus-server-service
spec:
  selector:
    app: indidus-server
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

## Monitoring and Observability

### Health Checks

```rust
use indidus_e2ee_server::{Server, ServerStats};

// Get server statistics
let stats = server.get_stats().await?;
println!("Active connections: {}", stats.active_connections);
println!("Messages relayed: {}", stats.messages_relayed);
println!("Files transferred: {}", stats.files_transferred);
```

### Metrics Integration

```rust
// Example with Prometheus metrics
use prometheus::{Counter, Gauge, Registry};

let messages_counter = Counter::new("messages_relayed_total", "Total messages relayed")?;
let connections_gauge = Gauge::new("active_connections", "Active WebSocket connections")?;

let registry = Registry::new();
registry.register(Box::new(messages_counter.clone()))?;
registry.register(Box::new(connections_gauge.clone()))?;
```

## Examples

See the [`examples/`](../examples/) directory for complete working examples:

- [`axum_integration/`](../examples/axum_integration/) - Full Axum server implementation
- [`warp_server/`](../examples/warp_server/) - Warp server implementation
- [`actix_integration/`](../examples/actix_integration/) - Actix Web integration

## Performance Considerations

- **Connection Pooling**: Efficient WebSocket connection management
- **Memory Usage**: Bounded message queues and file chunk caching
- **Disk I/O**: Asynchronous file operations with cleanup
- **Network**: Optimized message routing and batching

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Check if the server is running on the correct port
   - Verify firewall settings allow connections

2. **File Upload Failures**
   - Check disk space in the storage directory
   - Verify write permissions for the storage path

3. **High Memory Usage**
   - Reduce `max_connections` if needed
   - Check file cleanup interval settings

4. **WebSocket Disconnections**
   - Implement client-side reconnection logic
   - Check network stability and proxy settings

## API Reference

For detailed API documentation, run:

```bash
cargo doc --open
```

## Requirements

- Rust 1.70 or later
- Tokio runtime for async operations
- Sufficient disk space for temporary file storage
- Network connectivity for client connections

## License

This project is licensed under the MIT License - see the [LICENSE](../LICENSE) file for details.

## Contributing

Contributions are welcome! Please read our [Contributing Guide](../CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## Support

- **Documentation**: [API Reference](https://docs.rs/indidus_e2ee_server)
- **Issues**: [GitHub Issues](https://github.com/ashim-kr-saha/indidus_e2ee/issues)
- **Discussions**: [GitHub Discussions](https://github.com/ashim-kr-saha/indidus_e2ee/discussions)