[package]
name = "indidus_e2ee_server"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core protocol dependency
indidus_signal_protocol = { path = "../indidus_signal_protocol" }
indidus_shared = { path = "../indidus_shared" }

# Async runtime and web framework
tokio = { version = "1.0", features = ["full"] }
axum = { version = "0.7", features = ["ws", "multipart"] }
hyper = { version = "1.0", features = ["full"] }
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["cors", "trace"] }
async-trait = "0.1"
tokio-tungstenite = "0.21"
futures-util = "0.3"

# Serialization and data handling
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
base64 = "0.21"
url = { version = "2.4", features = ["serde"] }
dashmap = "5.5"

# Example dependencies
indidus_e2ee_client = { path = "../indidus_e2ee_client" }
sha2 = { workspace = true }
rand = { workspace = true }

[dev-dependencies]
tempfile = "3.8"
reqwest = { version = "0.11", features = ["multipart"] }
tokio-test = "0.4"

[[example]]
name = "server_file_transfer"
path = "examples/file_transfer.rs"
