//! Server configuration module for the Indidus E2EE Server
//!
//! This module defines the configuration structures and utilities for setting up
//! and running the Indidus E2EE server with various deployment options.

use serde::{Deserialize, Serialize};
use std::net::SocketAddr;
use std::path::PathBuf;
use url::Url;

/// Configuration parameters for the Indidus E2EE Server
///
/// This struct holds all the configuration needed to initialize and operate
/// the server, including network settings, storage options, and operational parameters.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// The host address to bind the server to
    pub host: String,
    
    /// The port number to listen on
    pub port: u16,
    
    /// Maximum number of concurrent connections
    pub max_connections: usize,
    
    /// Request timeout in seconds
    pub request_timeout_secs: u64,
    
    /// Maximum request body size in bytes
    pub max_request_size: usize,
    
    /// Enable CORS (Cross-Origin Resource Sharing)
    pub enable_cors: bool,
    
    /// Allowed CORS origins (if CORS is enabled)
    pub cors_origins: Vec<String>,
    
    /// Enable request logging
    pub enable_logging: bool,
    
    /// Log level (trace, debug, info, warn, error)
    pub log_level: String,
    
    /// Database connection URL (optional, for persistent storage)
    pub database_url: Option<Url>,
    
    /// Path to TLS certificate file (optional, for HTTPS)
    pub tls_cert_path: Option<PathBuf>,
    
    /// Path to TLS private key file (optional, for HTTPS)
    pub tls_key_path: Option<PathBuf>,
    
    /// Maximum number of pre-keys to store per client
    pub max_prekeys_per_client: u32,
    
    /// Session cleanup interval in seconds
    pub session_cleanup_interval_secs: u64,
    
    /// Maximum session idle time in seconds before cleanup
    pub max_session_idle_secs: u64,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 8080,
            max_connections: 1000,
            request_timeout_secs: 30,
            max_request_size: 1024 * 1024, // 1MB
            enable_cors: true,
            cors_origins: vec!["*".to_string()],
            enable_logging: true,
            log_level: "info".to_string(),
            database_url: None,
            tls_cert_path: None,
            tls_key_path: None,
            max_prekeys_per_client: 100,
            session_cleanup_interval_secs: 3600, // 1 hour
            max_session_idle_secs: 86400, // 24 hours
        }
    }
}

impl ServerConfig {
    /// Create a new server configuration with default values
    pub fn new() -> Self {
        Self::default()
    }
    
    /// Create a server configuration for the specified host and port
    ///
    /// # Arguments
    /// * `host` - The host address to bind to
    /// * `port` - The port number to listen on
    pub fn with_address(host: String, port: u16) -> Self {
        Self {
            host,
            port,
            ..Default::default()
        }
    }
    
    /// Set the maximum number of concurrent connections
    pub fn with_max_connections(mut self, max: usize) -> Self {
        self.max_connections = max;
        self
    }
    
    /// Set the request timeout
    pub fn with_request_timeout(mut self, timeout_secs: u64) -> Self {
        self.request_timeout_secs = timeout_secs;
        self
    }
    
    /// Set the maximum request body size
    pub fn with_max_request_size(mut self, size: usize) -> Self {
        self.max_request_size = size;
        self
    }
    
    /// Configure CORS settings
    pub fn with_cors(mut self, enabled: bool, origins: Vec<String>) -> Self {
        self.enable_cors = enabled;
        self.cors_origins = origins;
        self
    }
    
    /// Set logging configuration
    pub fn with_logging(mut self, enabled: bool, level: String) -> Self {
        self.enable_logging = enabled;
        self.log_level = level;
        self
    }
    
    /// Set the database URL for persistent storage
    pub fn with_database(mut self, url: Url) -> Self {
        self.database_url = Some(url);
        self
    }
    
    /// Configure TLS settings
    pub fn with_tls(mut self, cert_path: PathBuf, key_path: PathBuf) -> Self {
        self.tls_cert_path = Some(cert_path);
        self.tls_key_path = Some(key_path);
        self
    }
    
    /// Set pre-key storage limits
    pub fn with_prekey_limits(mut self, max_per_client: u32) -> Self {
        self.max_prekeys_per_client = max_per_client;
        self
    }
    
    /// Set session cleanup configuration
    pub fn with_session_cleanup(mut self, interval_secs: u64, max_idle_secs: u64) -> Self {
        self.session_cleanup_interval_secs = interval_secs;
        self.max_session_idle_secs = max_idle_secs;
        self
    }
    
    /// Get the socket address for binding
    pub fn socket_addr(&self) -> Result<SocketAddr, std::net::AddrParseError> {
        format!("{}:{}", self.host, self.port).parse()
    }
    
    /// Check if TLS is enabled
    pub fn is_tls_enabled(&self) -> bool {
        self.tls_cert_path.is_some() && self.tls_key_path.is_some()
    }
    
    /// Check if database persistence is enabled
    pub fn is_database_enabled(&self) -> bool {
        self.database_url.is_some()
    }
    
    /// Load configuration from a JSON file
    ///
    /// # Arguments
    /// * `path` - Path to the JSON configuration file
    ///
    /// # Returns
    /// The loaded configuration or an error if loading fails
    pub fn from_file(path: &PathBuf) -> Result<Self, Box<dyn std::error::Error>> {
        let content = std::fs::read_to_string(path)?;
        let config: ServerConfig = serde_json::from_str(&content)?;
        Ok(config)
    }
    
    /// Save configuration to a JSON file
    ///
    /// # Arguments
    /// * `path` - Path where to save the configuration file
    ///
    /// # Returns
    /// Success or an error if saving fails
    pub fn save_to_file(&self, path: &PathBuf) -> Result<(), Box<dyn std::error::Error>> {
        let content = serde_json::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_server_config_default() {
        let config = ServerConfig::default();
        assert_eq!(config.host, "127.0.0.1");
        assert_eq!(config.port, 8080);
        assert_eq!(config.max_connections, 1000);
        assert!(config.enable_cors);
        assert!(config.enable_logging);
        assert_eq!(config.log_level, "info");
    }
    
    #[test]
    fn test_server_config_builder() {
        let config = ServerConfig::with_address("0.0.0.0".to_string(), 9000)
            .with_max_connections(500)
            .with_logging(false, "debug".to_string());
            
        assert_eq!(config.host, "0.0.0.0");
        assert_eq!(config.port, 9000);
        assert_eq!(config.max_connections, 500);
        assert!(!config.enable_logging);
        assert_eq!(config.log_level, "debug");
    }
    
    #[test]
    fn test_socket_addr() {
        let config = ServerConfig::with_address("127.0.0.1".to_string(), 8080);
        let addr = config.socket_addr().unwrap();
        assert_eq!(addr.to_string(), "127.0.0.1:8080");
    }
    
    #[test]
    fn test_tls_configuration() {
        let mut config = ServerConfig::default();
        assert!(!config.is_tls_enabled());
        
        config = config.with_tls(
            PathBuf::from("/path/to/cert.pem"),
            PathBuf::from("/path/to/key.pem")
        );
        assert!(config.is_tls_enabled());
    }
    
    #[test]
    fn test_database_configuration() {
        let mut config = ServerConfig::default();
        assert!(!config.is_database_enabled());
        
        let db_url = Url::parse("postgresql://localhost/indidus").unwrap();
        config = config.with_database(db_url);
        assert!(config.is_database_enabled());
    }
}