use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

use super::error::HandlerResult;

/// Message relay request structure
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RelayMessageRequest {
    /// ID of the sender client
    pub sender_id: Uuid,
    
    /// ID of the recipient client
    pub recipient_id: Uuid,
    
    /// Encrypted message payload
    pub encrypted_payload: Vec<u8>,
    
    /// Message metadata (headers, timestamps, etc.)
    pub metadata: HashMap<String, String>,
    
    /// Message type identifier
    pub message_type: String,
}

/// Message relay response structure
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RelayMessageResponse {
    /// Whether the message was successfully relayed
    pub success: bool,
    
    /// Message ID assigned by the server
    pub message_id: Option<Uuid>,
    
    /// Error message if relay failed
    pub error: Option<String>,
    
    /// Whether the recipient is currently online
    pub recipient_online: bool,
}

/// Pre-key bundle request structure
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>erialize)]
pub struct PreKeyRequest {
    /// ID of the client requesting pre-keys
    pub requester_id: Uuid,
    
    /// ID of the client whose pre-keys are being requested
    pub target_client_id: Uuid,
    
    /// Number of pre-keys requested
    pub count: u32,
}

/// Pre-key bundle response structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PreKeyResponse {
    /// The client ID these pre-keys belong to
    pub client_id: Uuid,
    
    /// List of available pre-keys
    pub prekeys: Vec<Vec<u8>>,
    
    /// Whether more pre-keys are available
    pub has_more: bool,
}

/// File transfer coordination request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTransferRequest {
    /// ID of the sender client
    pub sender_id: Uuid,
    
    /// ID of the recipient client
    pub recipient_id: Uuid,
    
    /// File metadata
    pub file_name: String,
    pub file_size: u64,
    pub file_hash: String,
    
    /// Encryption parameters
    pub encryption_key: Vec<u8>,
    pub chunk_size: u32,
}

/// File transfer coordination response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTransferResponse {
    /// Unique transfer ID
    pub transfer_id: Uuid,
    
    /// Upload URL for the sender
    pub upload_url: String,
    
    /// Download URL for the recipient
    pub download_url: String,
    
    /// Transfer expiration time
    pub expires_at: std::time::SystemTime,
}

/// Trait for handling encrypted message relay between clients
///
/// This trait defines the contract for relaying encrypted messages between
/// clients. Implementations should handle message queuing for offline clients,
/// delivery confirmation, and rate limiting.
#[async_trait]
pub trait MessageRelay {
    /// Relay an encrypted message from one client to another
    ///
    /// # Arguments
    /// * `request` - The message relay request containing sender, recipient, and payload
    ///
    /// # Returns
    /// A result containing the relay response or an error
    async fn relay_message(&self, request: RelayMessageRequest) -> HandlerResult<RelayMessageResponse>;
    
    /// Get pending messages for a client
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client to get messages for
    ///
    /// # Returns
    /// A list of pending messages for the client
    async fn get_pending_messages(&self, client_id: Uuid) -> HandlerResult<Vec<RelayMessageRequest>>;
    
    /// Mark messages as delivered for a client
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client
    /// * `message_ids` - List of message IDs to mark as delivered
    ///
    /// # Returns
    /// Success or error result
    async fn mark_messages_delivered(&self, client_id: Uuid, message_ids: Vec<Uuid>) -> HandlerResult<()>;
    
    /// Check if a client is currently online
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client to check
    ///
    /// # Returns
    /// Whether the client is online
    async fn is_client_online(&self, client_id: Uuid) -> HandlerResult<bool>;
}

/// Trait for managing client pre-keys
///
/// This trait defines the contract for storing, retrieving, and managing
/// client pre-keys used in the X3DH key agreement protocol.
#[async_trait]
pub trait PreKeyManager {
    /// Store pre-keys for a client
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client
    /// * `prekeys` - List of pre-keys to store
    ///
    /// # Returns
    /// Success or error result
    async fn store_prekeys(&self, client_id: Uuid, prekeys: Vec<Vec<u8>>) -> HandlerResult<()>;
    
    /// Retrieve pre-keys for a client
    ///
    /// # Arguments
    /// * `request` - The pre-key request
    ///
    /// # Returns
    /// A response containing available pre-keys
    async fn get_prekeys(&self, request: PreKeyRequest) -> HandlerResult<PreKeyResponse>;
    
    /// Remove used pre-keys
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client
    /// * `prekey_ids` - List of pre-key IDs to remove
    ///
    /// # Returns
    /// Success or error result
    async fn remove_prekeys(&self, client_id: Uuid, prekey_ids: Vec<Uuid>) -> HandlerResult<()>;
    
    /// Get the number of available pre-keys for a client
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client
    ///
    /// # Returns
    /// The number of available pre-keys
    async fn prekey_count(&self, client_id: Uuid) -> HandlerResult<u32>;
}

/// Trait for coordinating file transfers between clients
///
/// This trait defines the contract for managing secure file transfers,
/// including upload/download URL generation, transfer tracking, and cleanup.
#[async_trait]
pub trait FileTransferCoordinator {
    /// Initiate a file transfer between clients
    ///
    /// # Arguments
    /// * `request` - The file transfer request
    ///
    /// # Returns
    /// A response containing transfer details and URLs
    async fn initiate_transfer(&self, request: FileTransferRequest) -> HandlerResult<FileTransferResponse>;
    
    /// Get the status of a file transfer
    ///
    /// # Arguments
    /// * `transfer_id` - The unique transfer ID
    ///
    /// # Returns
    /// The current transfer status
    async fn get_transfer_status(&self, transfer_id: Uuid) -> HandlerResult<FileTransferStatus>;
    
    /// Complete a file transfer
    ///
    /// # Arguments
    /// * `transfer_id` - The unique transfer ID
    ///
    /// # Returns
    /// Success or error result
    async fn complete_transfer(&self, transfer_id: Uuid) -> HandlerResult<()>;
    
    /// Cancel a file transfer
    ///
    /// # Arguments
    /// * `transfer_id` - The unique transfer ID
    ///
    /// # Returns
    /// Success or error result
    async fn cancel_transfer(&self, transfer_id: Uuid) -> HandlerResult<()>;
    
    /// Clean up expired transfers
    ///
    /// # Returns
    /// The number of transfers cleaned up
    async fn cleanup_expired_transfers(&self) -> HandlerResult<u32>;
}

/// File transfer status information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTransferStatus {
    /// Unique transfer ID
    pub transfer_id: Uuid,
    
    /// Current status of the transfer
    pub status: TransferStatus,
    
    /// Number of bytes uploaded
    pub bytes_uploaded: u64,
    
    /// Number of bytes downloaded
    pub bytes_downloaded: u64,
    
    /// Total file size
    pub total_size: u64,
    
    /// Transfer creation time
    pub created_at: std::time::SystemTime,
    
    /// Transfer expiration time
    pub expires_at: std::time::SystemTime,
}

/// Transfer status enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransferStatus {
    /// Transfer initiated, waiting for upload
    Pending,
    
    /// File is being uploaded
    Uploading,
    
    /// File uploaded, ready for download
    Ready,
    
    /// File is being downloaded
    Downloading,
    
    /// Transfer completed successfully
    Completed,
    
    /// Transfer was cancelled
    Cancelled,
    
    /// Transfer expired
    Expired,
    
    /// Transfer failed due to error
    Failed(String),
}

/// Trait for client authentication and session management
///
/// This trait defines the contract for authenticating clients and managing
/// their sessions with the server.
#[async_trait]
pub trait ClientAuthenticator {
    /// Authenticate a client and create a session
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client
    /// * `credentials` - Authentication credentials
    ///
    /// # Returns
    /// Success or authentication error
    async fn authenticate_client(&self, client_id: Uuid, credentials: Vec<u8>) -> HandlerResult<()>;
    
    /// Validate an existing client session
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client
    /// * `session_token` - The session token to validate
    ///
    /// # Returns
    /// Whether the session is valid
    async fn validate_session(&self, client_id: Uuid, session_token: String) -> HandlerResult<bool>;
    
    /// Revoke a client session
    ///
    /// # Arguments
    /// * `client_id` - The ID of the client
    ///
    /// # Returns
    /// Success or error result
    async fn revoke_session(&self, client_id: Uuid) -> HandlerResult<()>;
}
