use axum::extract::{Multipart, State};
use axum::http::StatusCode;
use axum::response::<PERSON><PERSON>;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{debug, error, info, warn};

use crate::storage::{ChunkStorageService, StorageError};

/// Request payload for file chunk upload
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct FileChunkRequest {
    /// Unique identifier for the file transfer session
    pub transfer_id: String,
    /// Index of this chunk in the sequence (0-based)
    pub chunk_index: u32,
    /// The encrypted chunk data
    pub chunk_data: Vec<u8>,
}

/// Response payload for successful file chunk upload
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileChunkResponse {
    /// The transfer ID that was processed
    pub transfer_id: String,
    /// The chunk index that was processed
    pub chunk_index: u32,
    /// Confirmation message
    pub message: String,
}

/// Error response for file chunk upload failures
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct FileChunkError {
    /// Error code for categorization
    pub error_code: String,
    /// Human-readable error message
    pub message: String,
    /// Optional additional details
    pub details: Option<String>,
}


/// HTTP handler for file chunk upload endpoint
/// 
/// Processes multipart form data containing transfer_id, chunk_index, and chunk_data
/// with comprehensive validation and error handling, then stores the chunk using the storage service
pub async fn handle_file_chunk(
    State(storage_service): State<Arc<ChunkStorageService>>,
    mut multipart: Multipart,
) -> Result<Json<FileChunkResponse>, (StatusCode, Json<FileChunkError>)> {
    // Parse the multipart request into a structured format
    let request = parse_multipart_request(&mut multipart).await?;
    
    // Validate the parsed request
    validate_chunk_request(&request, storage_service.max_chunk_size())?;
    
    // Store the chunk using the storage service
    match storage_service
        .store_chunk(&request.transfer_id, request.chunk_index, &request.chunk_data)
        .await
    {
        Ok(()) => {
            info!(
                "Successfully stored file chunk: transfer_id={}, chunk_index={}, size={} bytes",
                request.transfer_id, request.chunk_index, request.chunk_data.len()
            );

            Ok(Json(FileChunkResponse {
                transfer_id: request.transfer_id,
                chunk_index: request.chunk_index,
                message: "Chunk received, validated, and stored successfully".to_string(),
            }))
        }
        Err(storage_error) => {
            error!(
                "Failed to store chunk: transfer_id={}, chunk_index={}, error={}",
                request.transfer_id, request.chunk_index, storage_error
            );

            // Map storage errors to appropriate HTTP responses
            map_storage_error_to_http_response(storage_error, &request.transfer_id, request.chunk_index)
        }
    }
}

/// Map storage service errors to appropriate HTTP error responses
/// 
/// This function translates internal storage errors into user-friendly HTTP responses
/// while preserving appropriate error codes and security considerations
fn map_storage_error_to_http_response(
    error: StorageError,
    transfer_id: &str,
    chunk_index: u32,
) -> Result<Json<FileChunkResponse>, (StatusCode, Json<FileChunkError>)> {
    match error {
        StorageError::ChunkAlreadyExists { .. } => {
            // This is a client error - they're trying to upload a chunk that already exists
            Err((
                StatusCode::CONFLICT,
                Json(FileChunkError {
                    error_code: "CHUNK_ALREADY_EXISTS".to_string(),
                    message: format!(
                        "Chunk already exists for transfer_id={}, chunk_index={}",
                        transfer_id, chunk_index
                    ),
                    details: Some("This chunk has already been uploaded. Use a different chunk_index or transfer_id.".to_string()),
                }),
            ))
        }
        StorageError::InvalidTransferId(msg) => {
            // This should have been caught by validation, but handle it anyway
            Err((
                StatusCode::BAD_REQUEST,
                Json(FileChunkError {
                    error_code: "INVALID_TRANSFER_ID".to_string(),
                    message: "Invalid transfer ID".to_string(),
                    details: Some(msg),
                }),
            ))
        }
        StorageError::InvalidChunkIndex(index) => {
            // This should have been caught by validation, but handle it anyway
            Err((
                StatusCode::BAD_REQUEST,
                Json(FileChunkError {
                    error_code: "INVALID_CHUNK_INDEX".to_string(),
                    message: format!("Invalid chunk index: {}", index),
                    details: Some("Chunk index is out of acceptable range".to_string()),
                }),
            ))
        }
        StorageError::DirectoryCreationFailed(msg) => {
            // This is a server-side issue
            error!("Directory creation failed: {}", msg);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(FileChunkError {
                    error_code: "STORAGE_ERROR".to_string(),
                    message: "Failed to prepare storage for chunk".to_string(),
                    details: Some("The server encountered an error while preparing storage. Please try again later.".to_string()),
                }),
            ))
        }
        StorageError::AtomicWriteFailed(msg) => {
            // This is a server-side issue
            error!("Atomic write failed: {}", msg);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(FileChunkError {
                    error_code: "STORAGE_ERROR".to_string(),
                    message: "Failed to store chunk data".to_string(),
                    details: Some("The server encountered an error while storing the chunk. Please try again later.".to_string()),
                }),
            ))
        }
        StorageError::Io(io_error) => {
            // This is a server-side issue
            error!("IO error during storage: {}", io_error);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(FileChunkError {
                    error_code: "STORAGE_ERROR".to_string(),
                    message: "Storage system error".to_string(),
                    details: Some("The server encountered a storage system error. Please try again later.".to_string()),
                }),
            ))
        }
    }
}

/// Parse multipart form data into a FileChunkRequest
/// 
/// This function handles the low-level parsing of multipart/form-data and extracts
/// the required fields: transfer_id, chunk_index, and chunk_data
async fn parse_multipart_request(
    multipart: &mut Multipart,
) -> Result<FileChunkRequest, (StatusCode, Json<FileChunkError>)> {
    let mut transfer_id: Option<String> = None;
    let mut chunk_index: Option<u32> = None;
    let mut chunk_data: Option<Vec<u8>> = None;
    
    // Track which fields we've seen to detect duplicates
    let mut seen_transfer_id = false;
    let mut seen_chunk_index = false;
    let mut seen_chunk_data = false;

    // Parse each field in the multipart request
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        error!("Failed to parse multipart form data: {}", e);
        (
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "MULTIPART_PARSE_ERROR".to_string(),
                message: "Failed to parse multipart form data".to_string(),
                details: Some(format!("Parsing error: {}", e)),
            }),
        )
    })? {
        let field_name = field.name().unwrap_or("").to_string();
        
        match field_name.as_str() {
            "transfer_id" => {
                if seen_transfer_id {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "DUPLICATE_FIELD".to_string(),
                            message: "Duplicate transfer_id field in request".to_string(),
                            details: None,
                        }),
                    ));
                }
                seen_transfer_id = true;
                
                let data = field.bytes().await.map_err(|e| {
                    error!("Failed to read transfer_id field: {}", e);
                    (
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "FIELD_READ_ERROR".to_string(),
                            message: "Failed to read transfer_id field".to_string(),
                            details: Some(e.to_string()),
                        }),
                    )
                })?;
                
                transfer_id = Some(String::from_utf8(data.to_vec()).map_err(|e| {
                    (
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "INVALID_UTF8".to_string(),
                            message: "transfer_id must be valid UTF-8".to_string(),
                            details: Some(e.to_string()),
                        }),
                    )
                })?);
            }
            "chunk_index" => {
                if seen_chunk_index {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "DUPLICATE_FIELD".to_string(),
                            message: "Duplicate chunk_index field in request".to_string(),
                            details: None,
                        }),
                    ));
                }
                seen_chunk_index = true;
                
                let data = field.bytes().await.map_err(|e| {
                    error!("Failed to read chunk_index field: {}", e);
                    (
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "FIELD_READ_ERROR".to_string(),
                            message: "Failed to read chunk_index field".to_string(),
                            details: Some(e.to_string()),
                        }),
                    )
                })?;
                
                let index_str = String::from_utf8(data.to_vec()).map_err(|e| {
                    (
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "INVALID_UTF8".to_string(),
                            message: "chunk_index must be valid UTF-8".to_string(),
                            details: Some(e.to_string()),
                        }),
                    )
                })?;
                
                chunk_index = Some(index_str.trim().parse::<u32>().map_err(|e| {
                    (
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "INVALID_NUMBER".to_string(),
                            message: "chunk_index must be a valid non-negative integer".to_string(),
                            details: Some(format!("Parse error: {}", e)),
                        }),
                    )
                })?);
            }
            "chunk_data" => {
                if seen_chunk_data {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "DUPLICATE_FIELD".to_string(),
                            message: "Duplicate chunk_data field in request".to_string(),
                            details: None,
                        }),
                    ));
                }
                seen_chunk_data = true;
                
                let data = field.bytes().await.map_err(|e| {
                    error!("Failed to read chunk_data field: {}", e);
                    (
                        StatusCode::BAD_REQUEST,
                        Json(FileChunkError {
                            error_code: "FIELD_READ_ERROR".to_string(),
                            message: "Failed to read chunk_data field".to_string(),
                            details: Some(e.to_string()),
                        }),
                    )
                })?;
                
                chunk_data = Some(data.to_vec());
            }
            _ => {
                // Log and ignore unknown fields
                warn!("Ignoring unknown field in multipart form: '{}'", field_name);
            }
        }
    }

    // Validate that all required fields are present
    let transfer_id = transfer_id.ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "MISSING_FIELD".to_string(),
                message: "Missing required field: transfer_id".to_string(),
                details: Some("The transfer_id field is required for chunk uploads".to_string()),
            }),
        )
    })?;

    let chunk_index = chunk_index.ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "MISSING_FIELD".to_string(),
                message: "Missing required field: chunk_index".to_string(),
                details: Some("The chunk_index field is required for chunk uploads".to_string()),
            }),
        )
    })?;

    let chunk_data = chunk_data.ok_or_else(|| {
        (
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "MISSING_FIELD".to_string(),
                message: "Missing required field: chunk_data".to_string(),
                details: Some("The chunk_data field is required for chunk uploads".to_string()),
            }),
        )
    })?;

    Ok(FileChunkRequest {
        transfer_id,
        chunk_index,
        chunk_data,
    })
}

/// Validate a parsed FileChunkRequest
/// 
/// This function performs business logic validation on the request data
/// to ensure it meets the requirements for chunk storage
fn validate_chunk_request(
    request: &FileChunkRequest,
    max_chunk_size: usize,
) -> Result<(), (StatusCode, Json<FileChunkError>)> {
    // Validate transfer_id format and content
    if request.transfer_id.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "INVALID_TRANSFER_ID".to_string(),
                message: "transfer_id cannot be empty".to_string(),
                details: None,
            }),
        ));
    }
    
    if request.transfer_id.len() > 255 {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "INVALID_TRANSFER_ID".to_string(),
                message: "transfer_id is too long (maximum 255 characters)".to_string(),
                details: Some(format!("Provided length: {}", request.transfer_id.len())),
            }),
        ));
    }
    
    // Check for potentially dangerous characters in transfer_id
    if request.transfer_id.contains("..") 
        || request.transfer_id.contains('/') 
        || request.transfer_id.contains('\\')
        || request.transfer_id.contains('\0') {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "INVALID_TRANSFER_ID".to_string(),
                message: "transfer_id contains invalid characters".to_string(),
                details: Some("transfer_id cannot contain path separators or null bytes".to_string()),
            }),
        ));
    }
    
    // Validate chunk_index is within reasonable bounds
    const MAX_CHUNK_INDEX: u32 = 1_000_000; // 1 million chunks should be more than enough
    if request.chunk_index > MAX_CHUNK_INDEX {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "INVALID_CHUNK_INDEX".to_string(),
                message: format!("chunk_index {} exceeds maximum allowed value {}", 
                    request.chunk_index, MAX_CHUNK_INDEX),
                details: None,
            }),
        ));
    }
    
    // Validate chunk_data size
    if request.chunk_data.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(FileChunkError {
                error_code: "EMPTY_CHUNK".to_string(),
                message: "chunk_data cannot be empty".to_string(),
                details: None,
            }),
        ));
    }
    
    if request.chunk_data.len() > max_chunk_size {
        return Err((
            StatusCode::PAYLOAD_TOO_LARGE,
            Json(FileChunkError {
                error_code: "CHUNK_TOO_LARGE".to_string(),
                message: format!("chunk_data size {} exceeds maximum allowed size {}", 
                    request.chunk_data.len(), max_chunk_size),
                details: Some("Consider splitting large chunks into smaller pieces".to_string()),
            }),
        ));
    }
    
    debug!(
        "Validated chunk request: transfer_id='{}', chunk_index={}, size={} bytes",
        request.transfer_id, request.chunk_index, request.chunk_data.len()
    );
    
    Ok(())
}
