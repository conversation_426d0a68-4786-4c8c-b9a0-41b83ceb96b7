use axum::extract::ws::{Message, WebSocket};
use dashmap::DashMap;
use futures_util::sink::SinkExt;
use futures_util::stream::{SplitSink, StreamExt};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use tracing::{error, info, warn};

use super::error::HandlerError;
use indidus_shared::validation::PeerId;

/// Messages sent from client to server over WebSocket connection
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ClientMessage {
    /// Register a peer with the server using a unique peer ID
    Register { peer_id: PeerId },
    /// Relay an encrypted message to another peer
    Relay {
        recipient_id: PeerId,
        payload: Vec<u8>,
    },
    /// Heartbeat to keep connection alive
    Ping,
    /// Graceful disconnection notification
    Disconnect,
}

/// Messages sent from server to client over WebSocket connection
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ServerMessage {
    /// Confirmation that peer registration was successful
    RegisterSuccess { peer_id: PeerId },
    /// Relay an encrypted message from another peer
    MessageRelayed { sender_id: PeerId, payload: Vec<u8> },
    /// Response to ping
    Pong,
    /// Error occurred during message processing
    Error { reason: String },
    /// Notification that a peer has disconnected
    PeerDisconnected { peer_id: PeerId },
}

/// Type alias for WebSocket write half
pub type WebSocketSink = SplitSink<WebSocket, Message>;

/// Type alias for message sender channel
pub type MessageSender = mpsc::UnboundedSender<ServerMessage>;

/// Thread-safe routing table for managing peer connections
#[derive(Debug, Clone)]
pub struct RoutingTable {
    /// Internal mapping of peer IDs to message sender channels
    connections: Arc<DashMap<String, MessageSender>>,
}

impl RoutingTable {
    /// Create a new empty routing table
    pub fn new() -> Self {
        Self {
            connections: Arc::new(DashMap::new()),
        }
    }

    /// Insert a new peer connection into the routing table
    pub async fn insert(&self, peer_id: PeerId, sender: MessageSender) {
        self.connections.insert(peer_id.to_string(), sender);
    }

    /// Remove a peer connection from the routing table
    pub async fn remove(&self, peer_id: &PeerId) -> Option<MessageSender> {
        self.connections.remove(peer_id.as_str()).map(|(_, sender)| sender)
    }

    /// Send a message to a specific peer
    /// Returns true if the message was sent successfully, false if the peer is not connected
    pub async fn send_to_peer(&self, peer_id: &PeerId, message: ServerMessage) -> bool {
        if let Some(sender) = self.connections.get(peer_id.as_str()) {
            sender.send(message).is_ok()
        } else {
            false
        }
    }

    /// Check if a peer is currently connected
    pub async fn contains_peer(&self, peer_id: &PeerId) -> bool {
        self.connections.contains_key(peer_id.as_str())
    }

    /// Get the number of active connections
    pub async fn connection_count(&self) -> usize {
        self.connections.len()
    }

    /// Get a list of all connected peer IDs
    pub async fn connected_peers(&self) -> Vec<String> {
        self.connections.iter().map(|entry| entry.key().clone()).collect()
    }
}

impl Default for RoutingTable {
    fn default() -> Self {
        Self::new()
    }
}

/// Handle a WebSocket connection for a single client
///
/// This function manages the lifecycle of a client connection, including:
/// - Enforcing peer registration as the first message
/// - Setting up message channels for communication
/// - Processing incoming messages in a loop
/// - Deserializing messages into ClientMessage enum
/// - Robust connection cleanup with routing table management
pub async fn handle_websocket(
    socket: WebSocket,
    routing_table: RoutingTable,
) -> Result<(), HandlerError> {
    info!("New WebSocket connection established");

    // Split the WebSocket into read and write halves
    let (mut sink, mut stream) = socket.split();

    // Create a channel for sending messages to this client
    let (message_tx, mut message_rx) = mpsc::unbounded_channel::<ServerMessage>();

    // Track the peer ID and registration state
    let mut peer_id: Option<PeerId> = None;
    let mut is_registered = false;

    // Create a cleanup guard to ensure peer is removed from routing table
    let cleanup_guard = ConnectionCleanupGuard::new(routing_table.clone());

    // Spawn a task to handle outgoing messages
    let outgoing_task = tokio::spawn(async move {
        while let Some(message) = message_rx.recv().await {
            if let Err(e) = send_server_message(&mut sink, message).await {
                error!("Failed to send message to client: {}", e);
                break;
            }
        }
    });

    // Main message processing loop
    while let Some(message_result) = stream.next().await {
        match message_result {
            Ok(message) => {
                match process_websocket_message(
                    message,
                    &message_tx,
                    &routing_table,
                    &mut peer_id,
                    &mut is_registered,
                )
                .await
                {
                    Ok(should_continue) => {
                        if !should_continue {
                            info!("Client requested disconnection");
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Error processing message: {}", e);
                        // Send error response to client
                        let error_msg = ServerMessage::Error {
                            reason: e.to_string(),
                        };
                        if let Err(send_err) = message_tx.send(error_msg) {
                            error!("Failed to send error message: {}", send_err);
                            break;
                        }
                        // For critical errors (like unregistered client), disconnect
                        if matches!(e, HandlerError::AuthenticationFailed(_)) {
                            break;
                        }
                    }
                }
            }
            Err(e) => {
                error!("WebSocket error: {}", e);
                break;
            }
        }
    }

    // Set the peer_id in cleanup guard for proper cleanup
    if let Some(peer_id) = peer_id {
        cleanup_guard.set_peer_id(peer_id).await;
    }

    // Cancel the outgoing task
    outgoing_task.abort();

    info!("WebSocket connection closed");
    Ok(())
}

/// Cleanup guard to ensure peer is removed from routing table on connection drop
struct ConnectionCleanupGuard {
    routing_table: RoutingTable,
    peer_id: Arc<Mutex<Option<PeerId>>>,
}

impl ConnectionCleanupGuard {
    fn new(routing_table: RoutingTable) -> Self {
        Self {
            routing_table,
            peer_id: Arc::new(Mutex::new(None)),
        }
    }

    async fn set_peer_id(&self, peer_id: PeerId) {
        let mut guard = self.peer_id.lock().await;
        *guard = Some(peer_id);
    }
}

impl Drop for ConnectionCleanupGuard {
    fn drop(&mut self) {
        let routing_table = self.routing_table.clone();
        let peer_id = self.peer_id.clone();

        // Spawn a task to handle async cleanup
        tokio::spawn(async move {
            let peer_id_guard = peer_id.lock().await;
            if let Some(ref peer_id) = *peer_id_guard {
                info!(
                    "Cleanup guard removing peer from routing table: {}",
                    peer_id
                );
                routing_table.remove(&peer_id).await;
            }
        });
    }
}

/// Process a single WebSocket message
///
/// Returns Ok(true) to continue processing, Ok(false) to disconnect gracefully,
/// or Err for error conditions
async fn process_websocket_message(
    message: Message,
    message_tx: &MessageSender,
    routing_table: &RoutingTable,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, HandlerError> {
    match message {
        Message::Text(text) => {
            // Deserialize the message
            let client_message: ClientMessage = serde_json::from_str(&text)
                .map_err(|e| HandlerError::InvalidRequest(format!("Invalid JSON: {}", e)))?;

            process_client_message(
                client_message,
                message_tx,
                routing_table,
                peer_id,
                is_registered,
            )
            .await
        }
        Message::Binary(data) => {
            // For binary messages, try to deserialize as JSON
            let client_message: ClientMessage = serde_json::from_slice(&data)
                .map_err(|e| HandlerError::InvalidRequest(format!("Invalid binary JSON: {}", e)))?;

            process_client_message(
                client_message,
                message_tx,
                routing_table,
                peer_id,
                is_registered,
            )
            .await
        }
        Message::Ping(_data) => {
            // For WebSocket ping frames, we need to handle this at the WebSocket level
            // This is typically handled automatically by the WebSocket implementation
            // For now, we'll just acknowledge it
            Ok(true)
        }
        Message::Pong(_) => {
            // Pong received, continue processing
            Ok(true)
        }
        Message::Close(_) => {
            info!("Received close frame");
            Ok(false)
        }
    }
}

/// Process a deserialized client message
async fn process_client_message(
    message: ClientMessage,
    message_tx: &MessageSender,
    routing_table: &RoutingTable,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, HandlerError> {
    match message {
        ClientMessage::Register {
            peer_id: new_peer_id,
        } => {
            info!("Peer registration request: {}", new_peer_id);

            // Check if already registered
            if *is_registered {
                return Err(HandlerError::InvalidRequest(
                    "Peer is already registered. Multiple registrations not allowed.".to_string(),
                ));
            }

            // PeerId validation is already enforced at the type level during deserialization
            // No need for additional validation here

            // Check if peer_id is already taken
            if routing_table.contains_peer(&new_peer_id).await {
                return Err(HandlerError::InvalidRequest(format!(
                    "Peer ID '{}' is already in use",
                    new_peer_id
                )));
            }

            // Store the peer in the routing table
            routing_table
                .insert(new_peer_id.clone(), message_tx.clone())
                .await;

            // Update connection state
            *peer_id = Some(new_peer_id.clone());
            *is_registered = true;

            info!("Peer '{}' successfully registered", new_peer_id);

            // Send success response
            let response = ServerMessage::RegisterSuccess {
                peer_id: new_peer_id,
            };
            message_tx.send(response).map_err(|_| {
                HandlerError::InternalError("Failed to send registration response".to_string())
            })?;

            Ok(true)
        }
        ClientMessage::Relay {
            recipient_id,
            payload,
        } => {
            // Enforce registration requirement
            if !*is_registered {
                return Err(HandlerError::AuthenticationFailed(
                    "Must register before sending messages".to_string(),
                ));
            }

            let sender_id = peer_id.as_ref().unwrap(); // Safe because is_registered is true
            info!(
                "Message relay request from '{}' to '{}'",
                sender_id, recipient_id
            );

            // Construct the relayed message
            let relayed_message = ServerMessage::MessageRelayed {
                sender_id: sender_id.clone(),
                payload,
            };

            // Send the message to the recipient using the routing table
            if routing_table
                .send_to_peer(&recipient_id, relayed_message)
                .await
            {
                info!(
                    "Successfully relayed message from '{}' to '{}'",
                    sender_id, recipient_id
                );
            } else {
                // Recipient not found or connection failed
                warn!(
                    "Failed to relay message to recipient '{}' - not connected or connection error",
                    recipient_id
                );

                // Remove the recipient from routing table in case of connection error
                routing_table.remove(&recipient_id).await;

                // Send error back to sender
                let error_response = ServerMessage::Error {
                    reason: format!(
                        "Recipient '{}' is not connected or unreachable",
                        recipient_id
                    ),
                };
                message_tx.send(error_response).map_err(|_| {
                    HandlerError::InternalError("Failed to send error response".to_string())
                })?;
            }

            Ok(true)
        }
        ClientMessage::Ping => {
            // Ping is allowed even without registration
            let response = ServerMessage::Pong;
            message_tx.send(response).map_err(|_| {
                HandlerError::InternalError("Failed to send pong response".to_string())
            })?;
            Ok(true)
        }
        ClientMessage::Disconnect => {
            info!("Client requested disconnection");
            Ok(false)
        }
    }
}

/// Helper function to send a ServerMessage to the client
async fn send_server_message(
    sink: &mut SplitSink<WebSocket, Message>,
    message: ServerMessage,
) -> Result<(), HandlerError> {
    let json = serde_json::to_string(&message).map_err(|e| {
        HandlerError::SerializationError(format!("Failed to serialize message: {}", e))
    })?;

    sink.send(Message::Text(json))
        .await
        .map_err(|e| HandlerError::InternalError(format!("Failed to send message: {}", e)))?;

    Ok(())
}
