use std::collections::HashMap;

use axum::body::Body;
use axum::http::{Request, StatusCode};
use reqwest::multipart::{Form, Part};
use tempfile::TempDir;
use tokio::fs;
use tower::ServiceExt;
use uuid::Uuid;

use crate::handlers::file_transfer::FileChunkResponse;
use crate::handlers::traits::{FileTransferStatus, RelayMessageRequest, TransferStatus};
use crate::storage::StorageConfig;

use super::error::HandlerError;

#[tokio::test]
async fn test_file_chunk_endpoint_integration() {
    // Create a temporary directory for testing
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let storage_config = StorageConfig {
        base_dir: temp_dir.path().to_path_buf(),
        max_chunk_size: 10 * 1024 * 1024, // 10MB
        max_chunks_per_transfer: 1000,
    };

    // Create the router with test storage configuration
    let app = crate::server::Server::create_router_with_storage(storage_config);

    // Test data
    let transfer_id = "test_transfer_12345";
    let chunk_index = 42u32;
    let chunk_data = b"This is test chunk data for integration testing!";

    // Create multipart form data
    let form = Form::new()
        .text("transfer_id", transfer_id)
        .text("chunk_index", chunk_index.to_string())
        .part("chunk_data", Part::bytes(chunk_data.to_vec()));

    // Send the request
    let response = send_multipart_request(&app, form).await;

    // Assert the response status
    assert_eq!(response.status(), StatusCode::OK);

    // Parse the response body
    let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .expect("Failed to read response body");

    let response_json: FileChunkResponse =
        serde_json::from_slice(&body_bytes).expect("Failed to parse response JSON");

    // Assert response content
    assert_eq!(response_json.transfer_id, transfer_id);
    assert_eq!(response_json.chunk_index, chunk_index);
    assert!(response_json.message.contains("stored successfully"));

    // Verify the file was actually created on disk
    let expected_file_path = temp_dir
        .path()
        .join(transfer_id)
        .join(chunk_index.to_string());

    assert!(expected_file_path.exists(), "Chunk file was not created");

    // Verify the file content
    let stored_data = fs::read(&expected_file_path)
        .await
        .expect("Failed to read stored chunk file");

    assert_eq!(
        stored_data, chunk_data,
        "Stored data does not match original"
    );
}

#[tokio::test]
async fn test_file_chunk_endpoint_validation_errors() {
    // Create a temporary directory for testing
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let storage_config = StorageConfig {
        base_dir: temp_dir.path().to_path_buf(),
        max_chunk_size: 100, // Very small for testing
        max_chunks_per_transfer: 1000,
    };

    // Create the router with test storage configuration
    let app = crate::server::Server::create_router_with_storage(storage_config);

    // Test 1: Missing transfer_id
    let response = send_multipart_request_with_data(
        &app,
        None, // missing transfer_id
        Some("0"),
        Some(b"test"),
    )
    .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test 2: Missing chunk_index
    let response = send_multipart_request_with_data(
        &app,
        Some("test_transfer"),
        None, // missing chunk_index
        Some(b"test"),
    )
    .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test 3: Missing chunk_data
    let response = send_multipart_request_with_data(
        &app,
        Some("test_transfer"),
        Some("0"),
        None, // missing chunk_data
    )
    .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test 4: Invalid chunk_index (not a number)
    let response = send_multipart_request_with_data(
        &app,
        Some("test_transfer"),
        Some("not_a_number"),
        Some(b"test"),
    )
    .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test 5: Chunk too large
    let large_data = vec![0u8; 200]; // Larger than max_chunk_size (100)
    let response =
        send_multipart_request_with_data(&app, Some("test_transfer"), Some("0"), Some(&large_data))
            .await;
    assert_eq!(response.status(), StatusCode::PAYLOAD_TOO_LARGE);

    // Test 6: Invalid transfer_id (path traversal attempt)
    let response =
        send_multipart_request_with_data(&app, Some("../evil_path"), Some("0"), Some(b"test"))
            .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);
}

#[tokio::test]
async fn test_file_chunk_endpoint_duplicate_chunk() {
    // Create a temporary directory for testing
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let storage_config = StorageConfig {
        base_dir: temp_dir.path().to_path_buf(),
        max_chunk_size: 10 * 1024 * 1024,
        max_chunks_per_transfer: 1000,
    };

    // Create the router with test storage configuration
    let app = crate::server::Server::create_router_with_storage(storage_config);

    let transfer_id = "test_duplicate";
    let chunk_index = 0u32;
    let chunk_data = b"duplicate test data";

    // Send the first request (should succeed)
    let response = send_multipart_request_with_data(
        &app,
        Some(transfer_id),
        Some(&chunk_index.to_string()),
        Some(chunk_data),
    )
    .await;
    assert_eq!(response.status(), StatusCode::OK);

    // Send the same request again (should fail with conflict)
    let response = send_multipart_request_with_data(
        &app,
        Some(transfer_id),
        Some(&chunk_index.to_string()),
        Some(chunk_data),
    )
    .await;
    assert_eq!(response.status(), StatusCode::CONFLICT);
}

// Helper function to send multipart requests in tests
async fn send_multipart_request(app: &axum::Router, _form: Form) -> axum::response::Response {
    send_multipart_request_with_data(
        app,
        Some("test_transfer_12345"),
        Some("42"),
        Some(b"This is test chunk data for integration testing!"),
    )
    .await
}

// More flexible helper function for different test scenarios
async fn send_multipart_request_with_data(
    app: &axum::Router,
    transfer_id: Option<&str>,
    chunk_index: Option<&str>,
    chunk_data: Option<&[u8]>,
) -> axum::response::Response {
    let boundary = "----formdata-test-boundary";
    let mut body = String::new();

    // Add transfer_id field if provided
    if let Some(id) = transfer_id {
        body.push_str(&format!("--{}\r\n", boundary));
        body.push_str("Content-Disposition: form-data; name=\"transfer_id\"\r\n\r\n");
        body.push_str(&format!("{}\r\n", id));
    }

    // Add chunk_index field if provided
    if let Some(index) = chunk_index {
        body.push_str(&format!("--{}\r\n", boundary));
        body.push_str("Content-Disposition: form-data; name=\"chunk_index\"\r\n\r\n");
        body.push_str(&format!("{}\r\n", index));
    }

    // Add chunk_data field if provided
    if let Some(data) = chunk_data {
        body.push_str(&format!("--{}\r\n", boundary));
        body.push_str("Content-Disposition: form-data; name=\"chunk_data\"\r\n\r\n");
        // Don't add extra newline for binary data
        body.push_str(&String::from_utf8_lossy(data));
        body.push_str("\r\n");
    }

    body.push_str(&format!("--{}--\r\n", boundary));

    let content_type = format!("multipart/form-data; boundary={}", boundary);

    let request = Request::builder()
        .method("POST")
        .uri("/v1/files/chunk")
        .header("content-type", content_type)
        .body(Body::from(body))
        .expect("Failed to build test request");

    app.clone()
        .oneshot(request)
        .await
        .expect("Failed to execute request")
}

#[test]
fn test_relay_message_request_serialization() {
    let request = RelayMessageRequest {
        sender_id: Uuid::new_v4(),
        recipient_id: Uuid::new_v4(),
        encrypted_payload: vec![1, 2, 3, 4],
        metadata: HashMap::new(),
        message_type: "text".to_string(),
    };

    let json = serde_json::to_string(&request).unwrap();
    let deserialized: RelayMessageRequest = serde_json::from_str(&json).unwrap();

    assert_eq!(request.sender_id, deserialized.sender_id);
    assert_eq!(request.recipient_id, deserialized.recipient_id);
    assert_eq!(request.encrypted_payload, deserialized.encrypted_payload);
}

#[test]
fn test_file_transfer_status() {
    let status = FileTransferStatus {
        transfer_id: Uuid::new_v4(),
        status: TransferStatus::Uploading,
        bytes_uploaded: 1024,
        bytes_downloaded: 0,
        total_size: 2048,
        created_at: std::time::SystemTime::now(),
        expires_at: std::time::SystemTime::now() + std::time::Duration::from_secs(3600),
    };

    assert_eq!(status.bytes_uploaded, 1024);
    assert_eq!(status.total_size, 2048);
    assert!(matches!(status.status, TransferStatus::Uploading));
}

#[test]
fn test_handler_error_display() {
    let error = HandlerError::ClientNotFound(Uuid::new_v4());
    let error_string = format!("{}", error);
    assert!(error_string.contains("Client not found"));

    let auth_error = HandlerError::AuthenticationFailed("Invalid token".to_string());
    let auth_string = format!("{}", auth_error);
    assert!(auth_string.contains("Authentication failed"));
}
