//! Generic WebSocket message handling components
//!
//! This module provides framework-agnostic abstractions for handling WebSocket
//! connections and processing messages in the Indidus E2EE server.

use async_trait::async_trait;
use std::fmt::Debug;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{debug, error, info, warn};

use crate::handlers::{
    error::HandlerError,
    websocket::{ClientMessage, RoutingTable, ServerMessage},
};
use indidus_shared::validation::PeerId;

/// Generic message type for WebSocket communication
///
/// This enum represents the different types of messages that can be sent
/// over a WebSocket connection, abstracting away framework-specific message types.
#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub enum Message {
    /// Text message containing UTF-8 encoded string data
    Text(String),
    /// Binary message containing raw bytes
    Binary(Vec<u8>),
    /// Close message indicating connection termination
    Close,
    /// Ping message for connection keep-alive
    Ping(Vec<u8>),
    /// Pong message responding to ping
    Pong(Vec<u8>),
}

/// Error type for WebSocket stream operations
#[derive(Debug, thiserror::Error)]
pub enum WebSocketError {
    /// Connection was closed unexpectedly
    #[error("WebSocket connection closed")]
    ConnectionClosed,
    /// Failed to send message
    #[error("Failed to send message: {0}")]
    SendError(String),
    /// Failed to receive message
    #[error("Failed to receive message: {0}")]
    ReceiveError(String),
    /// Invalid message format
    #[error("Invalid message format: {0}")]
    InvalidMessage(String),
    /// IO error occurred
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
}

/// Generic trait for WebSocket stream operations
///
/// This trait abstracts the core functionality of a WebSocket connection,
/// making it framework-agnostic and reusable across different WebSocket
/// implementations (e.g., axum, warp, tokio-tungstenite).
#[async_trait]
pub trait WebSocketStream: Send + Sync + Debug {
    /// Send a message through the WebSocket connection
    ///
    /// # Arguments
    /// * `message` - The message to send
    ///
    /// # Returns
    /// * `Ok(())` if the message was sent successfully
    /// * `Err(WebSocketError)` if sending failed
    async fn send(&mut self, message: Message) -> Result<(), WebSocketError>;

    /// Receive the next message from the WebSocket connection
    ///
    /// # Returns
    /// * `Some(Ok(message))` if a message was received successfully
    /// * `Some(Err(error))` if an error occurred while receiving
    /// * `None` if the connection was closed gracefully
    async fn next(&mut self) -> Option<Result<Message, WebSocketError>>;

    /// Close the WebSocket connection gracefully
    ///
    /// # Returns
    /// * `Ok(())` if the connection was closed successfully
    /// * `Err(WebSocketError)` if closing failed
    async fn close(&mut self) -> Result<(), WebSocketError>;

    /// Check if the connection is still active
    ///
    /// # Returns
    /// * `true` if the connection is active
    /// * `false` if the connection is closed or broken
    fn is_active(&self) -> bool;
}

/// Result type alias for WebSocket operations
pub type WebSocketResult<T> = Result<T, WebSocketError>;

/// Generic message handler for processing WebSocket connections
///
/// This struct provides a framework-agnostic way to handle WebSocket
/// connections and process messages using the routing table for
/// message distribution.
#[derive(Debug, Clone)]
pub struct MessageHandler {
    /// Reference to the routing table for managing peer connections
    routing_table: Arc<RoutingTable>,
}

impl MessageHandler {
    /// Create a new message handler with the given routing table
    ///
    /// # Arguments
    /// * `routing_table` - Shared routing table for managing connections
    ///
    /// # Returns
    /// A new `MessageHandler` instance
    pub fn new(routing_table: Arc<RoutingTable>) -> Self {
        Self { routing_table }
    }

    /// Get a reference to the routing table
    pub fn routing_table(&self) -> &Arc<RoutingTable> {
        &self.routing_table
    }
}

/// Handle a WebSocket connection using the generic WebSocketStream trait
///
/// This function provides a framework-agnostic way to process WebSocket
/// connections. It takes any type that implements the `WebSocketStream` trait
/// and handles the message processing loop with full client registration,
/// message relay, and connection management.
///
/// # Arguments
/// * `stream` - The WebSocket stream to handle
/// * `routing_table` - Shared routing table for managing peer connections
///
/// # Type Parameters
/// * `S` - Any type that implements `WebSocketStream`
///
/// # Returns
/// * `Ok(())` if the connection was handled successfully
/// * `Err(WebSocketError)` if an error occurred during processing
pub async fn handle_connection<S>(
    mut stream: S,
    routing_table: Arc<RoutingTable>,
) -> WebSocketResult<()>
where
    S: WebSocketStream,
{
    info!("Starting WebSocket connection handler");

    // Track the peer ID and registration state
    let mut peer_id: Option<PeerId> = None;
    let mut is_registered = false;

    // Main message processing loop
    while let Some(message_result) = stream.next().await {
        match message_result {
            Ok(message) => {
                debug!("Received message: {:?}", message);

                // Process the message based on its type
                match process_generic_message(
                    message,
                    &mut stream,
                    &routing_table,
                    &mut peer_id,
                    &mut is_registered,
                )
                .await
                {
                    Ok(should_continue) => {
                        if !should_continue {
                            info!("Client requested disconnection");
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Error processing message: {}", e);
                        // Send error response to client
                        let error_msg = ServerMessage::Error {
                            reason: e.to_string(),
                        };
                        if let Err(_) = send_server_message(&mut stream, error_msg).await {
                            error!("Failed to send error message");
                            break;
                        }
                        // For critical errors (like unregistered client), disconnect
                        if matches!(e, HandlerError::AuthenticationFailed(_)) {
                            break;
                        }
                    }
                }
            }
            Err(e) => {
                error!("Error receiving message: {}", e);
                break;
            }
        }
    }

    // Cleanup: remove peer from routing table
    if let Some(ref peer_id) = peer_id {
        info!("Removing peer from routing table: {}", peer_id);
        routing_table.remove(peer_id).await;
    }

    info!("WebSocket connection handler finished");
    Ok(())
}

/// Process a generic WebSocket message using the framework-agnostic Message enum
///
/// Returns Ok(true) to continue processing, Ok(false) to disconnect gracefully,
/// or Err for error conditions
async fn process_generic_message<S>(
    message: Message,
    stream: &mut S,
    routing_table: &Arc<RoutingTable>,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, HandlerError>
where
    S: WebSocketStream,
{
    match message {
        Message::Text(text) => {
            // Deserialize the message
            let client_message: ClientMessage = serde_json::from_str(&text)
                .map_err(|e| HandlerError::InvalidRequest(format!("Invalid JSON: {}", e)))?;

            process_client_message(
                client_message,
                stream,
                routing_table,
                peer_id,
                is_registered,
            )
            .await
        }
        Message::Binary(data) => {
            // For binary messages, try to deserialize as JSON
            let client_message: ClientMessage = serde_json::from_slice(&data)
                .map_err(|e| HandlerError::InvalidRequest(format!("Invalid binary JSON: {}", e)))?;

            process_client_message(
                client_message,
                stream,
                routing_table,
                peer_id,
                is_registered,
            )
            .await
        }
        Message::Ping(data) => {
            // Respond to ping with pong containing the same data
            stream.send(Message::Pong(data)).await.map_err(|e| {
                HandlerError::InternalError(format!("Failed to send pong response: {}", e))
            })?;
            Ok(true)
        }
        Message::Pong(_) => {
            // Pong received, continue processing
            Ok(true)
        }
        Message::Close => {
            info!("Received close frame");
            Ok(false)
        }
    }
}

/// Process a deserialized client message
async fn process_client_message<S>(
    message: ClientMessage,
    stream: &mut S,
    routing_table: &Arc<RoutingTable>,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, HandlerError>
where
    S: WebSocketStream,
{
    match message {
        ClientMessage::Register {
            peer_id: new_peer_id,
        } => {
            info!("Peer registration request: {}", new_peer_id);

            // Check if already registered
            if *is_registered {
                return Err(HandlerError::InvalidRequest(
                    "Peer is already registered. Multiple registrations not allowed.".to_string(),
                ));
            }

            // PeerId validation is already enforced at the type level during deserialization
            // No need for additional validation here

            // Check if peer_id is already taken
            if routing_table.contains_peer(&new_peer_id).await {
                return Err(HandlerError::InvalidRequest(format!(
                    "Peer ID '{}' is already in use",
                    new_peer_id
                )));
            }

            // Create a dummy channel for routing table compatibility
            // Note: This is a limitation of the current routing table design
            // In a real implementation, we'd refactor RoutingTable to work with generic streams
            let (dummy_tx, _dummy_rx) = mpsc::unbounded_channel::<ServerMessage>();

            // Store the peer in the routing table
            routing_table.insert(new_peer_id.clone(), dummy_tx).await;

            // Update connection state
            *peer_id = Some(new_peer_id.clone());
            *is_registered = true;

            info!("Peer '{}' successfully registered", new_peer_id);

            // Send success response
            let response = ServerMessage::RegisterSuccess {
                peer_id: new_peer_id,
            };
            send_server_message(stream, response).await.map_err(|e| {
                HandlerError::InternalError(format!("Failed to send registration response: {}", e))
            })?;

            Ok(true)
        }
        ClientMessage::Relay {
            recipient_id,
            payload,
        } => {
            // Enforce registration requirement
            if !*is_registered {
                return Err(HandlerError::AuthenticationFailed(
                    "Must register before sending messages".to_string(),
                ));
            }

            let sender_id = peer_id.as_ref().unwrap(); // Safe because is_registered is true
            info!(
                "Message relay request from '{}' to '{}'",
                sender_id, recipient_id
            );

            // Construct the relayed message
            let relayed_message = ServerMessage::MessageRelayed {
                sender_id: sender_id.clone(),
                payload,
            };

            // Send the message to the recipient using the routing table
            if routing_table
                .send_to_peer(&recipient_id, relayed_message)
                .await
            {
                info!(
                    "Successfully relayed message from '{}' to '{}'",
                    sender_id, recipient_id
                );
            } else {
                // Recipient not found or connection failed
                warn!(
                    "Failed to relay message to recipient '{}' - not connected or connection error",
                    recipient_id
                );

                // Remove the recipient from routing table in case of connection error
                routing_table.remove(&recipient_id).await;

                // Send error back to sender
                let error_response = ServerMessage::Error {
                    reason: format!(
                        "Recipient '{}' is not connected or unreachable",
                        recipient_id
                    ),
                };
                send_server_message(stream, error_response)
                    .await
                    .map_err(|e| {
                        HandlerError::InternalError(format!("Failed to send error response: {}", e))
                    })?;
            }

            Ok(true)
        }
        ClientMessage::Ping => {
            // Ping is allowed even without registration
            let response = ServerMessage::Pong;
            send_server_message(stream, response).await.map_err(|e| {
                HandlerError::InternalError(format!("Failed to send pong response: {}", e))
            })?;
            Ok(true)
        }
        ClientMessage::Disconnect => {
            info!("Client requested disconnection");
            Ok(false)
        }
    }
}

/// Helper function to send a ServerMessage to the client using generic WebSocketStream
async fn send_server_message<S>(stream: &mut S, message: ServerMessage) -> WebSocketResult<()>
where
    S: WebSocketStream,
{
    let json = serde_json::to_string(&message).map_err(|e| {
        WebSocketError::InvalidMessage(format!("Failed to serialize message: {}", e))
    })?;

    stream.send(Message::Text(json)).await
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::VecDeque;
    use tokio::sync::Mutex;

    /// Mock WebSocket implementation for testing
    ///
    /// This mock allows controlled testing of the message handler without
    /// requiring real network connections. It uses internal channels and
    /// vectors to simulate WebSocket behavior.
    #[derive(Debug, Clone)]
    pub struct MockWebSocket {
        /// Queue of incoming messages to be returned by next()
        incoming_messages: Arc<Mutex<VecDeque<Option<Result<Message, WebSocketError>>>>>,
        /// Vector to capture outgoing messages sent via send()
        outgoing_messages: Arc<Mutex<Vec<Message>>>,
        /// Flag to track if the connection is active
        is_active: Arc<Mutex<bool>>,
    }

    impl MockWebSocket {
        /// Create a new mock WebSocket
        pub fn new() -> Self {
            Self {
                incoming_messages: Arc::new(Mutex::new(VecDeque::new())),
                outgoing_messages: Arc::new(Mutex::new(Vec::new())),
                is_active: Arc::new(Mutex::new(true)),
            }
        }

        /// Add an incoming message to the mock's queue
        ///
        /// # Arguments
        /// * `message` - The message to add to the incoming queue
        pub async fn add_incoming_message(&self, message: Message) {
            let mut queue = self.incoming_messages.lock().await;
            queue.push_back(Some(Ok(message)));
        }

        // /// Add an incoming error to the mock's queue
        // ///
        // /// # Arguments
        // /// * `error` - The error to add to the incoming queue
        // pub async fn add_incoming_error(&self, error: WebSocketError) {
        //     let mut queue = self.incoming_messages.lock().await;
        //     queue.push_back(Some(Err(error)));
        // }

        /// Signal that the connection should close (next() will return None)
        pub async fn close_connection(&self) {
            let mut queue = self.incoming_messages.lock().await;
            queue.push_back(None);
            // Don't set is_active to false immediately - let the handler finish processing
            // The connection will be marked as inactive when close() is called by the handler
        }

        /// Get all outgoing messages that were sent via send()
        ///
        /// # Returns
        /// A vector of all messages sent through this mock
        pub async fn get_outgoing_messages(&self) -> Vec<Message> {
            let messages = self.outgoing_messages.lock().await;
            messages.clone()
        }

        // /// Get the last outgoing message sent via send()
        // ///
        // /// # Returns
        // /// The last message sent, or None if no messages were sent
        // pub async fn get_last_outgoing_message(&self) -> Option<Message> {
        //     let messages = self.outgoing_messages.lock().await;
        //     messages.last().cloned()
        // }

        // /// Clear all outgoing messages
        // pub async fn clear_outgoing_messages(&self) {
        //     let mut messages = self.outgoing_messages.lock().await;
        //     messages.clear();
        // }

        // /// Get the number of outgoing messages sent
        // pub async fn outgoing_message_count(&self) -> usize {
        //     let messages = self.outgoing_messages.lock().await;
        //     messages.len()
        // }
    }

    #[async_trait]
    impl WebSocketStream for MockWebSocket {
        async fn send(&mut self, message: Message) -> Result<(), WebSocketError> {
            let is_active = {
                let active = self.is_active.lock().await;
                *active
            };

            if !is_active {
                return Err(WebSocketError::ConnectionClosed);
            }

            let mut outgoing = self.outgoing_messages.lock().await;
            outgoing.push(message);
            Ok(())
        }

        async fn next(&mut self) -> Option<Result<Message, WebSocketError>> {
            let mut queue = self.incoming_messages.lock().await;
            queue.pop_front().flatten()
        }

        async fn close(&mut self) -> Result<(), WebSocketError> {
            let mut active = self.is_active.lock().await;
            *active = false;
            Ok(())
        }

        fn is_active(&self) -> bool {
            // For testing purposes, we'll use a blocking approach
            // In real implementations, this would be non-blocking
            tokio::task::block_in_place(|| {
                tokio::runtime::Handle::current().block_on(async {
                    let active = self.is_active.lock().await;
                    *active
                })
            })
        }
    }

    impl Default for MockWebSocket {
        fn default() -> Self {
            Self::new()
        }
    }

    // Helper function to create a test routing table
    fn create_test_routing_table() -> Arc<RoutingTable> {
        Arc::new(RoutingTable::new())
    }

    #[tokio::test]
    async fn test_mock_websocket_basic_functionality() {
        let mut mock_ws = MockWebSocket::new();

        // Test that we can send a message and retrieve it
        let test_message = Message::Text("test".to_string());
        mock_ws.send(test_message.clone()).await.unwrap();

        let outgoing_messages = mock_ws.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 1);
        assert_eq!(outgoing_messages[0], test_message);
    }

    #[tokio::test]
    async fn test_successful_client_registration() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // Simulate a registration message
        let register_msg = ClientMessage::Register {
            peer_id: PeerId::try_from("test_peer").unwrap(),
        };
        let register_json = serde_json::to_string(&register_msg).unwrap();
        mock_ws
            .add_incoming_message(Message::Text(register_json))
            .await;

        // Close the connection to end the handler loop
        mock_ws.close_connection().await;

        // Run the handler
        let mock_ws_clone = mock_ws.clone();
        let result = handle_connection(mock_ws, routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify the registration success response was sent
        let outgoing_messages = mock_ws_clone.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 1);

        if let Message::Text(response_json) = &outgoing_messages[0] {
            let response: ServerMessage = serde_json::from_str(response_json).unwrap();
            match response {
                ServerMessage::RegisterSuccess { peer_id } => {
                    assert_eq!(peer_id, PeerId::try_from("test_peer").unwrap());
                }
                _ => panic!("Expected RegisterSuccess response"),
            }
        } else {
            panic!("Expected text message");
        }

        // Note: We don't check if peer is still in routing table because
        // the handler removes peers when the connection ends (which is correct behavior)
    }

    #[tokio::test]
    async fn test_duplicate_registration_error() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // First registration
        let register_msg = ClientMessage::Register {
            peer_id: PeerId::try_from("test_peer").unwrap(),
        };
        let register_json = serde_json::to_string(&register_msg).unwrap();
        mock_ws
            .add_incoming_message(Message::Text(register_json.clone()))
            .await;

        // Second registration attempt (should fail)
        mock_ws
            .add_incoming_message(Message::Text(register_json))
            .await;

        // Close the connection
        mock_ws.close_connection().await;

        // Run the handler
        let mock_ws_clone = mock_ws.clone();
        let result = handle_connection(mock_ws, routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify we got success response followed by error response
        let outgoing_messages = mock_ws_clone.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 2);

        // First message should be success
        if let Message::Text(response_json) = &outgoing_messages[0] {
            let response: ServerMessage = serde_json::from_str(response_json).unwrap();
            assert!(matches!(response, ServerMessage::RegisterSuccess { .. }));
        }

        // Second message should be error
        if let Message::Text(response_json) = &outgoing_messages[1] {
            let response: ServerMessage = serde_json::from_str(response_json).unwrap();
            match response {
                ServerMessage::Error { reason } => {
                    assert!(reason.contains("already registered"));
                }
                _ => panic!("Expected Error response"),
            }
        }
    }

    #[tokio::test]
    async fn test_message_relay_to_nonexistent_peer() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // Register a peer first
        let register_msg = ClientMessage::Register {
            peer_id: PeerId::try_from("sender").unwrap(),
        };
        let register_json = serde_json::to_string(&register_msg).unwrap();
        mock_ws
            .add_incoming_message(Message::Text(register_json))
            .await;

        // Try to send message to non-existent peer
        let relay_msg = ClientMessage::Relay {
            recipient_id: PeerId::try_from("nonexistent_peer").unwrap(),
            payload: vec![1, 2, 3, 4],
        };
        let relay_json = serde_json::to_string(&relay_msg).unwrap();
        mock_ws
            .add_incoming_message(Message::Text(relay_json))
            .await;

        // Close the connection
        mock_ws.close_connection().await;

        // Run the handler
        let mock_ws_clone = mock_ws.clone();
        let result = handle_connection(mock_ws, routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify we got registration success and then error for failed relay
        let outgoing_messages = mock_ws_clone.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 2);

        // Second message should be error about unreachable recipient
        if let Message::Text(response_json) = &outgoing_messages[1] {
            let response: ServerMessage = serde_json::from_str(response_json).unwrap();
            match response {
                ServerMessage::Error { reason } => {
                    assert!(reason.contains("not connected or unreachable"));
                }
                _ => panic!("Expected Error response"),
            }
        }
    }

    #[tokio::test]
    async fn test_relay_without_registration() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // Try to send message without registering first
        let relay_msg = ClientMessage::Relay {
            recipient_id: PeerId::try_from("some_peer").unwrap(),
            payload: vec![1, 2, 3, 4],
        };
        let relay_json = serde_json::to_string(&relay_msg).unwrap();
        mock_ws
            .add_incoming_message(Message::Text(relay_json))
            .await;

        // Close the connection
        mock_ws.close_connection().await;

        // Run the handler
        let mock_ws_clone = mock_ws.clone();
        let result = handle_connection(mock_ws, routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify we got an authentication error
        let outgoing_messages = mock_ws_clone.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 1);

        if let Message::Text(response_json) = &outgoing_messages[0] {
            let response: ServerMessage = serde_json::from_str(response_json).unwrap();
            match response {
                ServerMessage::Error { reason } => {
                    assert!(reason.contains("Must register before sending messages"));
                }
                _ => panic!("Expected Error response"),
            }
        }
    }

    #[tokio::test]
    async fn test_ping_pong_handling() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // Send a ping message
        let ping_msg = ClientMessage::Ping;
        let ping_json = serde_json::to_string(&ping_msg).unwrap();
        mock_ws.add_incoming_message(Message::Text(ping_json)).await;

        // Close the connection
        mock_ws.close_connection().await;

        // Run the handler
        let mock_ws_clone = mock_ws.clone();
        let result = handle_connection(mock_ws, routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify we got a pong response
        let outgoing_messages = mock_ws_clone.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 1);

        if let Message::Text(response_json) = &outgoing_messages[0] {
            let response: ServerMessage = serde_json::from_str(response_json).unwrap();
            assert!(matches!(response, ServerMessage::Pong));
        }
    }

    #[tokio::test]
    async fn test_malformed_json_handling() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // Send malformed JSON
        mock_ws
            .add_incoming_message(Message::Text("invalid json".to_string()))
            .await;

        // Close the connection
        mock_ws.close_connection().await;

        // Run the handler
        let mock_ws_clone = mock_ws.clone();
        let result = handle_connection(mock_ws, routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify we got an error response
        let outgoing_messages = mock_ws_clone.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 1);

        if let Message::Text(response_json) = &outgoing_messages[0] {
            let response: ServerMessage = serde_json::from_str(response_json).unwrap();
            match response {
                ServerMessage::Error { reason } => {
                    assert!(reason.contains("Invalid JSON"));
                }
                _ => panic!("Expected Error response"),
            }
        }
    }

    #[tokio::test]
    async fn test_websocket_ping_pong_frames() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // Send WebSocket ping frame
        let ping_data = vec![1, 2, 3, 4];
        mock_ws
            .add_incoming_message(Message::Ping(ping_data.clone()))
            .await;

        // Close the connection
        mock_ws.close_connection().await;

        // Run the handler
        let mock_ws_clone = mock_ws.clone();
        let result = handle_connection(mock_ws, routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify we got a pong response with the same data
        let outgoing_messages = mock_ws_clone.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 1);

        match &outgoing_messages[0] {
            Message::Pong(pong_data) => {
                assert_eq!(*pong_data, ping_data);
            }
            _ => panic!("Expected Pong message"),
        }
    }

    #[tokio::test]
    async fn test_disconnect_message_handling() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // Register a peer first
        let register_msg = ClientMessage::Register {
            peer_id: PeerId::try_from("test_peer").unwrap(),
        };
        let register_json = serde_json::to_string(&register_msg).unwrap();
        mock_ws
            .add_incoming_message(Message::Text(register_json))
            .await;

        // Send disconnect message
        let disconnect_msg = ClientMessage::Disconnect;
        let disconnect_json = serde_json::to_string(&disconnect_msg).unwrap();
        mock_ws
            .add_incoming_message(Message::Text(disconnect_json))
            .await;

        // Run the handler
        let result = handle_connection(mock_ws.clone(), routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify peer was removed from routing table after disconnect
        assert!(!routing_table.contains_peer(&PeerId::try_from("test_peer").unwrap()).await);
    }

    #[tokio::test]
    async fn test_invalid_json_message_handling() {
        let mock_ws = MockWebSocket::new();
        let routing_table = create_test_routing_table();

        // Try to send completely malformed JSON
        let invalid_json = r#"{"type":"Register","peer_id":123}"#; // peer_id should be string, not number
        mock_ws
            .add_incoming_message(Message::Text(invalid_json.to_string()))
            .await;

        // Close the connection
        mock_ws.close_connection().await;

        // Run the handler
        let mock_ws_clone = mock_ws.clone();
        let result = handle_connection(mock_ws, routing_table.clone()).await;
        assert!(result.is_ok());

        // Verify we got an error response due to deserialization failure
        let outgoing_messages = mock_ws_clone.get_outgoing_messages().await;
        assert_eq!(outgoing_messages.len(), 1);

        if let Message::Text(response_json) = &outgoing_messages[0] {
            let response: ServerMessage = serde_json::from_str(response_json).unwrap();
            match response {
                ServerMessage::Error { reason } => {
                    assert!(reason.contains("Invalid JSON") || reason.contains("invalid type"));
                }
                _ => panic!("Expected Error response, got: {:?}", response),
            }
        } else {
            panic!("Expected text message, got: {:?}", &outgoing_messages[0]);
        }
    }
}
