//! # Indidus E2EE Server SDK
//!
//! A Rust server SDK for relaying end-to-end encrypted messages and coordinating
//! file transfers using the Indidus Signal Protocol. This crate provides the
//! infrastructure needed to run a secure messaging server.
//!
//! ## Features
//!
//! - **Message Relay**: Secure relay of encrypted messages between clients
//! - **Pre-key Management**: Storage and distribution of client pre-keys
//! - **File Transfer Coordination**: Secure file sharing infrastructure
//! - **Session Management**: Track and manage client sessions
//! - **Web Framework Integration**: Built on Axum for high performance
//!
//! ## Quick Start
//!
//! ```rust
//! use indidus_e2ee_server::ServerConfig;
//!
//! # fn main() -> Result<(), Box<dyn std::error::Error>> {
//! // Create a server configuration
//! let config = ServerConfig::with_address("0.0.0.0".to_string(), 8080)
//!     .with_max_connections(1000)
//!     .with_logging(true, "info".to_string());
//!
//! println!("Server will bind to: {}", config.socket_addr()?);
//! # Ok(())
//! # }
//! ```

pub mod config;
pub mod server;
pub mod handlers;
pub mod handler;
pub mod storage;

// Re-export main types for convenience
pub use config::ServerConfig;
pub use server::{Server, ServerStats, ClientSession};
pub use handler::{handle_connection, Message, MessageHandler, WebSocketError, WebSocketResult, WebSocketStream};
pub use handlers::{
    error::{HandlerError, HandlerResult},
    file_transfer::{
        handle_file_chunk, FileChunkError, FileChunkRequest, FileChunkResponse,
    },
    traits::{
        ClientAuthenticator, FileTransferCoordinator, FileTransferRequest, FileTransferResponse,
        FileTransferStatus, MessageRelay, PreKeyManager, PreKeyRequest, PreKeyResponse,
        RelayMessageRequest, RelayMessageResponse, TransferStatus,
    },
    websocket::{
        handle_websocket, ClientMessage, MessageSender, RoutingTable, ServerMessage, WebSocketSink,
    },
};
pub use storage::{ChunkStorageService, StorageConfig, StorageError};
