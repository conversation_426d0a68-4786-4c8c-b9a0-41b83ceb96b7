//! Temporary file chunk storage service
//!
//! This module provides functionality for temporarily storing encrypted file chunks
//! on the server's filesystem. Chunks are organized by transfer ID and chunk index
//! to facilitate efficient retrieval and assembly.

use std::path::{Path, PathBuf};
use tokio::fs;
use tokio::io::AsyncWriteExt;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Errors that can occur during chunk storage operations
#[derive(Debug, thiserror::Error)]
pub enum StorageError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Invalid transfer ID: {0}")]
    InvalidTransferId(String),
    
    #[error("Invalid chunk index: {0}")]
    InvalidChunkIndex(u32),
    
    #[error("Chunk already exists: transfer_id={transfer_id}, chunk_index={chunk_index}")]
    ChunkAlreadyExists {
        transfer_id: String,
        chunk_index: u32,
    },
    
    #[error("Transfer directory creation failed: {0}")]
    DirectoryCreationFailed(String),
    
    #[error("Atomic write failed: {0}")]
    AtomicWriteFailed(String),
}

/// Configuration for the chunk storage service
#[derive(Debug, Clone)]
pub struct StorageConfig {
    /// Base directory for storing temporary chunks
    pub base_dir: PathBuf,
    /// Maximum size allowed for a single chunk (in bytes)
    pub max_chunk_size: usize,
    /// Maximum number of chunks per transfer
    pub max_chunks_per_transfer: u32,
}

impl Default for StorageConfig {
    fn default() -> Self {
        Self {
            base_dir: PathBuf::from("/tmp/indidus_transfers"),
            max_chunk_size: 10 * 1024 * 1024, // 10MB
            max_chunks_per_transfer: 10000,   // 10k chunks max
        }
    }
}

/// Service for managing temporary storage of file chunks
#[derive(Debug, Clone)]
pub struct ChunkStorageService {
    config: StorageConfig,
}

impl ChunkStorageService {
    /// Create a new chunk storage service with the given configuration
    pub fn new(config: StorageConfig) -> Self {
        Self { config }
    }
    
    /// Create a new chunk storage service with default configuration
    pub fn with_default_config() -> Self {
        Self::new(StorageConfig::default())
    }
    
    /// Get the maximum chunk size configured for this storage service
    pub fn max_chunk_size(&self) -> usize {
        self.config.max_chunk_size
    }
    
    /// Store a chunk of data for the given transfer ID and chunk index
    /// 
    /// This method performs atomic writes to prevent partial files and ensures
    /// that the directory structure exists before writing.
    /// 
    /// # Arguments
    /// * `transfer_id` - Unique identifier for the file transfer
    /// * `chunk_index` - Index of this chunk in the sequence (0-based)
    /// * `data` - The encrypted chunk data to store
    /// 
    /// # Returns
    /// * `Ok(())` if the chunk was stored successfully
    /// * `Err(StorageError)` if an error occurred during storage
    pub async fn store_chunk(
        &self,
        transfer_id: &str,
        chunk_index: u32,
        data: &[u8],
    ) -> Result<(), StorageError> {
        // Validate inputs
        self.validate_transfer_id(transfer_id)?;
        self.validate_chunk_index(chunk_index)?;
        self.validate_chunk_size(data)?;
        
        // Create the transfer directory path
        let transfer_dir = self.get_transfer_dir(transfer_id);
        let chunk_file = self.get_chunk_file_path(transfer_id, chunk_index);
        
        // Check if chunk already exists
        if chunk_file.exists() {
            return Err(StorageError::ChunkAlreadyExists {
                transfer_id: transfer_id.to_string(),
                chunk_index,
            });
        }
        
        // Ensure the transfer directory exists
        self.ensure_transfer_directory(&transfer_dir).await?;
        
        // Perform atomic write using a temporary file
        self.atomic_write(&chunk_file, data).await?;
        
        info!(
            "Stored chunk: transfer_id={}, chunk_index={}, size={} bytes",
            transfer_id,
            chunk_index,
            data.len()
        );
        
        Ok(())
    }
    
    /// Retrieve a stored chunk by transfer ID and chunk index
    /// 
    /// # Arguments
    /// * `transfer_id` - Unique identifier for the file transfer
    /// * `chunk_index` - Index of the chunk to retrieve
    /// 
    /// # Returns
    /// * `Ok(Some(data))` if the chunk exists and was read successfully
    /// * `Ok(None)` if the chunk does not exist
    /// * `Err(StorageError)` if an error occurred during retrieval
    pub async fn retrieve_chunk(
        &self,
        transfer_id: &str,
        chunk_index: u32,
    ) -> Result<Option<Vec<u8>>, StorageError> {
        self.validate_transfer_id(transfer_id)?;
        self.validate_chunk_index(chunk_index)?;
        
        let chunk_file = self.get_chunk_file_path(transfer_id, chunk_index);
        
        if !chunk_file.exists() {
            return Ok(None);
        }
        
        let data = fs::read(&chunk_file).await?;
        
        debug!(
            "Retrieved chunk: transfer_id={}, chunk_index={}, size={} bytes",
            transfer_id,
            chunk_index,
            data.len()
        );
        
        Ok(Some(data))
    }
    
    /// List all chunk indices for a given transfer ID
    /// 
    /// # Arguments
    /// * `transfer_id` - Unique identifier for the file transfer
    /// 
    /// # Returns
    /// * `Ok(indices)` - Vector of chunk indices that exist for this transfer
    /// * `Err(StorageError)` if an error occurred during listing
    pub async fn list_chunks(&self, transfer_id: &str) -> Result<Vec<u32>, StorageError> {
        self.validate_transfer_id(transfer_id)?;
        
        let transfer_dir = self.get_transfer_dir(transfer_id);
        
        if !transfer_dir.exists() {
            return Ok(Vec::new());
        }
        
        let mut entries = fs::read_dir(&transfer_dir).await?;
        let mut indices = Vec::new();
        
        while let Some(entry) = entries.next_entry().await? {
            if let Some(file_name) = entry.file_name().to_str() {
                if let Ok(index) = file_name.parse::<u32>() {
                    indices.push(index);
                }
            }
        }
        
        indices.sort_unstable();
        Ok(indices)
    }
    
    /// Clean up all chunks for a given transfer ID
    /// 
    /// # Arguments
    /// * `transfer_id` - Unique identifier for the file transfer to clean up
    /// 
    /// # Returns
    /// * `Ok(count)` - Number of chunks that were removed
    /// * `Err(StorageError)` if an error occurred during cleanup
    pub async fn cleanup_transfer(&self, transfer_id: &str) -> Result<usize, StorageError> {
        self.validate_transfer_id(transfer_id)?;
        
        let transfer_dir = self.get_transfer_dir(transfer_id);
        
        if !transfer_dir.exists() {
            return Ok(0);
        }
        
        let chunks = self.list_chunks(transfer_id).await?;
        let count = chunks.len();
        
        // Remove the entire transfer directory
        fs::remove_dir_all(&transfer_dir).await?;
        
        info!(
            "Cleaned up transfer: transfer_id={}, removed {} chunks",
            transfer_id, count
        );
        
        Ok(count)
    }
    
    /// Get the total size of all chunks for a transfer
    /// 
    /// # Arguments
    /// * `transfer_id` - Unique identifier for the file transfer
    /// 
    /// # Returns
    /// * `Ok(size)` - Total size in bytes of all chunks for this transfer
    /// * `Err(StorageError)` if an error occurred during calculation
    pub async fn get_transfer_size(&self, transfer_id: &str) -> Result<u64, StorageError> {
        self.validate_transfer_id(transfer_id)?;
        
        let chunks = self.list_chunks(transfer_id).await?;
        let mut total_size = 0u64;
        
        for chunk_index in chunks {
            let chunk_file = self.get_chunk_file_path(transfer_id, chunk_index);
            if let Ok(metadata) = fs::metadata(&chunk_file).await {
                total_size += metadata.len();
            }
        }
        
        Ok(total_size)
    }
    
    // Private helper methods
    
    fn validate_transfer_id(&self, transfer_id: &str) -> Result<(), StorageError> {
        if transfer_id.is_empty() || transfer_id.len() > 255 {
            return Err(StorageError::InvalidTransferId(
                "Transfer ID must be non-empty and less than 256 characters".to_string(),
            ));
        }
        
        // Check for path traversal attempts
        if transfer_id.contains("..") || transfer_id.contains('/') || transfer_id.contains('\\') {
            return Err(StorageError::InvalidTransferId(
                "Transfer ID contains invalid characters".to_string(),
            ));
        }
        
        Ok(())
    }
    
    fn validate_chunk_index(&self, chunk_index: u32) -> Result<(), StorageError> {
        if chunk_index >= self.config.max_chunks_per_transfer {
            return Err(StorageError::InvalidChunkIndex(chunk_index));
        }
        Ok(())
    }
    
    fn validate_chunk_size(&self, data: &[u8]) -> Result<(), StorageError> {
        if data.len() > self.config.max_chunk_size {
            return Err(StorageError::InvalidTransferId(format!(
                "Chunk size {} exceeds maximum allowed size {}",
                data.len(),
                self.config.max_chunk_size
            )));
        }
        Ok(())
    }
    
    fn get_transfer_dir(&self, transfer_id: &str) -> PathBuf {
        self.config.base_dir.join(transfer_id)
    }
    
    fn get_chunk_file_path(&self, transfer_id: &str, chunk_index: u32) -> PathBuf {
        self.get_transfer_dir(transfer_id).join(chunk_index.to_string())
    }
    
    async fn ensure_transfer_directory(&self, transfer_dir: &Path) -> Result<(), StorageError> {
        if !transfer_dir.exists() {
            fs::create_dir_all(transfer_dir).await.map_err(|e| {
                StorageError::DirectoryCreationFailed(format!(
                    "Failed to create directory {}: {}",
                    transfer_dir.display(),
                    e
                ))
            })?;
            
            debug!("Created transfer directory: {}", transfer_dir.display());
        }
        Ok(())
    }
    
    async fn atomic_write(&self, target_path: &Path, data: &[u8]) -> Result<(), StorageError> {
        // Create a temporary file with a unique name
        let temp_path = target_path.with_extension(format!("tmp.{}", Uuid::new_v4()));
        
        // Write to the temporary file
        let mut temp_file = fs::File::create(&temp_path).await.map_err(|e| {
            StorageError::AtomicWriteFailed(format!(
                "Failed to create temporary file {}: {}",
                temp_path.display(),
                e
            ))
        })?;
        
        temp_file.write_all(data).await.map_err(|e| {
            StorageError::AtomicWriteFailed(format!(
                "Failed to write to temporary file {}: {}",
                temp_path.display(),
                e
            ))
        })?;
        
        temp_file.sync_all().await.map_err(|e| {
            StorageError::AtomicWriteFailed(format!(
                "Failed to sync temporary file {}: {}",
                temp_path.display(),
                e
            ))
        })?;
        
        // Atomically move the temporary file to the target location
        fs::rename(&temp_path, target_path).await.map_err(|e| {
            // Clean up the temporary file if the rename failed
            if let Err(cleanup_err) = std::fs::remove_file(&temp_path) {
                warn!(
                    "Failed to clean up temporary file {}: {}",
                    temp_path.display(),
                    cleanup_err
                );
            }
            
            StorageError::AtomicWriteFailed(format!(
                "Failed to rename {} to {}: {}",
                temp_path.display(),
                target_path.display(),
                e
            ))
        })?;
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    async fn create_test_service() -> (ChunkStorageService, TempDir) {
        let temp_dir = TempDir::new().expect("Failed to create temp directory");
        let config = StorageConfig {
            base_dir: temp_dir.path().to_path_buf(),
            max_chunk_size: 1024,
            max_chunks_per_transfer: 100,
        };
        let service = ChunkStorageService::new(config);
        (service, temp_dir)
    }
    
    #[tokio::test]
    async fn test_store_and_retrieve_chunk() {
        let (service, _temp_dir) = create_test_service().await;
        let transfer_id = "test_transfer_123";
        let chunk_index = 0;
        let data = b"Hello, world!";
        
        // Store the chunk
        service
            .store_chunk(transfer_id, chunk_index, data)
            .await
            .expect("Failed to store chunk");
        
        // Retrieve the chunk
        let retrieved = service
            .retrieve_chunk(transfer_id, chunk_index)
            .await
            .expect("Failed to retrieve chunk");
        
        assert_eq!(retrieved, Some(data.to_vec()));
    }
    
    #[tokio::test]
    async fn test_list_chunks() {
        let (service, _temp_dir) = create_test_service().await;
        let transfer_id = "test_transfer_456";
        
        // Store multiple chunks
        for i in [0, 2, 5, 10] {
            service
                .store_chunk(transfer_id, i, b"test data")
                .await
                .expect("Failed to store chunk");
        }
        
        // List chunks
        let chunks = service
            .list_chunks(transfer_id)
            .await
            .expect("Failed to list chunks");
        
        assert_eq!(chunks, vec![0, 2, 5, 10]);
    }
    
    #[tokio::test]
    async fn test_cleanup_transfer() {
        let (service, _temp_dir) = create_test_service().await;
        let transfer_id = "test_transfer_cleanup";
        
        // Store some chunks
        for i in 0..5 {
            service
                .store_chunk(transfer_id, i, b"test data")
                .await
                .expect("Failed to store chunk");
        }
        
        // Verify chunks exist
        let chunks = service
            .list_chunks(transfer_id)
            .await
            .expect("Failed to list chunks");
        assert_eq!(chunks.len(), 5);
        
        // Clean up
        let removed_count = service
            .cleanup_transfer(transfer_id)
            .await
            .expect("Failed to cleanup transfer");
        assert_eq!(removed_count, 5);
        
        // Verify chunks are gone
        let chunks_after = service
            .list_chunks(transfer_id)
            .await
            .expect("Failed to list chunks after cleanup");
        assert_eq!(chunks_after.len(), 0);
    }
    
    #[tokio::test]
    async fn test_invalid_transfer_id() {
        let (service, _temp_dir) = create_test_service().await;
        
        // Test path traversal attempt
        let result = service.store_chunk("../evil", 0, b"data").await;
        assert!(matches!(result, Err(StorageError::InvalidTransferId(_))));
        
        // Test empty transfer ID
        let result = service.store_chunk("", 0, b"data").await;
        assert!(matches!(result, Err(StorageError::InvalidTransferId(_))));
    }
    
    #[tokio::test]
    async fn test_chunk_already_exists() {
        let (service, _temp_dir) = create_test_service().await;
        let transfer_id = "test_duplicate";
        let chunk_index = 0;
        
        // Store chunk first time
        service
            .store_chunk(transfer_id, chunk_index, b"first")
            .await
            .expect("Failed to store chunk first time");
        
        // Try to store again
        let result = service
            .store_chunk(transfer_id, chunk_index, b"second")
            .await;
        
        assert!(matches!(result, Err(StorageError::ChunkAlreadyExists { .. })));
    }
}