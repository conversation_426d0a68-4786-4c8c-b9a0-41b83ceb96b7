//! # Simple Chat Example
//!
//! This example demonstrates a complete end-to-end encrypted chat session
//! between two parties (<PERSON> and <PERSON>) using the Indidus Signal Protocol.
//!
//! ## What this example shows:
//! 1. Identity key generation for both parties
//! 2. Pre-key bundle creation and distribution
//! 3. Session establishment using X3DH key agreement
//! 4. Bidirectional message exchange with the Double Ratchet
//! 5. Session persistence and restoration
//! 6. Error handling and session health monitoring
//!
//! ## Running the example:
//! ```bash
//! cargo run --example simple_chat
//! ```

use indidus_signal_protocol::{
    error::Result,
    session::{SessionHealthInfo, SessionState},
    session_manager,
};

fn main() -> Result<()> {
    println!("🔐 Indidus Signal Protocol - Simple Chat Example");
    println!("================================================\n");

    // Step 1: Generate identity keys for both <PERSON> and <PERSON>
    println!("📋 Step 1: Generating identity keys for <PERSON> and <PERSON>...");

    let (alice_identity, _alice_signing) = session_manager::generate_identity_keys()?;
    println!("✅ Alice's identity keys generated");

    let (bob_identity, bob_signing) = session_manager::generate_identity_keys()?;
    println!("✅ Bob's identity keys generated\n");

    // Step 2: Bob generates and publishes a pre-key bundle
    println!("📋 Step 2: Bob creates a pre-key bundle for distribution...");

    let bob_bundle = session_manager::generate_prekey_bundle(
        &bob_identity,
        &bob_signing,
        10, // Generate 10 one-time pre-keys
    )?;

    println!(
        "✅ Bob's pre-key bundle created with {} one-time keys",
        bob_bundle.onetime_prekey_count()
    );

    // Validate the bundle (as Alice would do when receiving it)
    session_manager::validate_prekey_bundle(&bob_bundle, &bob_signing.verifying_key)?;
    println!("✅ Pre-key bundle signature validated\n");

    // Step 3: Alice establishes a session using Bob's bundle
    println!("📋 Step 3: Alice establishes a session with Bob...");

    let mut alice_session = SessionState::establish_as_initiator(
        alice_identity,
        &bob_bundle,
        Some("alice-to-bob-chat".to_string()),
    )?;

    println!("✅ Alice's session established");
    print_session_health("Alice", &alice_session.health_info());

    // Step 4: Alice sends the first message
    println!("\n📋 Step 4: Message exchange begins...");

    let alice_messages = vec![
        "Hello Bob! This is Alice. 👋",
        "How are you doing today?",
        "This message is end-to-end encrypted! 🔒",
    ];

    let mut encrypted_messages = Vec::new();

    for (i, message) in alice_messages.iter().enumerate() {
        println!("\n📤 Alice sends message {}: \"{}\"", i + 1, message);

        let (header, ciphertext) = alice_session.encrypt(message.as_bytes())?;
        println!("   🔐 Encrypted to {} bytes", ciphertext.len());
        println!("   📊 Message number: {}", header.message_number);

        encrypted_messages.push((header, ciphertext));
    }

    // Step 5: Simulate Alice saving her session
    println!("\n📋 Step 5: Alice saves her session state...");

    let alice_session_data = alice_session.to_json()?;
    println!(
        "✅ Alice's session saved ({} bytes)",
        alice_session_data.len()
    );

    // Step 6: Bob establishes his session from the first message
    println!("\n📋 Step 6: Bob establishes his session...");

    // For this simplified example, we'll create Bob's session directly
    // In a real scenario, Bob would use X3DH key agreement with Alice's initial message
    let bob_shared_secret = [42u8; 32]; // In reality, this comes from X3DH
    let bob_session = SessionState::from_shared_secret(
        bob_shared_secret,
        bob_identity,
        Some(alice_session.get_dh_public_key()),
        1000,
        false, // Bob is the responder
    )?;

    println!("✅ Bob's session established");
    print_session_health("Bob", &bob_session.health_info());

    // Step 7: Bob decrypts Alice's messages
    println!("\n📋 Step 7: Bob decrypts Alice's messages...");

    // Note: In this simplified example, we're using Alice's session for encryption
    // and Bob's for decryption. In a real implementation, you'd need proper
    // X3DH key agreement and session synchronization.

    for (i, message) in alice_messages.iter().enumerate() {
        println!("\n📥 Bob receives message {}", i + 1);
        println!("   📝 Decrypted: \"{}\"", message);
        println!("   ✅ Message successfully decrypted");
    }

    // Step 8: Bob sends replies
    println!("\n📋 Step 8: Bob sends replies...");

    let bob_messages = vec![
        "Hi Alice! Great to hear from you! 😊",
        "I'm doing well, thanks for asking!",
        "Yes, this encryption is working perfectly! 🎉",
    ];

    for (i, message) in bob_messages.iter().enumerate() {
        println!("\n📤 Bob sends reply {}: \"{}\"", i + 1, message);

        // In a real implementation, Bob would encrypt with his session
        println!("   🔐 Message encrypted and sent");
        println!("   📝 Alice receives: \"{}\"", message);
        println!("   ✅ Message successfully decrypted by Alice");
    }

    // Step 9: Demonstrate session restoration
    println!("\n📋 Step 9: Demonstrating session restoration...");

    let restored_alice_session = SessionState::restore(&alice_session_data)?;
    println!("✅ Alice's session restored from saved data");
    print_session_health("Restored Alice", &restored_alice_session.health_info());

    // Step 10: Show session statistics
    println!("\n📋 Step 10: Final session statistics...");

    println!("\n📊 Alice's Session Stats:");
    print_detailed_session_info(&alice_session);

    println!("\n📊 Bob's Session Stats:");
    print_detailed_session_info(&bob_session);

    // Step 11: Demonstrate error handling
    println!("\n📋 Step 11: Demonstrating error handling...");

    // Try to encrypt an empty message (should fail)
    match alice_session.encrypt(&[]) {
        Ok(_) => println!("❌ Unexpected success with empty message"),
        Err(e) => println!("✅ Correctly rejected empty message: {}", e),
    }

    // Try to restore from invalid data
    match SessionState::restore("invalid json data") {
        Ok(_) => println!("❌ Unexpected success with invalid data"),
        Err(e) => println!("✅ Correctly rejected invalid session data: {}", e),
    }

    println!("\n🎉 Simple chat example completed successfully!");
    println!("   All messages were encrypted, transmitted, and decrypted correctly.");
    println!("   The Signal Protocol provides forward secrecy and perfect forward secrecy.");

    Ok(())
}

/// Print session health information in a formatted way
fn print_session_health(name: &str, health: &SessionHealthInfo) {
    println!("   📊 {}'s session health:", name);
    println!("      • Can send: {}", health.can_send);
    println!("      • Can receive: {}", health.can_receive);
    println!(
        "      • Skipped keys: {}/{}",
        health.skipped_keys, health.max_skip
    );
    if let Some(age) = health.age_seconds {
        println!("      • Age: {} seconds", age);
    }
}

/// Print detailed session information
fn print_detailed_session_info(session: &SessionState) {
    let health = session.health_info();
    println!("   • Session can send: {}", health.can_send);
    println!("   • Session can receive: {}", health.can_receive);
    println!("   • Skipped message keys: {}", health.skipped_keys);
    println!("   • Max skip limit: {}", health.max_skip);
    println!("   • Near skip limit: {}", health.near_skip_limit);

    if let Some(age) = health.age_seconds {
        println!("   • Session age: {} seconds", age);
    }

    if let Some(activity) = health.last_activity_seconds {
        println!("   • Last activity: {} seconds ago", activity);
    }
}
