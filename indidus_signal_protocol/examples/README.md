# Examples

This directory contains practical examples demonstrating how to use the Indidus Signal Protocol crate.

## Available Examples

### `simple_chat.rs`

A comprehensive example showing end-to-end encrypted communication between two parties (<PERSON> and <PERSON>).

**What it demonstrates:**
- Identity key generation for both parties
- Pre-key bundle creation and validation
- Session establishment using the stabilized public API
- Message encryption and decryption
- Session persistence and restoration
- Error handling patterns
- Session health monitoring

**Run the example:**
```bash
cargo run --example simple_chat
```

**Key learning points:**
1. **Setup Phase**: How to generate identity keys and pre-key bundles
2. **Session Establishment**: Using the high-level API to establish secure sessions
3. **Message Exchange**: Encrypting and decrypting messages with forward secrecy
4. **Persistence**: Saving and restoring session state
5. **Monitoring**: Checking session health and handling errors

This example serves as both a tutorial and a reference implementation for integrating the Signal Protocol into messaging applications.

## Running Examples

All examples can be run using:
```bash
cargo run --example <example_name>
```

For example:
```bash
cargo run --example simple_chat
```

## Example Output

The examples include detailed console output showing each step of the protocol execution, making it easy to understand what's happening at each stage of the cryptographic operations.