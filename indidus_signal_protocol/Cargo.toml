[package]
name = "indidus_signal_protocol"
version = "0.1.0"
edition = "2021"

[dependencies]
curve25519-dalek = "4.1.2"
sha2 = "0.10.8"
hmac = "0.12.1"
hkdf = "0.12.3"
aes-gcm = "0.10.3"
rand_core = "0.6.4"
serde = { version = "1.0.197", features = ["derive"] }
serde_json = "1.0.115"
base64 = "0.21.7"
thiserror = "1.0.58"
ed25519-dalek = "2.1.1"
zeroize = { version = "1.8.1", features = ["derive"] }
