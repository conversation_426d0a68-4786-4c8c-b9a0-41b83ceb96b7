use super::*;
use crate::crypto::{bundle::PreKeyBundle, keys::KeyPair};

#[test]
fn test_session_state_creation() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let session_state = state::SessionState::new(root_key, dh_key.clone(), Some(remote_key), 1000);

    assert_eq!(session_state.root_key, root_key);
    assert_eq!(session_state.get_dh_public_key(), dh_key.public_key());
    assert_eq!(session_state.remote_dh_public_key, Some(remote_key));
    assert_eq!(session_state.sending_message_number, 0);
    assert_eq!(session_state.receiving_message_number, 0);
    assert_eq!(session_state.previous_chain_length, 0);
    assert_eq!(session_state.max_skip, 1000);
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());
    assert_eq!(session_state.skipped_keys_count(), 0);
    assert!(!session_state.is_at_max_skip());
    assert_eq!(session_state.remote_identity_key, None);
    assert_eq!(session_state.local_identity_key, None);
    assert_eq!(session_state.created_at, None);
    assert_eq!(session_state.last_activity, None);
    assert_eq!(session_state.session_id, None);
}

#[test]
fn test_session_state_creation_with_metadata() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();
    let remote_identity = KeyPair::generate()
        .expect("Failed to generate remote identity")
        .public_key();
    let local_identity = KeyPair::generate()
        .expect("Failed to generate local identity")
        .public_key();
    let session_id = "test-session-123".to_string();

    let session_state = state::SessionState::new_with_metadata(
        root_key,
        dh_key,
        Some(remote_key),
        500,
        Some(remote_identity),
        Some(local_identity),
        Some(session_id.clone()),
    );

    assert_eq!(session_state.root_key, root_key);
    assert_eq!(session_state.max_skip, 500);
    assert_eq!(session_state.remote_identity_key, Some(remote_identity));
    assert_eq!(session_state.local_identity_key, Some(local_identity));
    assert_eq!(session_state.session_id, Some(session_id));
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());
}

#[test]
fn test_session_state_activity_update() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let mut session_state = state::SessionState::new(root_key, dh_key, None, 1000);

    // Initially no activity timestamp
    assert_eq!(session_state.last_activity, None);

    // Update activity
    session_state.update_activity();
    assert!(session_state.last_activity.is_some());

    let first_activity = session_state.last_activity.unwrap();

    // Wait a bit and update again
    std::thread::sleep(std::time::Duration::from_millis(10));
    session_state.update_activity();

    let second_activity = session_state.last_activity.unwrap();
    assert!(second_activity >= first_activity);
}

#[test]
fn test_session_state_chain_capabilities() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let mut session_state = state::SessionState::new(root_key, dh_key, None, 1000);

    // Initially can't send or receive
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());

    // Set sending chain key
    session_state.sending_chain_key = Some([1u8; 32]);
    assert!(session_state.can_send());
    assert!(!session_state.can_receive());

    // Set receiving chain key
    session_state.receiving_chain_key = Some([2u8; 32]);
    assert!(session_state.can_send());
    assert!(session_state.can_receive());

    // Remove sending chain key
    session_state.sending_chain_key = None;
    assert!(!session_state.can_send());
    assert!(session_state.can_receive());
}

#[test]
fn test_session_state_skipped_keys_management() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let mut session_state = state::SessionState::new(root_key, dh_key, None, 2);

    // Initially no skipped keys
    assert_eq!(session_state.skipped_keys_count(), 0);
    assert!(!session_state.is_at_max_skip());

    // Add some skipped keys
    let key1 = (vec![1, 2, 3], 1);
    let key2 = (vec![4, 5, 6], 2);

    session_state.skipped_message_keys.insert(key1, [1u8; 32]);
    assert_eq!(session_state.skipped_keys_count(), 1);
    assert!(!session_state.is_at_max_skip());

    session_state.skipped_message_keys.insert(key2, [2u8; 32]);
    assert_eq!(session_state.skipped_keys_count(), 2);
    assert!(session_state.is_at_max_skip());

    // Clear all keys
    session_state.clear_skipped_keys();
    assert_eq!(session_state.skipped_keys_count(), 0);
    assert!(!session_state.is_at_max_skip());
}

#[test]
fn test_session_state_timing_functions() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    // Session without timestamps
    let session_state = state::SessionState::new(root_key, dh_key.clone(), None, 1000);
    assert_eq!(session_state.session_age_seconds(), None);
    assert_eq!(session_state.time_since_last_activity_seconds(), None);

    // Session with timestamps
    let session_state_with_meta = state::SessionState::new_with_metadata(
        root_key,
        dh_key.clone(),
        None,
        1000,
        None,
        None,
        None,
    );

    // Should have some age (very small since just created)
    let age = session_state_with_meta.session_age_seconds();
    assert!(age.is_some());
    assert!(age.unwrap() < 10); // Should be less than 10 seconds

    let last_activity = session_state_with_meta.time_since_last_activity_seconds();
    assert!(last_activity.is_some());
    assert!(last_activity.unwrap() < 10); // Should be less than 10 seconds
}

#[test]
fn test_session_state_dh_key_access() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let session_state = state::SessionState::new(root_key, dh_key.clone(), None, 1000);

    assert_eq!(session_state.get_dh_public_key(), dh_key.public_key());
    assert_eq!(
        session_state.dh_ratchet_key.private_key(),
        dh_key.private_key()
    );
}

#[test]
fn test_session_state_with_no_remote_key() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let session_state = state::SessionState::new(root_key, dh_key, None, 1000);

    assert_eq!(session_state.remote_dh_public_key, None);
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());
}

#[test]
fn test_session_state_new_initiator() {
    let x3dh_secret = [42u8; 32];
    let remote_identity = KeyPair::generate().expect("Failed to generate remote identity");
    let remote_signed_prekey =
        KeyPair::generate().expect("Failed to generate remote signed prekey");

    // Create a mock pre-key bundle
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32], // Mock verifying key
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64], // Mock signature
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let session_id = "test-session-initiator".to_string();
    let session_state = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000,
        Some(session_id.clone()),
    )
    .expect("Failed to create initiator session");

    // Initiator should have a sending chain but no receiving chain initially
    assert!(session_state.can_send());
    assert!(!session_state.can_receive());

    // Should have remote DH key set
    assert_eq!(
        session_state.remote_dh_public_key,
        Some(remote_bundle.signed_prekey())
    );

    // Should have remote identity key set
    assert_eq!(
        session_state.remote_identity_key,
        Some(remote_bundle.identity_key())
    );

    // Message numbers should be initialized to 0
    assert_eq!(session_state.sending_message_number, 0);
    assert_eq!(session_state.receiving_message_number, 0);
    assert_eq!(session_state.previous_chain_length, 0);

    // Root key should be derived (different from X3DH secret)
    assert_ne!(session_state.root_key, x3dh_secret);

    // Should have max_skip set correctly
    assert_eq!(session_state.max_skip, 1000);

    // Should have no skipped keys initially
    assert_eq!(session_state.skipped_keys_count(), 0);

    // Should have timestamps set
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());

    // Should have session ID set
    assert_eq!(session_state.session_id, Some(session_id));
}

#[test]
fn test_session_state_new_responder() {
    let x3dh_secret = [84u8; 32];
    let our_signed_prekey = KeyPair::generate().expect("Failed to generate our signed prekey");
    let remote_identity = KeyPair::generate()
        .expect("Failed to generate remote identity")
        .public_key();
    let session_id = "test-session-responder".to_string();

    let session_state = state::SessionState::new_responder(
        x3dh_secret,
        our_signed_prekey.clone(),
        remote_identity,
        500,
        Some(session_id.clone()),
    )
    .expect("Failed to create responder session");

    // Responder should not be able to send or receive initially
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());

    // Should not have remote DH key set initially
    assert_eq!(session_state.remote_dh_public_key, None);

    // Should have our DH key set
    assert_eq!(
        session_state.dh_ratchet_key.public_key(),
        our_signed_prekey.public_key()
    );
    assert_eq!(
        session_state.dh_ratchet_key.private_key().as_bytes(),
        our_signed_prekey.private_key().as_bytes()
    );

    // Should have remote identity key set
    assert_eq!(session_state.remote_identity_key, Some(remote_identity));

    // Message numbers should be initialized to 0
    assert_eq!(session_state.sending_message_number, 0);
    assert_eq!(session_state.receiving_message_number, 0);
    assert_eq!(session_state.previous_chain_length, 0);

    // Root key should be the X3DH secret
    assert_eq!(session_state.root_key, x3dh_secret);

    // Should have max_skip set correctly
    assert_eq!(session_state.max_skip, 500);

    // Should have no skipped keys initially
    assert_eq!(session_state.skipped_keys_count(), 0);

    // Should have timestamps set
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());

    // Should have session ID set
    assert_eq!(session_state.session_id, Some(session_id));
}

#[test]
fn test_session_state_from_shared_secret_initiator() {
    let shared_secret = [123u8; 32];
    let our_dh_key = KeyPair::generate().expect("Failed to generate our DH key");
    let remote_dh_key = KeyPair::generate()
        .expect("Failed to generate remote DH key")
        .public_key();

    let session_state = state::SessionState::from_shared_secret(
        shared_secret,
        our_dh_key,
        Some(remote_dh_key),
        1000,
        true, // is_initiator
    )
    .expect("Failed to create session from shared secret");

    // Initiator with remote DH key should be able to send
    assert!(session_state.can_send());
    assert!(!session_state.can_receive());

    // Should have remote DH key set
    assert_eq!(session_state.remote_dh_public_key, Some(remote_dh_key));

    // Root key should be derived (different from shared secret)
    assert_ne!(session_state.root_key, shared_secret);

    // Should have timestamps set
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());
}

#[test]
fn test_session_state_from_shared_secret_responder() {
    let shared_secret = [200u8; 32];
    let our_dh_key = KeyPair::generate().expect("Failed to generate our DH key");

    let session_state = state::SessionState::from_shared_secret(
        shared_secret,
        our_dh_key,
        None,
        1000,
        false, // is_initiator
    )
    .expect("Failed to create session from shared secret");

    // Responder without remote DH key should not be able to send or receive
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());

    // Should not have remote DH key set
    assert_eq!(session_state.remote_dh_public_key, None);

    // Root key should be the shared secret (no derivation for responder)
    assert_eq!(session_state.root_key, shared_secret);

    // Should have timestamps set
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());
}

#[test]
fn test_session_state_restore_and_activate() {
    let root_key = [99u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let original_session = state::SessionState::new_with_metadata(
        root_key,
        dh_key,
        None,
        750,
        None,
        None,
        Some("restore-test".to_string()),
    );

    // Serialize the session
    let serialized = serde_json::to_string(&original_session).expect("Failed to serialize session");

    // Wait a bit to ensure timestamp difference
    std::thread::sleep(std::time::Duration::from_millis(100));

    // Restore and activate
    let restored_session =
        state::SessionState::restore_and_activate(&serialized).expect("Failed to restore session");

    // Should have same core properties
    assert_eq!(restored_session.root_key, original_session.root_key);
    assert_eq!(restored_session.max_skip, original_session.max_skip);
    assert_eq!(restored_session.session_id, original_session.session_id);

    // Activity timestamp should be updated (or at least equal due to timing precision)
    assert!(restored_session.last_activity.unwrap() >= original_session.last_activity.unwrap());
}

#[test]
fn test_session_state_initialization_deterministic() {
    let x3dh_secret = [111u8; 32];
    let remote_identity = KeyPair::generate().expect("Failed to generate remote identity");
    let remote_signed_prekey =
        KeyPair::generate().expect("Failed to generate remote signed prekey");

    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32], // Mock verifying key
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    // Same inputs should produce sessions with different DH keys (randomly generated)
    // but same other properties
    let session1 = state::SessionState::new_initiator(x3dh_secret, &remote_bundle, 1000, None)
        .expect("Failed to create session 1");
    let session2 = state::SessionState::new_initiator(x3dh_secret, &remote_bundle, 1000, None)
        .expect("Failed to create session 2");

    // DH ratchet keys should be different (randomly generated)
    assert_ne!(
        session1.dh_ratchet_key.private_key().as_bytes(),
        session2.dh_ratchet_key.private_key().as_bytes()
    );
    assert_ne!(
        session1.dh_ratchet_key.public_key().as_bytes(),
        session2.dh_ratchet_key.public_key().as_bytes()
    );

    // But other properties should be the same
    assert_eq!(session1.remote_dh_public_key, session2.remote_dh_public_key);
    assert_eq!(session1.remote_identity_key, session2.remote_identity_key);
    assert_eq!(session1.max_skip, session2.max_skip);
    assert_eq!(
        session1.sending_message_number,
        session2.sending_message_number
    );
    assert_eq!(
        session1.receiving_message_number,
        session2.receiving_message_number
    );
}

#[test]
fn test_session_state_initialization_different_secrets() {
    let x3dh_secret1 = [111u8; 32];
    let x3dh_secret2 = [222u8; 32];
    let remote_identity = KeyPair::generate().expect("Failed to generate remote identity");
    let remote_signed_prekey =
        KeyPair::generate().expect("Failed to generate remote signed prekey");

    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32], // Mock verifying key
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let session1 = state::SessionState::new_initiator(x3dh_secret1, &remote_bundle, 1000, None)
        .expect("Failed to create session 1");
    let session2 = state::SessionState::new_initiator(x3dh_secret2, &remote_bundle, 1000, None)
        .expect("Failed to create session 2");

    // Different X3DH secrets should produce different root keys and sending chain keys
    assert_ne!(session1.root_key, session2.root_key);
    assert_ne!(session1.sending_chain_key, session2.sending_chain_key);
}

#[test]
fn test_session_state_responder_initialization() {
    let x3dh_secret = [150u8; 32];
    let our_signed_prekey = KeyPair::generate().expect("Failed to generate our signed prekey");
    let remote_identity1 = KeyPair::generate()
        .expect("Failed to generate remote identity 1")
        .public_key();
    let remote_identity2 = KeyPair::generate()
        .expect("Failed to generate remote identity 2")
        .public_key();

    let session1 = state::SessionState::new_responder(
        x3dh_secret,
        our_signed_prekey.clone(),
        remote_identity1,
        1000,
        None,
    )
    .expect("Failed to create responder session 1");
    let session2 = state::SessionState::new_responder(
        x3dh_secret,
        our_signed_prekey.clone(),
        remote_identity2,
        1000,
        None,
    )
    .expect("Failed to create responder session 2");

    // Same X3DH secret and our key should produce same root key
    assert_eq!(session1.root_key, session2.root_key);
    assert_eq!(
        session1.dh_ratchet_key.public_key(),
        session2.dh_ratchet_key.public_key()
    );

    // But different remote identity keys
    assert_ne!(session1.remote_identity_key, session2.remote_identity_key);
    assert_eq!(session1.remote_identity_key, Some(remote_identity1));
    assert_eq!(session2.remote_identity_key, Some(remote_identity2));
}

#[test]
fn test_session_state_restore_invalid_json() {
    let invalid_json = "{ invalid json }";
    let result = state::SessionState::restore_and_activate(invalid_json);
    assert!(result.is_err());
}

#[test]
fn test_session_state_serialization_json() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let mut session_state = state::SessionState::new_with_metadata(
        root_key,
        dh_key.clone(),
        Some(remote_key),
        500,
        Some(remote_key),          // Use as identity key for testing
        Some(dh_key.public_key()), // Use as local identity key for testing
        Some("test-session".to_string()),
    );

    // Add some state to make serialization more interesting
    session_state.sending_chain_key = Some([1u8; 32]);
    session_state.receiving_chain_key = Some([2u8; 32]);
    session_state.sending_message_number = 10;
    session_state.receiving_message_number = 20;
    session_state.previous_chain_length = 5;

    let serialized = session_state
        .to_json()
        .expect("Failed to serialize to JSON");
    let deserialized = state::SessionState::from_json(&serialized).expect(
        "Failed to deserialize from JSON
",
    );

    assert_eq!(session_state.root_key, deserialized.root_key);
    assert_eq!(
        session_state.dh_ratchet_key.public_key(),
        deserialized.dh_ratchet_key.public_key()
    );
    assert_eq!(
        session_state.remote_dh_public_key,
        deserialized.remote_dh_public_key
    );
    assert_eq!(
        session_state.sending_chain_key,
        deserialized.sending_chain_key
    );
    assert_eq!(
        session_state.receiving_chain_key,
        deserialized.receiving_chain_key
    );
    assert_eq!(
        session_state.sending_message_number,
        deserialized.sending_message_number
    );
    assert_eq!(
        session_state.receiving_message_number,
        deserialized.receiving_message_number
    );
    assert_eq!(
        session_state.previous_chain_length,
        deserialized.previous_chain_length
    );
    assert_eq!(session_state.max_skip, deserialized.max_skip);
    assert_eq!(
        session_state.remote_identity_key,
        deserialized.remote_identity_key
    );
    assert_eq!(
        session_state.local_identity_key,
        deserialized.local_identity_key
    );
    assert_eq!(session_state.session_id, deserialized.session_id);
}

#[test]
fn test_session_state_serialization_bytes() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let mut session_state = state::SessionState::new_with_metadata(
        root_key,
        dh_key.clone(),
        Some(remote_key),
        500,
        Some(remote_key),          // Use as identity key for testing
        Some(dh_key.public_key()), // Use as local identity key for testing
        Some("test-session-bytes".to_string()),
    );

    session_state.sending_chain_key = Some([10u8; 32]);
    session_state.receiving_chain_key = Some([20u8; 32]);

    let bytes = session_state
        .to_bytes()
        .expect("Failed to serialize to bytes");
    let deserialized =
        state::SessionState::from_bytes(&bytes).expect("Failed to deserialize from bytes");

    assert_eq!(session_state.root_key, deserialized.root_key);
    assert_eq!(
        session_state.dh_ratchet_key.public_key(),
        deserialized.dh_ratchet_key.public_key()
    );
    assert_eq!(session_state.session_id, deserialized.session_id);
}

#[test]
fn test_session_state_serialization_minimal() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let mut session_state = state::SessionState::new_with_metadata(
        root_key,
        dh_key.clone(),
        Some(remote_key),
        500,
        Some(remote_key),
        Some(dh_key.public_key()),
        Some("test-session-minimal".to_string()),
    );

    session_state.sending_chain_key = Some([1u8; 32]);

    let minimal_session = session_state.to_minimal();

    // Core properties should be preserved
    assert_eq!(minimal_session.root_key, session_state.root_key);
    assert_eq!(
        minimal_session.dh_ratchet_key.public_key(),
        session_state.dh_ratchet_key.public_key()
    );
    assert_eq!(
        minimal_session.sending_chain_key,
        session_state.sending_chain_key
    );

    // Metadata should be cleared
    assert!(minimal_session.created_at.is_none());
    assert!(minimal_session.last_activity.is_none());
    assert!(minimal_session.session_id.is_none());
}
