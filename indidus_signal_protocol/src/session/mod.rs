pub mod health;
pub mod protocol_state;
pub mod skipped_keys;
pub mod state;

#[cfg(test)]
mod tests;

// Re-export the key structs and types to maintain the public API
pub use health::SessionHealthInfo;
pub use protocol_state::ProtocolState;
pub use skipped_keys::SkippedMessageKeys;
pub use state::SessionState;

/// The unified Session struct - this is the authoritative session type
/// that replaces the old protocol::Session and session::SessionState structs
pub type Session = SessionState;
