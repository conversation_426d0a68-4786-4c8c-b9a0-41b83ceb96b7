
use std::collections::HashMap;

/// Type alias for skipped message keys storage
/// Maps (header public key bytes, message number) to the message key
pub type SkippedMessageKeys = HashMap<(Vec<u8>, u32), [u8; 32]>;

// Note: We can't implement Zeroize directly for HashMap due to orphan rules
// Instead, we'll implement custom zeroization in the containing structs

/// Custom serde module for skipped message keys
pub mod skipped_keys_serde {
    use super::SkippedMessageKeys;
    use serde::{Deserialize, Deserializer, Serializer};
    use serde::ser::SerializeMap;
    use std::collections::HashMap;

    pub fn serialize<S>(keys: &SkippedMessageKeys, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // Convert to a string-keyed map for JSON compatibility
        let mut map = serializer.serialize_map(Some(keys.len()))?;
        for ((key_bytes, msg_num), value) in keys {
            // Create a string key by base64 encoding the bytes and appending the message number
            let key_b64 = base64::Engine::encode(&base64::engine::general_purpose::STANDARD, key_bytes);
            let key_string = format!("{}:{}", key_b64, msg_num);
            let value_b64 = base64::Engine::encode(&base64::engine::general_purpose::STANDARD, value);
            map.serialize_entry(&key_string, &value_b64)?;
        }
        map.end()
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<SkippedMessageKeys, D::Error>
    where
        D: Deserializer<'de>,
    {
        let string_map: HashMap<String, String> = HashMap::deserialize(deserializer)?;
        let mut result = HashMap::new();

        for (key_string, value_b64) in string_map {
            // Parse the key string: "base64_encoded_bytes:message_number"
            let parts: Vec<&str> = key_string.split(':').collect();
            if parts.len() != 2 {
                return Err(serde::de::Error::custom("Invalid key format"));
            }

            let key_bytes = base64::Engine::decode(&base64::engine::general_purpose::STANDARD, parts[0])
                .map_err(|_| serde::de::Error::custom("Invalid base64 in key"))?;
            let msg_num: u32 = parts[1]
                .parse()
                .map_err(|_| serde::de::Error::custom("Invalid message number"))?;

            let value_bytes = base64::Engine::decode(&base64::engine::general_purpose::STANDARD, &value_b64)
                .map_err(|_| serde::de::Error::custom("Invalid base64 in value"))?;

            if value_bytes.len() != 32 {
                return Err(serde::de::Error::custom("Invalid value length"));
            }

            let mut value_array = [0u8; 32];
            value_array.copy_from_slice(&value_bytes);

            result.insert((key_bytes, msg_num), value_array);
        }

        Ok(result)
    }
}
