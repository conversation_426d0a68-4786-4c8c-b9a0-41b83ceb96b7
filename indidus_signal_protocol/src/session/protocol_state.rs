use crate::crypto::keys::{<PERSON><PERSON><PERSON>, <PERSON>Key};
use crate::error::{ProtocolError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use super::skipped_keys::{SkippedMessageKeys, skipped_keys_serde};
use super::state::SessionState;
use zeroize::{Zeroize, ZeroizeOnDrop};


/// A comprehensive data structure that encapsulates all cryptographic state necessary to resume a session
///
/// This struct serves as the single source of truth for serialization and contains all persistent
/// data required for the Double Ratchet protocol, including root keys, chain keys, message counters,
/// identity keys, and pre-key information that is part of the session context.
///
/// The `ProtocolState` is designed to be the primary serialization target for external state
/// management, providing a clean separation between the cryptographic state and the operational
/// session management logic.
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProtocolState {
    /// Root Key (RK) - used to derive new chain keys when the DH ratchet advances
    /// This is the master key from which all other keys in the session are derived
    pub root_key: [u8; 32],

    /// Our Diffie-<PERSON>man ratchet key pair (DHR)
    /// This is our current DH key pair used for the DH ratchet mechanism
    pub dh_ratchet_key: KeyPair,

    /// Remote party's DH ratchet public key (DHP)
    /// This is the most recent DH public key received from the remote party
    pub remote_dh_public_key: Option<PublicKey>,

    /// Sending Chain Key (CKs) - used to derive message keys for outgoing messages
    /// None if we don't have a sending chain established yet
    pub sending_chain_key: Option<[u8; 32]>,

    /// Receiving Chain Key (CKr) - used to derive message keys for incoming messages
    /// None if we don't have a receiving chain established yet
    pub receiving_chain_key: Option<[u8; 32]>,

    /// Number of messages sent in the current sending chain (Ns)
    /// This counter increments with each message we send
    pub sending_message_number: u32,

    /// Number of messages received in the current receiving chain (Nr)
    /// This counter increments with each message we receive in order
    pub receiving_message_number: u32,

    /// Previous chain length (PN) - number of messages in the previous sending chain
    /// This is used when advancing the DH ratchet to inform the recipient
    pub previous_chain_length: u32,

    /// Storage for skipped message keys
    /// Maps (header public key bytes, message number) to the message key
    /// This allows for out-of-order message decryption
    #[serde(with = "skipped_keys_serde")]
    pub skipped_message_keys: SkippedMessageKeys,

    /// Maximum number of skipped message keys to store
    /// This prevents unbounded memory growth from skipped messages
    pub max_skip: u32,

    /// Session metadata: Remote party's identity key
    /// This is used to verify the identity of the remote party
    pub remote_identity_key: Option<PublicKey>,

    /// Session metadata: Our identity key
    /// This is our long-term identity key used in the session
    pub local_identity_key: Option<PublicKey>,

    /// Pre-key information: The signed pre-key used in session establishment
    /// This may be needed for certain protocol operations or verification
    pub signed_prekey: Option<PublicKey>,

    /// Pre-key information: The one-time pre-key used in session establishment (if any)
    /// This is stored for potential protocol verification or debugging purposes
    pub onetime_prekey: Option<PublicKey>,

    /// Session creation timestamp (Unix timestamp)
    /// When this session was originally established
    pub created_at: Option<u64>,

    /// Last activity timestamp (Unix timestamp)
    /// When this session was last used for encryption or decryption
    pub last_activity: Option<u64>,

    /// Session identifier for tracking and management
    /// A unique identifier for this session (optional)
    pub session_id: Option<String>,
}

impl ProtocolState {
    /// Create a new ProtocolState with the provided parameters
    ///
    /// This is the primary constructor for creating a new protocol state instance.
    /// All cryptographic material and metadata should be provided through this constructor.
    ///
    /// # Arguments
    /// * `root_key` - The root key derived from X3DH key agreement
    /// * `dh_ratchet_key` - Our DH ratchet key pair
    /// * `remote_dh_public_key` - The remote party's DH public key (if available)
    /// * `max_skip` - Maximum number of skipped message keys to store
    ///
    /// # Returns
    /// A new ProtocolState instance with initialized values
    pub fn new(
        root_key: [u8; 32],
        dh_ratchet_key: KeyPair,
        remote_dh_public_key: Option<PublicKey>,
        max_skip: u32,
    ) -> Self {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();

        Self {
            root_key,
            dh_ratchet_key,
            remote_dh_public_key,
            sending_chain_key: None,
            receiving_chain_key: None,
            sending_message_number: 0,
            receiving_message_number: 0,
            previous_chain_length: 0,
            skipped_message_keys: HashMap::new(),
            max_skip,
            remote_identity_key: None,
            local_identity_key: None,
            signed_prekey: None,
            onetime_prekey: None,
            created_at: now,
            last_activity: now,
            session_id: None,
        }
    }

    /// Create a new ProtocolState with comprehensive metadata
    ///
    /// This constructor allows setting all metadata fields during creation,
    /// providing full control over the protocol state initialization.
    ///
    /// # Arguments
    /// * `root_key` - The root key derived from X3DH key agreement
    /// * `dh_ratchet_key` - Our DH ratchet key pair
    /// * `remote_dh_public_key` - The remote party's DH public key (if available)
    /// * `max_skip` - Maximum number of skipped message keys to store
    /// * `remote_identity_key` - The remote party's identity key
    /// * `local_identity_key` - Our identity key
    /// * `signed_prekey` - The signed pre-key used in session establishment
    /// * `onetime_prekey` - The one-time pre-key used (if any)
    /// * `session_id` - Optional session identifier
    ///
    /// # Returns
    /// A new ProtocolState instance with full metadata
    pub fn new_with_metadata(
        root_key: [u8; 32],
        dh_ratchet_key: KeyPair,
        remote_dh_public_key: Option<PublicKey>,
        max_skip: u32,
        remote_identity_key: Option<PublicKey>,
        local_identity_key: Option<PublicKey>,
        signed_prekey: Option<PublicKey>,
        onetime_prekey: Option<PublicKey>,
        session_id: Option<String>,
    ) -> Self {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();

        Self {
            root_key,
            dh_ratchet_key,
            remote_dh_public_key,
            sending_chain_key: None,
            receiving_chain_key: None,
            sending_message_number: 0,
            receiving_message_number: 0,
            previous_chain_length: 0,
            skipped_message_keys: HashMap::new(),
            max_skip,
            remote_identity_key,
            local_identity_key,
            signed_prekey,
            onetime_prekey,
            created_at: now,
            last_activity: now,
            session_id,
        }
    }

    /// Convert from an existing SessionState to ProtocolState
    ///
    /// This method provides a conversion path from the existing SessionState
    /// structure to the new ProtocolState, preserving all cryptographic material
    /// and metadata.
    ///
    /// # Arguments
    /// * `session_state` - The SessionState to convert from
    ///
    /// # Returns
    /// A new ProtocolState with data copied from the SessionState
    pub fn from_session_state(session_state: &SessionState) -> Self {
        Self {
            root_key: session_state.root_key,
            dh_ratchet_key: session_state.dh_ratchet_key.clone(),
            remote_dh_public_key: session_state.remote_dh_public_key,
            sending_chain_key: session_state.sending_chain_key,
            receiving_chain_key: session_state.receiving_chain_key,
            sending_message_number: session_state.sending_message_number,
            receiving_message_number: session_state.receiving_message_number,
            previous_chain_length: session_state.previous_chain_length,
            skipped_message_keys: session_state.skipped_message_keys.clone(),
            max_skip: session_state.max_skip,
            remote_identity_key: session_state.remote_identity_key,
            local_identity_key: session_state.local_identity_key,
            signed_prekey: None, // SessionState doesn't have this field
            onetime_prekey: None, // SessionState doesn't have this field
            created_at: session_state.created_at,
            last_activity: session_state.last_activity,
            session_id: session_state.session_id.clone(),
        }
    }


    /// Convert this ProtocolState to a SessionState
    ///
    /// This method provides a conversion path to the existing SessionState
    /// structure, allowing interoperability with existing code.
    ///
    /// # Returns
    /// A new SessionState with data copied from this ProtocolState
    pub fn to_session_state(&self) -> SessionState {
        SessionState {
            root_key: self.root_key,
            dh_ratchet_key: self.dh_ratchet_key.clone(),
            remote_dh_public_key: self.remote_dh_public_key,
            sending_chain_key: self.sending_chain_key,
            receiving_chain_key: self.receiving_chain_key,
            sending_message_number: self.sending_message_number,
            receiving_message_number: self.receiving_message_number,
            previous_chain_length: self.previous_chain_length,
            skipped_message_keys: self.skipped_message_keys.clone(),
            max_skip: self.max_skip,
            remote_identity_key: self.remote_identity_key,
            local_identity_key: self.local_identity_key,
            created_at: self.created_at,
            last_activity: self.last_activity,
            session_id: self.session_id.clone(),
        }
    }


    /// Update the last activity timestamp to the current time
    ///
    /// This method should be called whenever the protocol state is used
    /// for cryptographic operations to maintain accurate activity tracking.
    pub fn update_activity(&mut self) {
        self.last_activity = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();
    }

    /// Check if this protocol state can be used for sending messages
    ///
    /// A protocol state can send messages if it has a sending chain key established.
    ///
    /// # Returns
    /// True if the state can be used for encryption, false otherwise
    pub fn can_send(&self) -> bool {
        self.sending_chain_key.is_some()
    }

    /// Check if this protocol state can be used for receiving messages
    ///
    /// A protocol state can receive messages if it has a receiving chain key established.
    ///
    /// # Returns
    /// True if the state can be used for decryption, false otherwise
    pub fn can_receive(&self) -> bool {
        self.receiving_chain_key.is_some()
    }

    /// Get the current number of skipped message keys
    ///
    /// This indicates how many out-of-order messages the session is tracking.
    /// A high number might indicate network issues or potential attacks.
    ///
    /// # Returns
    /// The number of skipped message keys currently stored
    pub fn skipped_keys_count(&self) -> usize {
        self.skipped_message_keys.len()
    }

    /// Check if the protocol state is at the maximum skip limit
    ///
    /// # Returns
    /// True if at maximum capacity, false otherwise
    pub fn is_at_max_skip(&self) -> bool {
        self.skipped_keys_count() >= self.max_skip as usize
    }

    /// Check if the protocol state is approaching the maximum skip limit
    ///
    /// Returns true if the number of skipped keys is close to the maximum,
    /// which might indicate the need for cleanup or session reset.
    ///
    /// # Returns
    /// True if approaching the limit (>75% of max), false otherwise
    pub fn is_near_skip_limit(&self) -> bool {
        let current = self.skipped_keys_count();
        let max = self.max_skip as usize;
        current > (max * 3 / 4) // 75% of max
    }

    /// Clear all skipped message keys
    ///
    /// This method removes all stored skipped message keys, which can be useful
    /// for cleanup or when resetting the session state.
    pub fn clear_skipped_keys(&mut self) {
        self.skipped_message_keys.clear();
    }

    /// Get the current DH public key for this protocol state
    ///
    /// # Returns
    /// The public key component of our DH ratchet key pair
    pub fn get_dh_public_key(&self) -> PublicKey {
        self.dh_ratchet_key.public_key()
    }

    /// Get session age in seconds (if created_at is available)
    ///
    /// # Returns
    /// The age of the session in seconds, or None if creation time is unknown
    pub fn session_age_seconds(&self) -> Option<u64> {
        self.created_at.and_then(|created| {
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .map(|d| d.as_secs().saturating_sub(created))
                .ok()
        })
    }

    /// Get time since last activity in seconds (if last_activity is available)
    ///
    /// # Returns
    /// The time since last activity in seconds, or None if activity time is unknown
    pub fn time_since_last_activity_seconds(&self) -> Option<u64> {
        self.last_activity.and_then(|last| {
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .map(|d| d.as_secs().saturating_sub(last))
                .ok()
        })
    }

    /// Serialize the protocol state to JSON format
    ///
    /// This method converts the entire protocol state into a JSON string
    /// that can be stored externally and later restored.
    ///
    /// # Returns
    /// A JSON string representation of the protocol state
    pub fn to_json(&self) -> Result<String> {
        serde_json::to_string(self)
            .map_err(|e| ProtocolError::serialization_error("ProtocolState", e.to_string()))
    }

    /// Deserialize the protocol state from JSON format
    ///
    /// This method reconstructs a ProtocolState from a JSON string.
    ///
    /// # Arguments
    /// * `json` - The JSON string to deserialize
    ///
    /// # Returns
    /// A restored ProtocolState instance
    pub fn from_json(json: &str) -> Result<Self> {
        serde_json::from_str(json)
            .map_err(|e| ProtocolError::deserialization_error("ProtocolState", e.to_string()))
    }

    /// Serialize the protocol state to bytes (using JSON internally)
    ///
    /// This method converts the protocol state into an opaque byte array
    /// suitable for external storage systems.
    ///
    /// # Returns
    /// A byte vector containing the serialized protocol state
    pub fn to_bytes(&self) -> Result<Vec<u8>> {
        let json = self.to_json()?;
        Ok(json.into_bytes())
    }

    /// Deserialize the protocol state from bytes (expecting JSON format)
    ///
    /// This method reconstructs a ProtocolState from a byte array.
    ///
    /// # Arguments
    /// * `bytes` - The byte array to deserialize
    ///
    /// # Returns
    /// A restored ProtocolState instance
    pub fn from_bytes(bytes: &[u8]) -> Result<Self> {
        let json = std::str::from_utf8(bytes)
            .map_err(|e| crate::error::X3dhError::DeserializationError(e.to_string()))?;
        Self::from_json(json)
    }

    /// Get the serialized size of the protocol state in bytes (JSON format)
    ///
    /// This method returns the size that the protocol state would occupy
    /// when serialized, which is useful for storage planning.
    ///
    /// # Returns
    /// The size in bytes of the serialized protocol state
    pub fn serialized_size(&self) -> Result<usize> {
        let bytes = self.to_bytes()?;
        Ok(bytes.len())
    }
}

impl Zeroize for ProtocolState {
    fn zeroize(&mut self) {
        // Zeroize all sensitive cryptographic material
        self.root_key.zeroize();
        self.dh_ratchet_key.zeroize();
        // Note: remote_dh_public_key is public data, no need to zeroize
        
        if let Some(ref mut key) = self.sending_chain_key {
            key.zeroize();
        }
        if let Some(ref mut key) = self.receiving_chain_key {
            key.zeroize();
        }
        
        // Zeroize all skipped message keys
        for (_, message_key) in self.skipped_message_keys.iter_mut() {
            message_key.zeroize();
        }
        self.skipped_message_keys.clear();
        
        // Note: Public keys, counters, and metadata don't need zeroization
    }
}

impl ZeroizeOnDrop for ProtocolState {}
