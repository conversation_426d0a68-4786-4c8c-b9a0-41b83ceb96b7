use super::health::SessionHealthInfo;
use super::skipped_keys::{skipped_keys_serde, SkippedMessageKeys};
use crate::crypto::{
    bundle::PreKeyBundle,
    keys::{KeyPair, PublicKey},
    x3dh::{x3dh_initiator, x3dh_recipient, X3dhInitialMessage, X3dhParams, X3dhRecipientParams},
};
use crate::error::{ProtocolError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use zeroize::{Zeroize, ZeroizeOnDrop};

/// A comprehensive data structure that encapsulates the complete state of a cryptographic session
///
/// This struct maintains all cryptographic materials managed by the Double Ratchet algorithm,
/// including root keys, chain keys, message counters, and skipped message keys. It serves as
/// the primary container for session state that can be serialized for external storage.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionState {
    /// Root Key (RK) - used to derive new chain keys when the DH ratchet advances
    /// This is the master key from which all other keys in the session are derived
    pub root_key: [u8; 32],

    /// Our Diffie-Hellman ratchet key pair (DHR)
    /// This is our current DH key pair used for the DH ratchet mechanism
    pub dh_ratchet_key: KeyPair,

    /// Remote party's DH ratchet public key (DHP)
    /// This is the most recent DH public key received from the remote party
    pub remote_dh_public_key: Option<PublicKey>,

    /// Sending Chain Key (CKs) - used to derive message keys for outgoing messages
    /// None if we don't have a sending chain established yet
    pub sending_chain_key: Option<[u8; 32]>,

    /// Receiving Chain Key (CKr) - used to derive message keys for incoming messages
    /// None if we don't have a receiving chain established yet
    pub receiving_chain_key: Option<[u8; 32]>,

    /// Number of messages sent in the current sending chain (Ns)
    /// This counter increments with each message we send
    pub sending_message_number: u32,

    /// Number of messages received in the current receiving chain (Nr)
    /// This counter increments with each message we receive in order
    pub receiving_message_number: u32,

    /// Previous chain length (PN) - number of messages in the previous sending chain
    /// This is used when advancing the DH ratchet to inform the recipient
    pub previous_chain_length: u32,

    /// Storage for skipped message keys
    /// Maps (header public key bytes, message number) to the message key
    /// This allows for out-of-order message decryption
    #[serde(with = "skipped_keys_serde")]
    pub skipped_message_keys: SkippedMessageKeys,

    /// Maximum number of skipped message keys to store
    /// This prevents unbounded memory growth from skipped messages
    pub max_skip: u32,

    /// Session metadata: Remote party's identity key
    /// This is used to verify the identity of the remote party
    pub remote_identity_key: Option<PublicKey>,

    /// Session metadata: Our identity key
    /// This is our long-term identity key used in the session
    pub local_identity_key: Option<PublicKey>,

    /// Session metadata: Session creation timestamp
    /// Unix timestamp when this session was created
    pub created_at: Option<u64>,

    /// Session metadata: Last activity timestamp
    /// Unix timestamp when this session was last used
    pub last_activity: Option<u64>,

    /// Session metadata: Session identifier
    /// A unique identifier for this session (optional)
    pub session_id: Option<String>,
}

impl SessionState {
    /// Check if this session can encrypt messages
    ///
    /// A session can encrypt if it has the necessary cryptographic state.
    /// This includes having a DH ratchet key and being properly initialized.
    pub fn can_encrypt(&self) -> bool {
        self.can_send()
    }

    /// Check if this session can decrypt messages
    ///
    /// A session can decrypt if it has been properly initialized and
    /// has the necessary cryptographic state for receiving.
    pub fn can_decrypt(&self) -> bool {
        self.can_receive()
    }

    /// Get the current number of skipped message keys
    ///
    /// This indicates how many out-of-order messages the session is tracking.
    /// A high number might indicate network issues or potential attacks.
    pub fn skipped_message_count(&self) -> usize {
        self.skipped_keys_count()
    }

    /// Check if the session is approaching the maximum skip limit
    ///
    /// Returns true if the number of skipped keys is close to the maximum,
    /// which might indicate the need for cleanup or session reset.
    pub fn is_near_skip_limit(&self) -> bool {
        let current = self.skipped_keys_count();
        let max = self.max_skip as usize;
        current > (max * 3 / 4) // 75% of max
    }

    /// Get session health information
    ///
    /// Returns a summary of the session's current state for monitoring.
    pub fn health_info(&self) -> SessionHealthInfo {
        SessionHealthInfo {
            can_send: self.can_send(),
            can_receive: self.can_receive(),
            skipped_keys: self.skipped_keys_count(),
            max_skip: self.max_skip,
            near_skip_limit: self.is_near_skip_limit(),
            age_seconds: self.time_since_last_activity_seconds(),
            last_activity_seconds: self.time_since_last_activity_seconds(),
        }
    }
    /// Encrypt a message using the Double Ratchet algorithm
    ///
    /// This function advances the sending chain and creates a message header
    /// containing the necessary information for the recipient to decrypt the message.
    /// The session state is automatically updated and the activity timestamp is refreshed.
    ///
    /// # Arguments
    /// * `plaintext` - The message to encrypt (must not be empty)
    ///
    /// # Returns
    /// A tuple containing (message_header, ciphertext) where:
    /// - `message_header` contains the DH public key and message number for decryption
    /// - `ciphertext` contains the encrypted message data
    ///
    /// # Errors
    /// - `SessionError` if the session cannot send messages (missing keys)
    /// - `CryptoError` if encryption fails
    /// - `ProtocolViolation` if plaintext is empty
    pub fn encrypt(
        &mut self,
        plaintext: &[u8],
    ) -> Result<(crate::protocol::MessageHeader, Vec<u8>)> {
        // Allow empty messages - they are valid in the Signal protocol

        // Validate session state
        if !self.can_send() {
            return Err(ProtocolError::invalid_session_state(
                "message encryption",
                "session is not ready for sending messages",
            ));
        }
        // Implement encryption directly in SessionState
        // This is a simplified implementation that maintains the core Double Ratchet logic

        // If we don't have a sending chain key, we need to perform a DH ratchet step
        if self.sending_chain_key.is_none() {
            if let Some(remote_dh_key) = self.remote_dh_public_key {
                // Perform DH ratchet step
                let dh_output = crate::protocol::x25519_diffie_hellman(
                    &self.dh_ratchet_key.private_key(),
                    &remote_dh_key,
                )?;

                // Derive new root key and sending chain key
                let (new_root_key, new_sending_chain_key) =
                    crate::protocol::kdf_rk(&self.root_key, &dh_output)?;

                self.root_key = new_root_key;
                self.sending_chain_key = Some(new_sending_chain_key);
                self.previous_chain_length = self.sending_message_number;
                self.sending_message_number = 0;
            } else {
                return Err(ProtocolError::invalid_session_state(
                    "DH ratchet step",
                    "No remote DH key available for ratchet step",
                ));
            }
        }

        // Get the current sending chain key
        let chain_key = self.sending_chain_key.ok_or_else(|| {
            ProtocolError::invalid_session_state(
                "message encryption",
                "No sending chain key available",
            )
        })?;

        // Derive message key from chain key
        let message_key = self.derive_message_key(&chain_key)?;

        // Update chain key for next message
        self.sending_chain_key = Some(self.derive_next_chain_key(&chain_key)?);

        // Create message header
        let header = crate::protocol::MessageHeader {
            dh_public_key: self.dh_ratchet_key.public_key(),
            previous_chain_length: self.previous_chain_length,
            message_number: self.sending_message_number,
        };

        // Encrypt the message
        let ciphertext = self.encrypt_with_message_key(&message_key, plaintext)?;

        // Increment message number
        self.sending_message_number += 1;

        let result = (header, ciphertext);

        // Update activity timestamp
        self.update_activity();

        Ok(result)
    }

    /// Decrypt a received message using the Double Ratchet algorithm
    ///
    /// This function handles DH ratchet advancement, out-of-order messages,
    /// and advances the receiving chain as needed. The session state is automatically
    /// updated and the activity timestamp is refreshed.
    ///
    /// # Arguments
    /// * `header` - The message header containing DH key and message number
    /// * `ciphertext` - The encrypted message (includes nonce + ciphertext)
    ///
    /// # Returns
    /// The decrypted plaintext
    ///
    /// # Errors
    /// - `InvalidMessage` if the header is malformed or ciphertext is invalid
    /// - `SessionError` if the session cannot receive messages
    /// - `CryptoError` if decryption fails
    /// - `ProtocolViolation` if message is too far out of order (exceeds max_skip)
    pub fn decrypt(
        &mut self,
        header: &crate::protocol::MessageHeader,
        ciphertext: &[u8],
    ) -> Result<Vec<u8>> {
        // Validate input
        if ciphertext.is_empty() {
            return Err(ProtocolError::InvalidInput {
                details: "Cannot decrypt empty ciphertext".to_string(),
            });
        }

        // For responder sessions, we can receive messages even without a receiving chain key initially
        // The receiving chain key will be established during the first DH ratchet step

        // Check if message would cause too many skipped keys
        if header.message_number > self.receiving_message_number {
            let num_skipped = header.message_number - self.receiving_message_number;
            if self.skipped_keys_count() + num_skipped as usize > self.max_skip as usize {
                return Err(ProtocolError::MessageOutOfOrder {
                    received: header.message_number,
                    expected: self.receiving_message_number,
                });
            }
        }
        // Implement decryption directly in SessionState
        // This is a simplified implementation that maintains the core Double Ratchet logic

        // Check if we need to perform a DH ratchet step (new DH key in header)
        let header_dh_key = header.dh_public_key;

        // If this is a new DH key, perform ratchet step
        if self.remote_dh_public_key.as_ref() != Some(&header_dh_key) {
            // Perform DH ratchet step using our current DH key
            let dh_output = crate::protocol::x25519_diffie_hellman(
                &self.dh_ratchet_key.private_key(),
                &header_dh_key,
            )?;

            // Derive new root key and receiving chain key
            let (new_root_key, new_receiving_chain_key) =
                crate::protocol::kdf_rk(&self.root_key, &dh_output)?;

            self.root_key = new_root_key;
            self.receiving_chain_key = Some(new_receiving_chain_key);
            self.remote_dh_public_key = Some(header_dh_key);
            self.receiving_message_number = 0;

            // Generate new DH key for future sending and establish sending chain
            let new_dh_key = KeyPair::generate().map_err(|_| {
                ProtocolError::key_derivation_failure(
                    "DH key generation",
                    "Failed to generate new DH key",
                )
            })?;

            // Derive sending chain key using the new DH key
            let sending_dh_output =
                crate::protocol::x25519_diffie_hellman(&new_dh_key.private_key(), &header_dh_key)?;
            let (newer_root_key, sending_chain_key) =
                crate::protocol::kdf_rk(&new_root_key, &sending_dh_output)?;

            self.root_key = newer_root_key;
            self.sending_chain_key = Some(sending_chain_key);
            self.dh_ratchet_key = new_dh_key;
            self.previous_chain_length = 0;
            self.sending_message_number = 0;
        }

        // Get the current receiving chain key
        let chain_key = self.receiving_chain_key.ok_or_else(|| {
            ProtocolError::invalid_session_state(
                "message decryption",
                "No receiving chain key available",
            )
        })?;

        // Handle message decryption with proper chain key advancement
        let message_key = if header.message_number == self.receiving_message_number {
            // This is the next expected message
            let message_key = self.derive_message_key(&chain_key)?;
            // Advance the chain key
            self.receiving_chain_key = Some(self.derive_next_chain_key(&chain_key)?);
            self.receiving_message_number += 1;
            message_key
        } else if header.message_number > self.receiving_message_number {
            // This is a future message - store skipped keys
            let mut current_chain_key = chain_key;

            // Store keys for skipped messages
            for msg_num in self.receiving_message_number..header.message_number {
                let skip_key = self.derive_message_key(&current_chain_key)?;
                let skip_header_key = (header.dh_public_key.as_bytes().to_vec(), msg_num);
                self.skipped_message_keys.insert(skip_header_key, skip_key);
                current_chain_key = self.derive_next_chain_key(&current_chain_key)?;
            }

            // Derive key for this message
            let message_key = self.derive_message_key(&current_chain_key)?;

            // Update chain key and message number
            self.receiving_chain_key = Some(self.derive_next_chain_key(&current_chain_key)?);
            self.receiving_message_number = header.message_number + 1;

            message_key
        } else {
            // This is an old message - check if we have a stored key
            let skip_key = (
                header.dh_public_key.as_bytes().to_vec(),
                header.message_number,
            );
            if let Some(stored_key) = self.skipped_message_keys.remove(&skip_key) {
                stored_key
            } else {
                return Err(ProtocolError::InvalidMessageFormat {
                    details: format!("No stored key for old message {}", header.message_number),
                });
            }
        };

        // Decrypt the message
        let result = self.decrypt_with_message_key(&message_key, ciphertext)?;

        // Update activity timestamp
        self.update_activity();

        Ok(result)
    }
    /// Establish a new session as the initiator using a pre-key bundle
    ///
    /// This is the primary method for initiating a new session with another party.
    /// It performs X3DH key agreement and sets up the Double Ratchet.
    ///
    /// # Arguments
    /// * `our_identity_key` - Our long-term identity key pair
    /// * `remote_prekey_bundle` - The recipient's pre-key bundle
    /// * `session_id` - Optional session identifier for tracking
    ///
    /// # Returns
    /// A new SessionState ready for message encryption
    pub fn establish_as_initiator(
        our_identity_key: KeyPair,
        remote_prekey_bundle: &PreKeyBundle,
        session_id: Option<String>,
    ) -> Result<Self> {
        // Get the recipient's Ed25519 verifying key from the bundle
        let recipient_verifying_key = remote_prekey_bundle.identity_verifying_key()?;

        // Perform X3DH key agreement with internal signature verification
        let x3dh_params = X3dhParams {
            identity_key: our_identity_key,
            recipient_bundle: remote_prekey_bundle.clone(),
            onetime_prekey_index: None, // Use first available
        };

        let x3dh_result = x3dh_initiator(x3dh_params, &recipient_verifying_key)?;

        // Create session state from X3DH result
        Self::new_initiator(
            x3dh_result.shared_secret,
            remote_prekey_bundle,
            1000, // Default max_skip
            session_id,
        )
    }

    /// Establish a new session as the responder using an initial message
    ///
    /// This method is used by the recipient to establish a session after
    /// receiving the first message from an initiator.
    ///
    /// # Arguments
    /// * `our_identity_key` - Our long-term identity key pair
    /// * `our_signed_prekey` - Our signed pre-key pair that was used
    /// * `our_onetime_prekey` - Our one-time pre-key pair that was used (if any)
    /// * `initial_message` - The initial message from the initiator
    /// * `session_id` - Optional session identifier for tracking
    ///
    /// # Returns
    /// A new SessionState ready for message decryption and encryption
    pub fn establish_as_responder(
        our_identity_key: KeyPair,
        our_signed_prekey: KeyPair,
        our_onetime_prekey: Option<KeyPair>,
        initial_message: &X3dhInitialMessage,
        session_id: Option<String>,
    ) -> Result<Self> {
        // Perform X3DH key agreement
        let x3dh_params = X3dhRecipientParams {
            identity_key: our_identity_key,
            signed_prekey: our_signed_prekey.clone(),
            onetime_prekey: our_onetime_prekey,
            initial_message: initial_message.clone(),
        };

        let x3dh_result = x3dh_recipient(x3dh_params)?;

        // Create session state from X3DH result
        Self::new_responder(
            x3dh_result.shared_secret,
            our_signed_prekey,
            initial_message.identity_key,
            1000, // Default max_skip
            session_id,
        )
    }

    /// Restore a session from serialized data
    ///
    /// This is the primary method for loading a previously saved session.
    /// It automatically updates the activity timestamp.
    ///
    /// # Arguments
    /// * `serialized_data` - The serialized session data (JSON format)
    ///
    /// # Returns
    /// A restored SessionState ready for use
    pub fn restore(serialized_data: &str) -> Result<Self> {
        Self::restore_and_activate(serialized_data)
    }

    /// Create a new SessionState with the provided parameters
    ///
    /// # Arguments
    /// * `root_key` - The initial root key derived from X3DH
    /// * `dh_ratchet_key` - Our initial DH ratchet key pair
    /// * `remote_dh_public_key` - The remote party's initial DH public key (if available)
    /// * `max_skip` - Maximum number of skipped message keys to store
    ///
    /// # Returns
    /// A new SessionState instance with initialized values
    pub fn new(
        root_key: [u8; 32],
        dh_ratchet_key: KeyPair,
        remote_dh_public_key: Option<PublicKey>,
        max_skip: u32,
    ) -> Self {
        Self {
            root_key,
            dh_ratchet_key,
            remote_dh_public_key,
            sending_chain_key: None,
            receiving_chain_key: None,
            sending_message_number: 0,
            receiving_message_number: 0,
            previous_chain_length: 0,
            skipped_message_keys: HashMap::new(),
            max_skip,
            remote_identity_key: None,
            local_identity_key: None,
            created_at: None,
            last_activity: None,
            session_id: None,
        }
    }

    /// Create a new SessionState with full metadata
    ///
    /// # Arguments
    /// * `root_key` - The initial root key derived from X3DH
    /// * `dh_ratchet_key` - Our initial DH ratchet key pair
    /// * `remote_dh_public_key` - The remote party's initial DH public key (if available)
    /// * `max_skip` - Maximum number of skipped message keys to store
    /// * `remote_identity_key` - The remote party's identity key
    /// * `local_identity_key` - Our identity key
    /// * `session_id` - Optional session identifier
    ///
    /// # Returns
    /// A new SessionState instance with full metadata
    pub fn new_with_metadata(
        root_key: [u8; 32],
        dh_ratchet_key: KeyPair,
        remote_dh_public_key: Option<PublicKey>,
        max_skip: u32,
        remote_identity_key: Option<PublicKey>,
        local_identity_key: Option<PublicKey>,
        session_id: Option<String>,
    ) -> Self {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();

        Self {
            root_key,
            dh_ratchet_key,
            remote_dh_public_key,
            sending_chain_key: None,
            receiving_chain_key: None,
            sending_message_number: 0,
            receiving_message_number: 0,
            previous_chain_length: 0,
            skipped_message_keys: HashMap::new(),
            max_skip,
            remote_identity_key,
            local_identity_key,
            created_at: now,
            last_activity: now,
            session_id,
        }
    }

    /// Update the last activity timestamp to the current time
    pub fn update_activity(&mut self) {
        self.last_activity = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();
    }

    /// Derive a message key from a chain key using HKDF
    fn derive_message_key(&self, chain_key: &[u8; 32]) -> Result<[u8; 32]> {
        use hkdf::Hkdf;
        use sha2::Sha256;

        let hk = Hkdf::<Sha256>::new(None, chain_key);
        let mut message_key = [0u8; 32];
        hk.expand(b"message_key", &mut message_key).map_err(|_| {
            ProtocolError::key_derivation_failure(
                "message key derivation",
                "Failed to derive message key",
            )
        })?;
        Ok(message_key)
    }

    /// Derive the next chain key from the current chain key
    fn derive_next_chain_key(&self, chain_key: &[u8; 32]) -> Result<[u8; 32]> {
        use hkdf::Hkdf;
        use sha2::Sha256;

        let hk = Hkdf::<Sha256>::new(None, chain_key);
        let mut next_chain_key = [0u8; 32];
        hk.expand(b"chain_key", &mut next_chain_key).map_err(|_| {
            ProtocolError::key_derivation_failure(
                "chain key derivation",
                "Failed to derive next chain key",
            )
        })?;
        Ok(next_chain_key)
    }

    /// Encrypt plaintext with a message key using AES-256-GCM
    fn encrypt_with_message_key(
        &self,
        message_key: &[u8; 32],
        plaintext: &[u8],
    ) -> Result<Vec<u8>> {
        use aes_gcm::aead::Aead;
        use aes_gcm::{Aes256Gcm, Key, KeyInit, Nonce};
        use rand_core::{OsRng, RngCore};

        let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(message_key));

        // Generate a random nonce
        let mut nonce_bytes = [0u8; 12];
        OsRng.fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        // Encrypt and prepend nonce to ciphertext
        let ciphertext = cipher
            .encrypt(nonce, plaintext)
            .map_err(|_| ProtocolError::encryption_failure("Failed to encrypt message"))?;

        let mut result = Vec::with_capacity(12 + ciphertext.len());
        result.extend_from_slice(&nonce_bytes);
        result.extend_from_slice(&ciphertext);
        Ok(result)
    }

    /// Decrypt ciphertext with a message key using AES-256-GCM
    fn decrypt_with_message_key(
        &self,
        message_key: &[u8; 32],
        ciphertext: &[u8],
    ) -> Result<Vec<u8>> {
        use aes_gcm::aead::Aead;
        use aes_gcm::{Aes256Gcm, Key, KeyInit, Nonce};

        // Ensure ciphertext is long enough to contain nonce + encrypted data
        if ciphertext.len() < 12 {
            return Err(ProtocolError::decryption_failure("Ciphertext too short"));
        }

        let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(message_key));

        // Extract nonce from the beginning of ciphertext
        let nonce = Nonce::from_slice(&ciphertext[0..12]);
        let encrypted_data = &ciphertext[12..];

        cipher
            .decrypt(nonce, encrypted_data)
            .map_err(|_| ProtocolError::decryption_failure("Failed to decrypt message"))
    }

    /// Check if the session can send messages (has a sending chain key)
    pub fn can_send(&self) -> bool {
        self.sending_chain_key.is_some()
    }

    /// Check if the session can receive messages (has a receiving chain key)
    pub fn can_receive(&self) -> bool {
        self.receiving_chain_key.is_some()
    }

    /// Get the current DH public key for this session
    pub fn get_dh_public_key(&self) -> PublicKey {
        self.dh_ratchet_key.public_key()
    }

    /// Get the number of skipped message keys currently stored
    pub fn skipped_keys_count(&self) -> usize {
        self.skipped_message_keys.len()
    }

    /// Check if we're at the maximum number of skipped keys
    pub fn is_at_max_skip(&self) -> bool {
        self.skipped_keys_count() >= self.max_skip as usize
    }

    /// Clear all skipped message keys (useful for cleanup)
    pub fn clear_skipped_keys(&mut self) {
        self.skipped_message_keys.clear();
    }

    /// Get session age in seconds (if created_at is available)
    pub fn session_age_seconds(&self) -> Option<u64> {
        self.created_at.and_then(|created| {
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .map(|d| d.as_secs().saturating_sub(created))
                .ok()
        })
    }

    /// Get time since last activity in seconds (if last_activity is available)
    pub fn time_since_last_activity_seconds(&self) -> Option<u64> {
        self.last_activity.and_then(|last| {
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .map(|d| d.as_secs().saturating_sub(last))
                .ok()
        })
    }

    /// Initialize a new session as the initiator (Alice) from X3DH shared secret
    ///
    /// The initiator starts with a sending chain and can immediately send messages.
    /// This method takes the output of X3DH key agreement and bootstraps the Double Ratchet.
    ///
    /// # Arguments
    /// * `x3dh_shared_secret` - The shared secret derived from X3DH key agreement
    /// * `remote_bundle` - The recipient's pre-key bundle used in X3DH
    /// * `max_skip` - Maximum number of skipped message keys to store
    /// * `session_id` - Optional session identifier
    ///
    /// # Returns
    /// A new SessionState configured as an initiator
    pub fn new_initiator(
        x3dh_shared_secret: [u8; 32],
        remote_bundle: &PreKeyBundle,
        max_skip: u32,
        session_id: Option<String>,
    ) -> Result<Self> {
        // Generate our initial DH ratchet key pair
        let our_dh_key = KeyPair::generate()?;

        // Use X3DH shared secret as initial root key
        let root_key = x3dh_shared_secret;

        // Perform initial DH ratchet step to derive sending chain key
        let dh_output = crate::protocol::x25519_diffie_hellman(
            &our_dh_key.private_key(),
            &remote_bundle.signed_prekey(),
        )?;
        let (new_root_key, sending_chain_key) = crate::protocol::kdf_rk(&root_key, &dh_output)?;

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();

        Ok(Self {
            root_key: new_root_key,
            dh_ratchet_key: our_dh_key,
            remote_dh_public_key: Some(remote_bundle.signed_prekey()),
            sending_chain_key: Some(sending_chain_key),
            receiving_chain_key: None, // Will be established when we receive first message
            sending_message_number: 0,
            receiving_message_number: 0,
            previous_chain_length: 0,
            skipped_message_keys: HashMap::new(),
            max_skip,
            remote_identity_key: Some(remote_bundle.identity_key()),
            local_identity_key: None, // Can be set later if needed
            created_at: now,
            last_activity: now,
            session_id,
        })
    }

    /// Initialize a new session as the responder (Bob) from X3DH shared secret
    ///
    /// The responder starts without chains and waits for the first message from the initiator.
    /// This method takes the output of X3DH key agreement and prepares for Double Ratchet.
    ///
    /// # Arguments
    /// * `x3dh_shared_secret` - The shared secret derived from X3DH key agreement
    /// * `our_signed_prekey` - Our signed pre-key pair that was used in X3DH
    /// * `remote_identity_key` - The initiator's identity key
    /// * `max_skip` - Maximum number of skipped message keys to store
    /// * `session_id` - Optional session identifier
    ///
    /// # Returns
    /// A new SessionState configured as a responder
    pub fn new_responder(
        x3dh_shared_secret: [u8; 32],
        our_signed_prekey: KeyPair,
        remote_identity_key: PublicKey,
        max_skip: u32,
        session_id: Option<String>,
    ) -> Result<Self> {
        // Use X3DH shared secret as initial root key
        let root_key = x3dh_shared_secret;

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();

        Ok(Self {
            root_key,
            dh_ratchet_key: our_signed_prekey,
            remote_dh_public_key: None, // Will be set when we receive first message
            sending_chain_key: None,    // Will be established after first DH ratchet
            receiving_chain_key: None,  // Will be established when we receive first message
            sending_message_number: 0,
            receiving_message_number: 0,
            previous_chain_length: 0,
            skipped_message_keys: HashMap::new(),
            max_skip,
            remote_identity_key: Some(remote_identity_key),
            local_identity_key: None, // Can be set later if needed
            created_at: now,
            last_activity: now,
            session_id,
        })
    }

    /// Initialize a new session from a shared secret with minimal parameters
    ///
    /// This is a simplified constructor for cases where you have a shared secret
    /// but don't need the full X3DH bundle information.
    ///
    /// # Arguments
    /// * `shared_secret` - The shared secret to use as the root key
    /// * `our_dh_key` - Our DH ratchet key pair
    /// * `remote_dh_key` - The remote party's DH public key (optional)
    /// * `max_skip` - Maximum number of skipped message keys to store
    /// * `is_initiator` - Whether this session is the initiator
    ///
    /// # Returns
    /// A new SessionState initialized with the provided parameters
    pub fn from_shared_secret(
        shared_secret: [u8; 32],
        our_dh_key: KeyPair,
        remote_dh_key: Option<PublicKey>,
        max_skip: u32,
        is_initiator: bool,
    ) -> Result<Self> {
        let mut session_state =
            Self::new(shared_secret, our_dh_key.clone(), remote_dh_key, max_skip);

        // If this is the initiator and we have a remote DH key, set up sending chain
        if is_initiator && remote_dh_key.is_some() {
            let remote_key = remote_dh_key.unwrap();
            let dh_output =
                crate::protocol::x25519_diffie_hellman(&our_dh_key.private_key(), &remote_key)?;
            let (new_root_key, sending_chain_key) =
                crate::protocol::kdf_rk(&shared_secret, &dh_output)?;

            session_state.root_key = new_root_key;
            session_state.sending_chain_key = Some(sending_chain_key);
        }

        // Set timestamps
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();
        session_state.created_at = now;
        session_state.last_activity = now;

        Ok(session_state)
    }

    /// Restore a session from serialized state and update activity timestamp
    ///
    /// This method is useful when loading a session from storage and you want
    /// to mark it as recently active.
    ///
    /// # Arguments
    /// * `serialized_state` - The serialized session state
    ///
    /// # Returns
    /// A restored SessionState with updated activity timestamp
    pub fn restore_and_activate(serialized_state: &str) -> Result<Self> {
        let mut session_state: Self = serde_json::from_str(serialized_state)
            .map_err(|e| crate::error::X3dhError::DeserializationError(e.to_string()))?;

        session_state.update_activity();
        Ok(session_state)
    }

    /// Serialize the session state to JSON format
    ///
    /// This method converts the entire session state into a JSON string
    /// that can be stored externally and later restored.
    ///
    /// # Returns
    /// A JSON string representation of the session state
    pub fn to_json(&self) -> Result<String> {
        serde_json::to_string(self)
            .map_err(|e| ProtocolError::serialization_error("SessionState", e.to_string()))
    }

    /// Deserialize the session state from JSON format
    ///
    /// This method reconstructs a SessionState from a JSON string.
    ///
    /// # Arguments
    /// * `json` - The JSON string to deserialize
    ///
    /// # Returns
    /// A restored SessionState instance
    pub fn from_json(json: &str) -> Result<Self> {
        serde_json::from_str(json)
            .map_err(|e| ProtocolError::deserialization_error("SessionState", e.to_string()))
    }

    /// Serialize the session state to bytes (using JSON internally)
    ///
    /// This method converts the session state into an opaque byte array
    /// suitable for external storage systems.
    ///
    /// # Returns
    /// A byte vector containing the serialized session state
    pub fn to_bytes(&self) -> Result<Vec<u8>> {
        let json = self.to_json()?;
        Ok(json.into_bytes())
    }

    /// Deserialize the session state from bytes (expecting JSON format)
    ///
    /// This method reconstructs a SessionState from a byte array.
    ///
    /// # Arguments
    /// * `bytes` - The byte array to deserialize
    ///
    /// # Returns
    /// A restored SessionState instance
    pub fn from_bytes(bytes: &[u8]) -> Result<Self> {
        let json = std::str::from_utf8(bytes)
            .map_err(|e| crate::error::X3dhError::DeserializationError(e.to_string()))?;
        Self::from_json(json)
    }

    /// Serialize the session state to a compact binary format using bincode
    ///
    /// This method provides a more space-efficient serialization compared to JSON.
    /// Note: This requires the bincode dependency to be added to Cargo.toml
    ///
    /// # Returns
    /// A byte vector containing the binary-serialized session state
    pub fn to_binary(&self) -> Result<Vec<u8>> {
        // For now, we'll use JSON as the binary format since bincode isn't in dependencies
        // In a production implementation, you would add bincode to Cargo.toml and use:
        // bincode::serialize(self).map_err(|e| crate::error::X3dhError::SerializationError(e.to_string()))
        self.to_bytes()
    }

    /// Deserialize the session state from a compact binary format
    ///
    /// This method reconstructs a SessionState from a binary-serialized byte array.
    ///
    /// # Arguments
    /// * `bytes` - The binary data to deserialize
    ///
    /// # Returns
    /// A restored SessionState instance
    pub fn from_binary(bytes: &[u8]) -> Result<Self> {
        // For now, we'll use JSON as the binary format since bincode isn't in dependencies
        // In a production implementation, you would use:
        // bincode::deserialize(bytes).map_err(|e| crate::error::X3dhError::DeserializationError(e.to_string()))
        Self::from_bytes(bytes)
    }

    /// Get the serialized size of the session state in bytes (JSON format)
    ///
    /// This method returns the size that the session state would occupy
    /// when serialized, which is useful for storage planning.
    ///
    /// # Returns
    /// The size in bytes of the serialized session state
    pub fn serialized_size(&self) -> Result<usize> {
        let bytes = self.to_bytes()?;
        Ok(bytes.len())
    }

    /// Create a minimal serializable representation of the session state
    ///
    /// This method creates a version of the session state that contains only
    /// the essential cryptographic material, excluding metadata like timestamps.
    /// This can be useful for more compact storage or when metadata isn't needed.
    ///
    /// # Returns
    /// A minimal SessionState with only essential cryptographic data
    pub fn to_minimal(&self) -> Self {
        Self {
            root_key: self.root_key,
            dh_ratchet_key: self.dh_ratchet_key.clone(),
            remote_dh_public_key: self.remote_dh_public_key,
            sending_chain_key: self.sending_chain_key,
            receiving_chain_key: self.receiving_chain_key,
            sending_message_number: self.sending_message_number,
            receiving_message_number: self.receiving_message_number,
            previous_chain_length: self.previous_chain_length,
            skipped_message_keys: self.skipped_message_keys.clone(),
            max_skip: self.max_skip,
            remote_identity_key: self.remote_identity_key,
            local_identity_key: self.local_identity_key,
            created_at: None,    // Exclude metadata
            last_activity: None, // Exclude metadata
            session_id: None,    // Exclude metadata
        }
    }
}

impl Zeroize for SessionState {
    fn zeroize(&mut self) {
        // Zeroize all sensitive cryptographic material
        self.root_key.zeroize();
        self.dh_ratchet_key.zeroize();
        // Note: remote_dh_public_key is public data, no need to zeroize

        if let Some(ref mut key) = self.sending_chain_key {
            key.zeroize();
        }
        if let Some(ref mut key) = self.receiving_chain_key {
            key.zeroize();
        }

        // Zeroize all skipped message keys
        for (_, message_key) in self.skipped_message_keys.iter_mut() {
            message_key.zeroize();
        }
        self.skipped_message_keys.clear();

        // Note: Public keys, counters, and metadata don't need zeroization
    }
}

impl ZeroizeOnDrop for SessionState {}
