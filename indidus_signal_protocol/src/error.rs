use thiserror::Error;

/// Core protocol error types for the Signal Protocol implementation
///
/// This enum serves as the foundational error type for all protocol-level operations,
/// providing comprehensive coverage of cryptographic failures, protocol violations,
/// and invalid state conditions. Each variant includes descriptive error messages
/// with actionable guidance for developers.
#[derive(Error, Debug)]
pub enum ProtocolError {
    // === Cryptographic Failures ===
    /// Decryption operation failed
    #[error("Message decryption failed: {reason}. Ensure the correct session state and message key are being used.")]
    DecryptionFailure { reason: String },

    /// Encryption operation failed
    #[error("Message encryption failed: {reason}. Check that the session has a valid sending chain key.")]
    EncryptionFailure { reason: String },

    /// Digital signature verification failed
    #[error("Signature verification failed for {context}. The signature may be invalid or the wrong verifying key was used.")]
    InvalidSignature { context: String },

    /// Pre-key bundle signature verification failed
    #[error("Pre-key bundle signature verification failed. The signed pre-key signature is invalid or does not match the recipient's identity key.")]
    InvalidPreKeySignature,

    /// Key derivation function failed
    #[error("Key derivation failed during {operation}: {reason}. This may indicate corrupted cryptographic state.")]
    KeyDerivationFailure { operation: String, reason: String },

    /// Random number generation failed
    #[error("Random number generation failed: {reason}. Ensure the system has sufficient entropy.")]
    RandomnessFailure { reason: String },

    // === Protocol Violations ===
    /// Message format is invalid or corrupted
    #[error("Invalid message format: {details}. The message may be corrupted or from an incompatible protocol version.")]
    InvalidMessageFormat { details: String },

    /// Pre-key is stale or has been used
    #[error("Stale pre-key detected: {details}. Request a fresh pre-key bundle from the recipient.")]
    StalePreKey { details: String },

    /// Message replay attack detected
    #[error("Message replay detected: message number {message_number} has already been processed. This may indicate a replay attack.")]
    MessageReplay { message_number: u32 },

    /// Message arrived out of acceptable order
    #[error("Message out of order: received message {received} but expected {expected}. Too many messages may have been skipped.")]
    MessageOutOfOrder { received: u32, expected: u32 },

    /// Protocol version mismatch
    #[error("Protocol version mismatch: expected {expected}, got {received}. Update to a compatible protocol version.")]
    VersionMismatch { expected: String, received: String },

    // === Invalid State Conditions ===
    /// Session not found or not initialized
    #[error("Session not found for {identifier}. Initialize a new session or check the session identifier.")]
    SessionNotFound { identifier: String },

    /// Session is in an invalid state for the requested operation
    #[error("Invalid session state for {operation}: {details}. The session may need to be reset or re-established.")]
    InvalidSessionState { operation: String, details: String },

    /// Required key material is missing
    #[error("Missing key material: {key_type}. Ensure the session is properly initialized with all required keys.")]
    MissingKeyMaterial { key_type: String },

    /// Chain key is exhausted or invalid
    #[error("Chain key exhausted or invalid for {chain_type}. The session may need to advance the ratchet.")]
    ChainKeyExhausted { chain_type: String },

    // === Configuration and Input Errors ===
    /// Invalid key format or length
    #[error("Invalid key format: {details}. Ensure keys are the correct length and format for the operation.")]
    InvalidKeyFormat { details: String },

    /// Invalid configuration parameter
    #[error("Invalid configuration: {parameter} = {value}. {suggestion}")]
    InvalidConfiguration {
        parameter: String,
        value: String,
        suggestion: String,
    },

    /// Input data is invalid or malformed
    #[error("Invalid input: {details}. Check that the input data is properly formatted and within acceptable ranges.")]
    InvalidInput { details: String },

    // === Serialization and Storage Errors ===
    /// Serialization operation failed
    #[error("Serialization failed for {data_type}: {reason}. The data may be too large or contain invalid characters.")]
    SerializationError { data_type: String, reason: String },

    /// Deserialization operation failed
    #[error("Deserialization failed for {data_type}: {reason}. The data may be corrupted or from an incompatible version.")]
    DeserializationError { data_type: String, reason: String },

    // === Legacy X3DH Errors (for backward compatibility) ===
    /// Legacy X3DH error for backward compatibility
    #[error("X3DH operation failed: {0}")]
    X3dhError(#[from] X3dhError),
}

/// Errors that can occur during X3DH protocol operations
///
/// This enum is maintained for backward compatibility and specific X3DH operations.
/// New code should prefer using `ProtocolError` for comprehensive error handling.
#[derive(Error, Debug)]
pub enum X3dhError {
    #[error("Cryptographic operation failed: {0}")]
    CryptoError(String),

    #[error("Invalid key format or length")]
    InvalidKey,

    #[error("Key generation failed")]
    KeyGenerationFailed,

    #[error("Signature verification failed")]
    SignatureVerificationFailed,

    #[error("Invalid signature")]
    InvalidSignature,

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("Deserialization error: {0}")]
    DeserializationError(String),
}

/// Convenience type alias for Results using ProtocolError
pub type Result<T> = std::result::Result<T, ProtocolError>;

/// Legacy type alias for backward compatibility
pub type X3dhResult<T> = std::result::Result<T, X3dhError>;

impl ProtocolError {
    /// Helper function to create a DecryptionFailure error
    pub fn decryption_failure(reason: impl Into<String>) -> Self {
        Self::DecryptionFailure { reason: reason.into() }
    }

    /// Helper function to create an EncryptionFailure error
    pub fn encryption_failure(reason: impl Into<String>) -> Self {
        Self::EncryptionFailure { reason: reason.into() }
    }

    /// Helper function to create an InvalidSignature error
    pub fn invalid_signature(context: impl Into<String>) -> Self {
        Self::InvalidSignature { context: context.into() }
    }

    /// Helper function to create a KeyDerivationFailure error
    pub fn key_derivation_failure(operation: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::KeyDerivationFailure { 
            operation: operation.into(), 
            reason: reason.into() 
        }
    }

    /// Helper function to create an InvalidSessionState error
    pub fn invalid_session_state(operation: impl Into<String>, details: impl Into<String>) -> Self {
        Self::InvalidSessionState { 
            operation: operation.into(), 
            details: details.into() 
        }
    }

    /// Helper function to create a SerializationError
    pub fn serialization_error(data_type: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::SerializationError { 
            data_type: data_type.into(), 
            reason: reason.into() 
        }
    }

    /// Helper function to create a DeserializationError
    pub fn deserialization_error(data_type: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::DeserializationError { 
            data_type: data_type.into(), 
            reason: reason.into() 
        }
    }
}
