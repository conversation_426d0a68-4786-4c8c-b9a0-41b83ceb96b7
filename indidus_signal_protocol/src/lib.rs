//! # Indidus Signal Protocol
//!
//! A Rust implementation of the Signal Protocol, providing end-to-end encryption
//! for messaging applications. This crate implements the X3DH key agreement protocol
//! and the Double Ratchet algorithm for secure, forward-secret communication.
//!
//! ## Features
//!
//! - **X3DH Key Agreement**: Secure initial key exchange between parties
//! - **Double Ratchet**: Forward-secure message encryption with out-of-order support
//! - **Session Management**: Persistent session state with serialization support
//! - **Cryptographic Primitives**: X25519 key exchange and Ed25519 signatures
//!
//! ## Quick Start
//!
//! ### Basic Session Establishment and Message Exchange
//!
//! ```rust
//! use indidus_signal_protocol::{
//!     session_manager,
//!     session::Session,
//!     crypto::{keys::{KeyPair, PublicKey}, bundle::PreKeyBundle},
//! };
//! use indidus_signal_protocol::session::SessionState;
//!
//! # fn main() -> Result<(), Box<dyn std::error::Error>> {
//! // 1. Generate identity keys for both parties
//! let (alice_identity, alice_signing) = session_manager::generate_identity_keys()?;
//! let (bob_identity, bob_signing) = session_manager::generate_identity_keys()?;
//!
//! // 2. Bob generates and publishes a pre-key bundle
//! let bob_bundle = session_manager::generate_prekey_bundle(
//!     &bob_identity,
//!     &bob_signing,
//!     10, // Generate 10 one-time pre-keys
//! )?;
//!
//! // 3. Alice establishes a session using Bob's bundle
//! let mut alice_session = SessionState::establish_as_initiator(
//!     alice_identity,
//!     &bob_bundle,
//!     Some("alice-to-bob".to_string()),
//! )?;
//!
//! // 4. Alice encrypts a message
//! let message = b"Hello Bob! This is a secure message.";
//! let (header, ciphertext) = alice_session.encrypt(message)?;
//!
//! // 5. Alice can save her session state
//! let alice_session_data = alice_session.to_json()?;
//! println!("Session saved: {} bytes", alice_session_data.len());
//!
//! // 6. Later, Alice can restore her session
//! let mut restored_session = SessionState::restore(&alice_session_data)?;
//! # Ok(())
//! # }
//! ```
//!
//! ### Advanced Usage with Session Management
//!
//! ```rust
//! use indidus_signal_protocol::{
//!     session::Session,
//!     crypto::keys::KeyPair,
//! };
//!
//! # fn main() -> Result<(), Box<dyn std::error::Error>> {
//! // Create a session from existing shared secret
//! let shared_secret = [42u8; 32]; // From X3DH key agreement
//! let our_dh_key = KeyPair::generate()?;
//! let remote_dh_key = KeyPair::generate()?.public_key();
//!
//! let mut session = Session::from_shared_secret(
//!     shared_secret,
//!     our_dh_key,
//!     Some(remote_dh_key),
//!     1000, // max_skip
//!     true, // is_initiator
//! )?;
//!
//! // Check session health
//! let health = session.health_info();
//! println!("Can send: {}, Can receive: {}", health.can_send, health.can_receive);
//! println!("Skipped keys: {}/{}", health.skipped_keys, health.max_skip);
//!
//! // Encrypt multiple messages
//! for i in 0..5 {
//!     let message = format!("Message {}", i);
//!     let (header, ciphertext) = session.encrypt(message.as_bytes())?;
//!     println!("Encrypted message {}: {} bytes", i, ciphertext.len());
//! }
//! # Ok(())
//! # }
//! ```
//!
//! ## Security Considerations
//!
//! - **Key Storage**: Private keys should be stored securely and never transmitted
//! - **Session Persistence**: Session state contains sensitive cryptographic material
//! - **Forward Secrecy**: Old message keys are automatically deleted after use
//! - **Out-of-Order Messages**: The protocol handles message reordering up to `max_skip` limit
//!
//! ## Error Handling
//!
//! All operations return `Result<T, X3dhError>` for comprehensive error handling:
//!
//! ```rust
//! use indidus_signal_protocol::{session::Session, error::ProtocolError};
//!
//! # fn example() -> Result<(), ProtocolError> {
//! let session_data = "invalid json";
//! match Session::restore(session_data) {
//!     Ok(session) => println!("Session restored successfully"),
//!     Err(ProtocolError::DeserializationError { data_type, reason }) => {
//!         eprintln!("Failed to restore session {}: {}", data_type, reason);
//!     }
//!     Err(e) => eprintln!("Other error: {}", e),
//! }
//! # Ok(())
//! # }
//! ```

pub mod crypto;
pub mod error;
pub mod protocol;
pub mod session;

// Re-export core types for external use
pub use crypto::{
    bundle::PreKeyBundle,
    keys::{KeyPair, PublicKey},
    x3dh::x3dh_initiator,
};
pub use error::{ProtocolError, Result};
pub use protocol::MessageHeader;
pub use session::Session;

/// Session management utilities for common operations
///
/// This module provides high-level utilities for managing cryptographic sessions,
/// including identity key generation, pre-key bundle creation, and session validation.
/// These functions abstract away the complexity of the underlying cryptographic operations
/// and provide a simple API for common tasks.
///
/// # Examples
///
/// ## Complete Session Setup Workflow
///
/// ```rust
/// use indidus_signal_protocol::session_manager;
///
/// # fn main() -> Result<(), Box<dyn std::error::Error>> {
/// // Step 1: Generate long-term identity keys
/// let (identity_keys, signing_keys) = session_manager::generate_identity_keys()?;
///
/// // Step 2: Generate a pre-key bundle for distribution
/// let prekey_bundle = session_manager::generate_prekey_bundle(
///     &identity_keys,
///     &signing_keys,
///     50, // Generate 50 one-time pre-keys
/// )?;
///
/// // Step 3: Validate the bundle (optional, for verification)
/// session_manager::validate_prekey_bundle(&prekey_bundle, &signing_keys.verifying_key)?;
///
/// println!("Pre-key bundle created with {} one-time keys",
///          prekey_bundle.onetime_prekey_count());
/// # Ok(())
/// # }
/// ```
pub mod session_manager {
    use crate::crypto::{
        bundle::PreKeyBundle,
        keys::{KeyPair, SigningKeyPair},
    };
    use crate::error::Result;
    use ed25519_dalek::Signer;

    /// Generate a new identity key pair for long-term use
    ///
    /// This creates both X25519 keys for key agreement and Ed25519 keys for signing.
    /// The identity keys represent a client's long-term cryptographic identity and
    /// should be generated once and stored securely for the lifetime of the client.
    ///
    /// # Returns
    /// A tuple containing:
    /// - `KeyPair`: X25519 key pair for Diffie-Hellman key exchange
    /// - `SigningKeyPair`: Ed25519 key pair for creating and verifying signatures
    ///
    /// # Errors
    /// Returns `X3dhError::KeyGenerationFailed` if random number generation fails.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use indidus_signal_protocol::session_manager;
    ///
    /// # fn main() -> Result<(), Box<dyn std::error::Error>> {
    /// let (x25519_keys, ed25519_keys) = session_manager::generate_identity_keys()?;
    ///
    /// // The X25519 keys are used for key agreement
    /// println!("X25519 public key: {:?}", x25519_keys.public_key().as_bytes());
    ///
    /// // The Ed25519 keys are used for signatures
    /// println!("Ed25519 verifying key: {:?}", ed25519_keys.verifying_key_bytes());
    /// # Ok(())
    /// # }
    /// ```
    ///
    /// # Security Notes
    /// - Store the private keys securely and never transmit them
    /// - The identity keys should be backed up as they cannot be recovered if lost
    /// - Consider using hardware security modules (HSMs) for high-security applications
    pub fn generate_identity_keys() -> Result<(KeyPair, SigningKeyPair)> {
        let x25519_keys = KeyPair::generate()?;
        let ed25519_keys = SigningKeyPair::generate()?;
        Ok((x25519_keys, ed25519_keys))
    }

    /// Generate a signed pre-key bundle for distribution
    ///
    /// This creates a complete pre-key bundle that can be uploaded to a server
    /// and used by initiators to establish sessions. The bundle includes a signed
    /// pre-key (signed with the identity key) and multiple one-time pre-keys for
    /// forward secrecy.
    ///
    /// # Arguments
    /// * `identity_key` - The long-term identity key pair (X25519)
    /// * `signing_key` - The Ed25519 signing key pair for creating signatures
    /// * `onetime_prekey_count` - Number of one-time pre-keys to generate (recommended: 50-100)
    ///
    /// # Returns
    /// A complete `PreKeyBundle` ready for distribution, containing:
    /// - Identity public key
    /// - Signed pre-key with signature
    /// - Collection of one-time pre-keys
    /// - Creation timestamp
    ///
    /// # Errors
    /// Returns `X3dhError::KeyGenerationFailed` if key generation fails.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use indidus_signal_protocol::session_manager;
    ///
    /// # fn main() -> Result<(), Box<dyn std::error::Error>> {
    /// // Generate identity keys first
    /// let (identity_key, signing_key) = session_manager::generate_identity_keys()?;
    ///
    /// // Create a pre-key bundle with 25 one-time keys
    /// let bundle = session_manager::generate_prekey_bundle(
    ///     &identity_key,
    ///     &signing_key,
    ///     25,
    /// )?;
    ///
    /// println!("Bundle created with {} one-time keys", bundle.onetime_prekey_count());
    ///
    /// // The bundle can be serialized for distribution
    /// let bundle_json = bundle.to_json()?;
    /// println!("Bundle size: {} bytes", bundle_json.len());
    /// # Ok(())
    /// # }
    /// ```
    ///
    /// # Security Notes
    /// - Generate fresh one-time pre-keys regularly
    /// - Store the corresponding private keys securely until used
    /// - Remove used one-time pre-keys from the server
    /// - Rotate signed pre-keys periodically (e.g., weekly)
    pub fn generate_prekey_bundle(
        identity_key: &KeyPair,
        signing_key: &SigningKeyPair,
        onetime_prekey_count: usize,
    ) -> Result<PreKeyBundle> {
        // Generate signed pre-key
        let signed_prekey = KeyPair::generate()?;

        // Create signature over the signed pre-key
        let signature = signing_key
            .signing_key
            .sign(signed_prekey.public_key().as_bytes());

        // Generate one-time pre-keys
        let mut onetime_prekeys = Vec::with_capacity(onetime_prekey_count);
        for _ in 0..onetime_prekey_count {
            onetime_prekeys.push(KeyPair::generate()?.public_key());
        }

        // Create timestamp
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .map(|d| d.as_secs())
            .ok();

        Ok(PreKeyBundle::new(
            identity_key.public_key(),
            signing_key.verifying_key.as_bytes().to_vec(),
            signed_prekey.public_key(),
            crate::crypto::signature::Ed25519Signature::from_bytes(signature.to_bytes()),
            onetime_prekeys,
        )
        .with_timestamp(timestamp))
    }

    /// Validate a pre-key bundle's signature
    ///
    /// This verifies that the signed pre-key was actually signed by the identity key,
    /// ensuring the bundle's authenticity and integrity. This should be called before
    /// using a pre-key bundle received from a server or another party.
    ///
    /// # Arguments
    /// * `bundle` - The pre-key bundle to validate
    /// * `identity_verifying_key` - The Ed25519 verifying key corresponding to the identity
    ///
    /// # Returns
    /// `Ok(())` if the signature is valid, `Err(X3dhError::SignatureVerificationFailed)` if invalid.
    ///
    /// # Examples
    ///
    /// ```rust
    /// use indidus_signal_protocol::session_manager;
    ///
    /// # fn main() -> Result<(), Box<dyn std::error::Error>> {
    /// // Generate keys and bundle
    /// let (identity_key, signing_key) = session_manager::generate_identity_keys()?;
    /// let bundle = session_manager::generate_prekey_bundle(&identity_key, &signing_key, 10)?;
    ///
    /// // Validate the bundle signature
    /// match session_manager::validate_prekey_bundle(&bundle, &signing_key.verifying_key) {
    ///     Ok(()) => println!("Bundle signature is valid"),
    ///     Err(e) => eprintln!("Bundle validation failed: {}", e),
    /// }
    /// # Ok(())
    /// # }
    /// ```
    ///
    /// # Security Notes
    /// - Always validate bundles received from untrusted sources
    /// - Signature validation prevents man-in-the-middle attacks
    /// - Invalid signatures may indicate compromised or malicious bundles
    pub fn validate_prekey_bundle(
        bundle: &PreKeyBundle,
        identity_verifying_key: &ed25519_dalek::VerifyingKey,
    ) -> Result<()> {
        bundle.validate_signature(identity_verifying_key)
    }
}
