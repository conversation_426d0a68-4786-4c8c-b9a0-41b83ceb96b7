
use curve25519_dalek::{
    constants::ED25519_BASEPOINT_TABLE, montgomery::MontgomeryPoint, scalar::<PERSON><PERSON><PERSON>,
};
use ed25519_dalek::{Signing<PERSON>ey, Verifying<PERSON>ey};
use rand_core::{OsRng, Rng<PERSON>ore};
use serde::{Deserialize, Serialize};
use zeroize::{Zeroize, ZeroizeOnDrop};

use crate::error::Result;

/// A 32-byte public key for X25519 key exchange
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct PublicKey([u8; 32]);

/// A 32-byte private key for X25519 key exchange
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub struct PrivateKey([u8; 32]);

impl Zeroize for PrivateKey {
    fn zeroize(&mut self) {
        self.0.zeroize();
    }
}

// Note: We can't implement ZeroizeOnDrop for Copy types
// The caller should manually zeroize when needed

/// A key pair consisting of a private and public key
///
/// This type encapsulates both the private and public components of an X25519 key pair.
/// The private key should be kept secure and never transmitted, while the public key
/// can be safely shared for key exchange operations.
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct KeyPair {
    /// The private key component (kept private for security)
    private_key: PrivateKey,
    /// The public key component (can be shared)
    public_key: PublicKey,
}

impl Zeroize for KeyPair {
    fn zeroize(&mut self) {
        // Only zeroize the private key since the public key doesn't contain secrets
        self.private_key.zeroize();
    }
}

impl ZeroizeOnDrop for KeyPair {}

/// An Ed25519 signing key pair for creating signatures.
///
/// This struct provides a wrapper around `ed25519-dalek`'s `SigningKey` and `VerifyingKey`
/// types, enabling cryptographic signing operations for the Signal protocol implementation.
///
/// # Security Warning
///
/// **Important**: While this struct implements `Zeroize` and `ZeroizeOnDrop` to clear
/// sensitive data from memory when dropped, there is a critical limitation with the
/// underlying cryptographic types.
///
/// As of `ed25519-dalek` version 2.1.1, the underlying `SigningKey` type does **not**
/// implement the `Zeroize` trait. This means that while our wrapper attempts to zeroize
/// the key material (see the `Zeroize` implementation below), the actual secret key data
/// within the `SigningKey` may remain in memory longer than intended.
///
/// **Implications**:
/// - Secret key material may persist in memory after this struct is dropped
/// - Memory dumps or swap files could potentially contain sensitive key data
/// - This represents a potential security vulnerability in high-security environments
///
/// **Mitigation**:
/// We plan to contribute upstream to `ed25519-dalek` to add proper `Zeroize` support.
/// Until then, users should be aware of this limitation when deploying in security-critical
/// environments. Consider additional memory protection mechanisms if required.
///
/// **Tracking**:
/// - `ed25519-dalek` version: 2.1.1
/// - Related upstream issue: [RustCrypto/signatures#478](https://github.com/RustCrypto/signatures/issues/478)
///
/// # Examples
///
/// ```rust
/// use indidus_signal_protocol::crypto::keys::SigningKeyPair;
///
/// // Generate a new signing key pair
/// let keypair = SigningKeyPair::generate().expect("Failed to generate keypair");
///
/// // The keypair will be zeroized when dropped, but see security warning above
/// ```
#[derive(Debug, Clone)]
pub struct SigningKeyPair {
    pub signing_key: SigningKey,
    pub verifying_key: VerifyingKey,
}

impl Zeroize for SigningKeyPair {
    fn zeroize(&mut self) {
        // Manually zeroize the signing key bytes since SigningKey doesn't implement Zeroize
        // We need to access the internal bytes and zeroize them
        
        // Get the secret key bytes and zeroize them
        let secret_bytes = self.signing_key.to_bytes();
        let mut secret_bytes_mut = secret_bytes;
        secret_bytes_mut.zeroize();
        
        // Note: This doesn't actually zeroize the original SigningKey's internal state
        // For complete security, we'd need SigningKey to implement Zeroize itself
        // This is a limitation of using external types that don't support zeroization
    }
}

impl ZeroizeOnDrop for SigningKeyPair {}

// Custom serialization for SigningKeyPair since ed25519_dalek keys don't implement Serialize/Deserialize
impl Serialize for SigningKeyPair {
    fn serialize<S>(&self, serializer: S) -> std::result::Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        use serde::ser::SerializeStruct;
        let mut state = serializer.serialize_struct("SigningKeyPair", 2)?;
        state.serialize_field("signing_key", &self.signing_key.to_bytes())?;
        state.serialize_field("verifying_key", &self.verifying_key.to_bytes())?;
        state.end()
    }
}

impl<'de> Deserialize<'de> for SigningKeyPair {
    fn deserialize<D>(deserializer: D) -> std::result::Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        use serde::de::{self, MapAccess, Visitor};
        use std::fmt;

        #[derive(Deserialize)]
        #[serde(field_identifier, rename_all = "snake_case")]
        enum Field {
            SigningKey,
            VerifyingKey,
        }

        struct SigningKeyPairVisitor;

        impl<'de> Visitor<'de> for SigningKeyPairVisitor {
            type Value = SigningKeyPair;

            fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
                formatter.write_str("struct SigningKeyPair")
            }

            fn visit_map<V>(self, mut map: V) -> std::result::Result<SigningKeyPair, V::Error>
            where
                V: MapAccess<'de>,
            {
                let mut signing_key_bytes: Option<[u8; 32]> = None;
                let mut verifying_key_bytes: Option<[u8; 32]> = None;

                while let Some(key) = map.next_key()? {
                    match key {
                        Field::SigningKey => {
                            if signing_key_bytes.is_some() {
                                return Err(de::Error::duplicate_field("signing_key"));
                            }
                            signing_key_bytes = Some(map.next_value()?);
                        }
                        Field::VerifyingKey => {
                            if verifying_key_bytes.is_some() {
                                return Err(de::Error::duplicate_field("verifying_key"));
                            }
                            verifying_key_bytes = Some(map.next_value()?);
                        }
                    }
                }

                let signing_key_bytes = signing_key_bytes.ok_or_else(|| de::Error::missing_field("signing_key"))?;
                let verifying_key_bytes = verifying_key_bytes.ok_or_else(|| de::Error::missing_field("verifying_key"))?;

                let signing_key = SigningKey::from_bytes(&signing_key_bytes);
                let verifying_key = VerifyingKey::from_bytes(&verifying_key_bytes)
                    .map_err(|e| de::Error::custom(format!("Invalid verifying key: {}", e)))?;

                Ok(SigningKeyPair {
                    signing_key,
                    verifying_key,
                })
            }
        }

        const FIELDS: &'static [&'static str] = &["signing_key", "verifying_key"];
        deserializer.deserialize_struct("SigningKeyPair", FIELDS, SigningKeyPairVisitor)
    }
}

impl PublicKey {
    /// Create a PublicKey from a 32-byte array
    ///
    /// # Arguments
    /// * `bytes` - A 32-byte array representing the public key
    ///
    /// # Returns
    /// A new `PublicKey` instance
    ///
    /// # Examples
    /// ```rust
    /// use indidus_signal_protocol::crypto::keys::PublicKey;
    ///
    /// let key_bytes = [0u8; 32];
    /// let public_key = PublicKey::from_bytes(key_bytes);
    /// ```
    pub fn from_bytes(bytes: [u8; 32]) -> Self {
        Self(bytes)
    }

    /// Get the raw bytes of the public key
    ///
    /// # Returns
    /// A reference to the 32-byte array containing the public key data
    ///
    /// # Examples
    /// ```rust
    /// use indidus_signal_protocol::crypto::keys::PublicKey;
    ///
    /// let key_bytes = [1u8; 32];
    /// let public_key = PublicKey::from_bytes(key_bytes);
    /// assert_eq!(public_key.as_bytes(), &key_bytes);
    /// ```
    pub fn as_bytes(&self) -> &[u8; 32] {
        &self.0
    }

    /// Convert to Montgomery point for X25519 operations
    pub fn to_montgomery(&self) -> MontgomeryPoint {
        MontgomeryPoint(self.0)
    }
}

impl PrivateKey {
    /// Create a PrivateKey from a 32-byte array
    pub fn from_bytes(bytes: [u8; 32]) -> Self {
        Self(bytes)
    }

    /// Get the raw bytes of the private key
    pub fn as_bytes(&self) -> &[u8; 32] {
        &self.0
    }

    /// Convert to Scalar for cryptographic operations
    pub fn to_scalar(&self) -> Scalar {
        Scalar::from_bytes_mod_order(self.0)
    }
}

impl SigningKeyPair {
    /// Generate a new Ed25519 signing key pair
    pub fn generate() -> Result<Self> {
        let mut rng = OsRng;
        let mut secret_bytes = [0u8; 32];
        rng.fill_bytes(&mut secret_bytes);

        let signing_key = SigningKey::from_bytes(&secret_bytes);
        let verifying_key = signing_key.verifying_key();

        Ok(SigningKeyPair {
            signing_key,
            verifying_key,
        })
    }

    /// Create a SigningKeyPair from an existing signing key
    pub fn from_signing_key(signing_key: SigningKey) -> Self {
        let verifying_key = signing_key.verifying_key();
        Self {
            signing_key,
            verifying_key,
        }
    }

    /// Get the verifying key bytes
    pub fn verifying_key_bytes(&self) -> [u8; 32] {
        self.verifying_key.to_bytes()
    }
}

impl KeyPair {
    /// Get the public key component
    pub fn public_key(&self) -> PublicKey {
        self.public_key
    }

    /// Get the private key component
    pub fn private_key(&self) -> PrivateKey {
        self.private_key
    }
    /// Generate a new random X25519 key pair
    pub fn generate() -> Result<Self> {
        let mut rng = OsRng;
        let mut private_bytes = [0u8; 32];
        rng.fill_bytes(&mut private_bytes);

        // Clamp the private key according to X25519 specification
        private_bytes[0] &= 248;
        private_bytes[31] &= 127;
        private_bytes[31] |= 64;

        let private_key = PrivateKey::from_bytes(private_bytes);

        // Compute the public key: public = private * basepoint
        let scalar = Scalar::from_bytes_mod_order(private_bytes);
        let point = &scalar * ED25519_BASEPOINT_TABLE;
        let public_bytes = point.to_montgomery().to_bytes();
        let public_key = PublicKey::from_bytes(public_bytes);

        Ok(KeyPair {
            private_key,
            public_key,
        })
    }

    /// Create a KeyPair from existing private and public keys
    pub fn from_keys(private_key: PrivateKey, public_key: PublicKey) -> Self {
        Self {
            private_key,
            public_key,
        }
    }
}
