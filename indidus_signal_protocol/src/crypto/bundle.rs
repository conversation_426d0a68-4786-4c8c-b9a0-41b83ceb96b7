use ed25519_dalek::Verifying<PERSON><PERSON>;
use serde::{Deserialize, Serialize};

use crate::error::{ProtocolError, Result};

use super::keys::{Key<PERSON>air, <PERSON>Key, SigningKeyPair};
use super::signature::{verify_prekey_signature, Ed25519Signature};

/// A pre-key bundle containing all public keys and signatures needed for X3DH
///
/// This bundle contains all the cryptographic material needed for an initiator
/// to perform X3DH key agreement with a recipient. It includes the recipient's
/// identity key, signed pre-key, and optional one-time pre-keys.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PreKeyBundle {
    /// The client's long-term identity public key (X25519 for key exchange)
    pub identity_key: PublicKey,
    /// The Ed25519 verifying key corresponding to the identity key (for signature verification)
    pub identity_verifying_key: Vec<u8>,
    /// The signed pre-key public key
    pub signed_prekey: PublicKey,
    /// The signature over the signed pre-key, created with the identity key
    pub signed_prekey_signature: Vec<u8>,
    /// A collection of one-time pre-keys
    pub onetime_prekeys: Vec<PublicKey>,
    /// Optional: timestamp when the bundle was created
    pub timestamp: Option<u64>,
}

impl PreKeyBundle {
    /// Get the identity key
    pub fn identity_key(&self) -> PublicKey {
        self.identity_key
    }

    /// Get the Ed25519 verifying key for signature verification
    pub fn identity_verifying_key(&self) -> Result<VerifyingKey> {
        if self.identity_verifying_key.len() != 32 {
            return Err(ProtocolError::InvalidKeyFormat {
                details: "Identity verifying key must be 32 bytes".to_string(),
            });
        }

        let mut key_bytes = [0u8; 32];
        key_bytes.copy_from_slice(&self.identity_verifying_key);

        VerifyingKey::from_bytes(&key_bytes).map_err(|_| ProtocolError::InvalidKeyFormat {
            details: "Invalid Ed25519 verifying key format".to_string(),
        })
    }

    /// Get the signed pre-key
    pub fn signed_prekey(&self) -> PublicKey {
        self.signed_prekey
    }

    /// Get the signed pre-key signature
    pub fn signed_prekey_signature(&self) -> &[u8] {
        &self.signed_prekey_signature
    }

    /// Get the one-time pre-keys
    pub fn onetime_prekeys(&self) -> &[PublicKey] {
        &self.onetime_prekeys
    }

    /// Get the timestamp when the bundle was created
    pub fn timestamp(&self) -> Option<u64> {
        self.timestamp
    }

    /// Get the number of available one-time pre-keys
    pub fn onetime_prekey_count(&self) -> usize {
        self.onetime_prekeys.len()
    }

    /// Set the timestamp for this bundle
    pub fn with_timestamp(mut self, timestamp: Option<u64>) -> Self {
        self.timestamp = timestamp;
        self
    }
    /// Create a new pre-key bundle
    pub fn new(
        identity_key: PublicKey,
        identity_verifying_key: Vec<u8>,
        signed_prekey: PublicKey,
        signed_prekey_signature: Ed25519Signature,
        onetime_prekeys: Vec<PublicKey>,
    ) -> Self {
        Self {
            identity_key,
            identity_verifying_key,
            signed_prekey,
            signed_prekey_signature: signed_prekey_signature.as_bytes().to_vec(),
            onetime_prekeys,
            timestamp: Some(
                std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs(),
            ),
        }
    }

    /// Create a pre-key bundle without timestamp
    pub fn new_without_timestamp(
        identity_key: PublicKey,
        identity_verifying_key: Vec<u8>,
        signed_prekey: PublicKey,
        signed_prekey_signature: Ed25519Signature,
        onetime_prekeys: Vec<PublicKey>,
    ) -> Self {
        Self {
            identity_key,
            identity_verifying_key,
            signed_prekey,
            signed_prekey_signature: signed_prekey_signature.as_bytes().to_vec(),
            onetime_prekeys,
            timestamp: None,
        }
    }

    /// Get the signature as Ed25519Signature
    pub fn get_signature(&self) -> Result<Ed25519Signature> {
        if self.signed_prekey_signature.len() != 64 {
            return Err(ProtocolError::invalid_signature(
                "Pre-key bundle signature verification",
            ));
        }

        let mut sig_bytes = [0u8; 64];
        sig_bytes.copy_from_slice(&self.signed_prekey_signature);
        Ok(Ed25519Signature::from_bytes(sig_bytes))
    }

    /// Serialize the bundle to JSON
    pub fn to_json(&self) -> Result<String> {
        serde_json::to_string(self)
            .map_err(|e| ProtocolError::serialization_error("PreKeyBundle", e.to_string()))
    }

    /// Deserialize the bundle from JSON
    pub fn from_json(json: &str) -> Result<Self> {
        serde_json::from_str(json)
            .map_err(|e| ProtocolError::deserialization_error("PreKeyBundle", e.to_string()))
    }

    /// Serialize the bundle to bytes (using JSON internally)
    pub fn to_bytes(&self) -> Result<Vec<u8>> {
        let json = self.to_json()?;
        Ok(json.into_bytes())
    }

    /// Deserialize the bundle from bytes (expecting JSON format)
    pub fn from_bytes(bytes: &[u8]) -> Result<Self> {
        let json = std::str::from_utf8(bytes)
            .map_err(|e| ProtocolError::deserialization_error("PreKeyBundle", e.to_string()))?;
        Self::from_json(json)
    }

    /// Verify the signature in the bundle
    ///
    /// Note: This method requires the identity key to be converted to a signing key first.
    /// For verification, you should use verify_bundle_signature() with the signing key.
    pub fn verify_signature(&self) -> Result<()> {
        Err(ProtocolError::invalid_signature(
            "Cannot verify signature directly from X25519 public key. Use verify_bundle_signature() with the identity signing key."
        ))
    }

    /// Verify the signature in the bundle using the identity verifying key
    pub fn validate_signature(&self, identity_verifying_key: &VerifyingKey) -> Result<()> {
        let signature = self.get_signature()?;
        verify_prekey_signature(identity_verifying_key, &self.signed_prekey, &signature)
    }

    /// Verify the signature in the bundle using the identity signing key
    pub fn verify_bundle_signature(&self, identity_signing_key: &SigningKeyPair) -> Result<()> {
        let signature = self.get_signature()?;
        verify_prekey_signature(
            &identity_signing_key.verifying_key,
            &self.signed_prekey,
            &signature,
        )
    }
}

/// Generate a complete pre-key bundle for a client
///
/// This function generates all the necessary keys and creates a signed pre-key bundle
/// that can be published to a server for other clients to use in X3DH key agreement.
///
/// # Arguments
/// * `identity_key` - The client's identity key pair
/// * `onetime_prekey_count` - Number of one-time pre-keys to generate
///
/// # Returns
/// A tuple containing the PreKeyBundle and the private keys needed for decryption
pub fn generate_prekey_bundle(
    identity_key: &KeyPair,
    onetime_prekey_count: usize,
) -> Result<(PreKeyBundle, KeyPair, Vec<KeyPair>)> {
    // Generate signed pre-key
    let signed_prekey = super::util::generate_signed_prekey()?;

    // Generate one-time pre-keys
    let onetime_prekeys = super::util::generate_onetime_prekeys(onetime_prekey_count)?;

    // Convert identity key to signing key for creating signature
    let identity_signing_key =
        super::util::x25519_to_ed25519_signing_key(&identity_key.private_key())?;

    // Sign the signed pre-key
    let signature =
        super::signature::sign_prekey(&identity_signing_key, &signed_prekey.public_key())?;

    // Extract public keys from one-time pre-keys
    let onetime_public_keys: Vec<PublicKey> =
        onetime_prekeys.iter().map(|kp| kp.public_key()).collect();

    // Create the bundle using the X25519 identity key (for X3DH key agreement)
    // Include the Ed25519 verifying key for signature verification
    let bundle = PreKeyBundle::new(
        identity_key.public_key(),
        identity_signing_key.verifying_key.as_bytes().to_vec(),
        signed_prekey.public_key(),
        signature,
        onetime_public_keys,
    );

    Ok((bundle, signed_prekey, onetime_prekeys))
}
