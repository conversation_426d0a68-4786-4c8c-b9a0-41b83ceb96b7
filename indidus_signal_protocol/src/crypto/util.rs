use ed25519_dalek::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};

use crate::error::Result;

use super::keys::{KeyPair, PrivateKey, PublicKey, SigningKeyPair};

/// Generate an Identity Key (IK) pair
///
/// The Identity Key is a long-term key pair that represents a client's identity.
/// It should be generated once and stored securely for the lifetime of the client.
pub fn generate_identity_key() -> Result<KeyPair> {
    KeyPair::generate()
}

/// Generate a Signed Pre-key (SPK) pair
///
/// Signed Pre-keys are medium-term keys that are signed by the Identity Key.
/// They should be rotated periodically (e.g., weekly) for forward secrecy.
pub fn generate_signed_prekey() -> Result<KeyPair> {
    KeyPair::generate()
}

/// Generate a One-Time Pre-key (OPK) pair
///
/// One-Time Pre-keys are ephemeral keys used only once to provide forward secrecy.
/// A client should generate many of these and upload them to the server.
pub fn generate_onetime_prekey() -> Result<KeyPair> {
    KeyPair::generate()
}

/// Generate multiple One-Time Pre-key pairs
///
/// This is a convenience function to generate multiple OPK pairs at once.
///
/// # Arguments
/// * `count` - The number of OPK pairs to generate
///
/// # Returns
/// A vector of KeyPair instances, or an error if generation fails
pub fn generate_onetime_prekeys(count: usize) -> Result<Vec<KeyPair>> {
    let mut prekeys = Vec::with_capacity(count);

    for _ in 0..count {
        prekeys.push(generate_onetime_prekey()?);
    }

    Ok(prekeys)
}

/// Convert an X25519 private key to an Ed25519 signing key
///
/// This function derives an Ed25519 signing key from an X25519 private key.
/// This allows using the same key material for both key exchange and signing.
///
/// Note: For proper interoperability, we use the same private key bytes for both
/// X25519 and Ed25519 operations. This is a common pattern in cryptographic protocols.
///
/// # Arguments
/// * `x25519_private` - The X25519 private key
///
/// # Returns
/// A SigningKeyPair for Ed25519 operations
pub fn x25519_to_ed25519_signing_key(x25519_private: &PrivateKey) -> Result<SigningKeyPair> {
    // Use the same private key bytes for Ed25519 signing
    // This is the standard approach for dual-use keys
    let signing_key = SigningKey::from_bytes(x25519_private.as_bytes());

    // Let Ed25519 derive the verifying key from the signing key
    let verifying_key = signing_key.verifying_key();

    // Create the signing key pair
    Ok(SigningKeyPair {
        signing_key,
        verifying_key,
    })
}

/// Convert an X25519 public key to an Ed25519 verifying key
///
/// This function attempts to derive an Ed25519 verifying key from an X25519 public key.
/// However, this conversion is only reliable when both keys are derived from the same
/// private key material.
///
/// Note: For proper signature verification, the Ed25519 public key should be derived
/// from the same private key that was used to create the X25519 public key.
///
/// # Arguments
/// * `x25519_public` - The X25519 public key
///
/// # Returns
/// A VerifyingKey for Ed25519 signature verification
pub fn x25519_to_ed25519_verifying_key(x25519_public: &PublicKey) -> Result<VerifyingKey> {
    // For keys derived from the same private key material, we can try direct conversion
    // This works when the Ed25519 public key was derived from the same private key
    if let Ok(verifying_key) = VerifyingKey::from_bytes(x25519_public.as_bytes()) {
        return Ok(verifying_key);
    }

    // If direct conversion fails, the keys were likely not derived from the same material
    Err(crate::error::ProtocolError::InvalidKeyFormat {
        details: "Cannot convert X25519 public key to Ed25519 verifying key - keys must be derived from same private key"
            .to_string(),
    })
}
