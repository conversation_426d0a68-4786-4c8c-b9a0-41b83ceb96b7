#[cfg(test)]
mod tests {
    use crate::crypto::{bundle::*, ecdh::*, keys::*, signature::*, util::*, x3dh::*};
    use crate::error::ProtocolError;
    use curve25519_dalek::constants::ED25519_BASEPOINT_TABLE;

    #[test]
    fn test_keypair_generation() {
        let keypair = KeyPair::generate().expect("Failed to generate keypair");

        // Ensure keys are different
        assert_ne!(keypair.private_key().as_bytes(), &[0u8; 32]);
        assert_ne!(keypair.public_key().as_bytes(), &[0u8; 32]);

        // Ensure private and public keys are different
        assert_ne!(
            keypair.private_key().as_bytes(),
            keypair.public_key().as_bytes()
        );
    }

    #[test]
    fn test_identity_key_generation() {
        let ik = generate_identity_key().expect("Failed to generate identity key");
        assert_ne!(ik.private_key().as_bytes(), &[0u8; 32]);
        assert_ne!(ik.public_key().as_bytes(), &[0u8; 32]);
    }

    #[test]
    fn test_signed_prekey_generation() {
        let spk = generate_signed_prekey().expect("Failed to generate signed prekey");
        assert_ne!(spk.private_key().as_bytes(), &[0u8; 32]);
        assert_ne!(spk.public_key().as_bytes(), &[0u8; 32]);
    }

    #[test]
    fn test_onetime_prekey_generation() {
        let opk = generate_onetime_prekey().expect("Failed to generate onetime prekey");
        assert_ne!(opk.private_key().as_bytes(), &[0u8; 32]);
        assert_ne!(opk.public_key().as_bytes(), &[0u8; 32]);
    }

    #[test]
    fn test_multiple_onetime_prekeys_generation() {
        let count = 10;
        let opks = generate_onetime_prekeys(count).expect("Failed to generate onetime prekeys");

        assert_eq!(opks.len(), count);

        // Ensure all keys are unique
        for i in 0..count {
            for j in (i + 1)..count {
                assert_ne!(
                    opks[i].private_key().as_bytes(),
                    opks[j].private_key().as_bytes()
                );
                assert_ne!(
                    opks[i].public_key().as_bytes(),
                    opks[j].public_key().as_bytes()
                );
            }
        }
    }

    #[test]
    fn test_key_serialization() {
        let keypair = KeyPair::generate().expect("Failed to generate keypair");

        // Test that keys can be serialized and deserialized
        let serialized = serde_json::to_string(&keypair).expect("Failed to serialize keypair");
        let deserialized: KeyPair =
            serde_json::from_str(&serialized).expect("Failed to deserialize keypair");

        assert_eq!(
            keypair.private_key().as_bytes(),
            deserialized.private_key().as_bytes()
        );
        assert_eq!(
            keypair.public_key().as_bytes(),
            deserialized.public_key().as_bytes()
        );
    }

    #[test]
    fn test_signing_key_generation() {
        let signing_keypair =
            SigningKeyPair::generate().expect("Failed to generate signing keypair");

        // Ensure the verifying key is derived correctly
        assert_eq!(
            signing_keypair.verifying_key_bytes(),
            signing_keypair.signing_key.verifying_key().to_bytes()
        );
    }

    #[test]
    fn test_prekey_signing_and_verification() {
        // Generate identity key and convert to signing key
        let identity_key = generate_identity_key().expect("Failed to generate identity key");
        let identity_signing_key = x25519_to_ed25519_signing_key(&identity_key.private_key())
            .expect("Failed to convert to signing key");

        // Generate signed prekey
        let signed_prekey = generate_signed_prekey().expect("Failed to generate signed prekey");

        // Sign the prekey
        let signature = sign_prekey(&identity_signing_key, &signed_prekey.public_key())
            .expect("Failed to sign prekey");

        // Verify the signature
        verify_prekey_signature(
            &identity_signing_key.verifying_key,
            &signed_prekey.public_key(),
            &signature,
        )
        .expect("Failed to verify signature");
    }

    #[test]
    fn test_signature_verification_fails_with_wrong_key() {
        // Generate two different identity keys
        let identity_key1 = generate_identity_key().expect("Failed to generate identity key 1");
        let identity_key2 = generate_identity_key().expect("Failed to generate identity key 2");

        let identity_signing_key1 = x25519_to_ed25519_signing_key(&identity_key1.private_key())
            .expect("Failed to convert to signing key 1");
        let identity_signing_key2 = x25519_to_ed25519_signing_key(&identity_key2.private_key())
            .expect("Failed to convert to signing key 2");

        // Generate signed prekey
        let signed_prekey = generate_signed_prekey().expect("Failed to generate signed prekey");

        // Sign with key 1
        let signature = sign_prekey(&identity_signing_key1, &signed_prekey.public_key())
            .expect("Failed to sign prekey");

        // Try to verify with key 2 (should fail)
        let result = verify_prekey_signature(
            &identity_signing_key2.verifying_key,
            &signed_prekey.public_key(),
            &signature,
        );

        assert!(result.is_err());
    }

    #[test]
    fn test_signature_verification_fails_with_wrong_message() {
        // Generate identity key and convert to signing key
        let identity_key = generate_identity_key().expect("Failed to generate identity key");
        let identity_signing_key = x25519_to_ed25519_signing_key(&identity_key.private_key())
            .expect("Failed to convert to signing key");

        // Generate two different signed prekeys
        let signed_prekey1 = generate_signed_prekey().expect("Failed to generate signed prekey 1");
        let signed_prekey2 = generate_signed_prekey().expect("Failed to generate signed prekey 2");

        // Sign prekey 1
        let signature = sign_prekey(&identity_signing_key, &signed_prekey1.public_key())
            .expect("Failed to sign prekey");

        // Try to verify signature against prekey 2 (should fail)
        let result = verify_prekey_signature(
            &identity_signing_key.verifying_key,
            &signed_prekey2.public_key(),
            &signature,
        );

        assert!(result.is_err());
    }

    #[test]
    fn test_prekey_bundle_creation() {
        let identity_key = generate_identity_key().expect("Failed to generate identity key");
        let signed_prekey = generate_signed_prekey().expect("Failed to generate signed prekey");
        let onetime_prekeys =
            generate_onetime_prekeys(5).expect("Failed to generate onetime prekeys");

        let identity_signing_key = x25519_to_ed25519_signing_key(&identity_key.private_key())
            .expect("Failed to convert to signing key");
        let signature = sign_prekey(&identity_signing_key, &signed_prekey.public_key())
            .expect("Failed to sign prekey");

        let onetime_public_keys: Vec<PublicKey> =
            onetime_prekeys.iter().map(|kp| kp.public_key()).collect();

        let bundle = PreKeyBundle::new(
            identity_key.public_key(),
            identity_signing_key.verifying_key.as_bytes().to_vec(),
            signed_prekey.public_key(),
            signature,
            onetime_public_keys,
        );

        assert_eq!(bundle.identity_key(), identity_key.public_key());
        assert_eq!(bundle.signed_prekey(), signed_prekey.public_key());
        assert_eq!(bundle.onetime_prekeys().len(), 5);
        assert!(bundle.timestamp().is_some());

        // Verify the signature in the bundle
        bundle
            .verify_bundle_signature(&identity_signing_key)
            .expect("Failed to verify bundle signature");
    }

    #[test]
    fn test_prekey_bundle_serialization() {
        let identity_key = generate_identity_key().expect("Failed to generate identity key");
        let (bundle, _, _) =
            generate_prekey_bundle(&identity_key, 3).expect("Failed to generate prekey bundle");

        // Test JSON serialization
        let json = bundle.to_json().expect("Failed to serialize to JSON");
        let deserialized_bundle =
            PreKeyBundle::from_json(&json).expect("Failed to deserialize from JSON");

        assert_eq!(
            bundle.identity_key().as_bytes(),
            deserialized_bundle.identity_key().as_bytes()
        );
        assert_eq!(
            bundle.signed_prekey().as_bytes(),
            deserialized_bundle.signed_prekey().as_bytes()
        );
        assert_eq!(
            bundle.signed_prekey_signature(),
            deserialized_bundle.signed_prekey_signature()
        );
        assert_eq!(
            bundle.onetime_prekeys().len(),
            deserialized_bundle.onetime_prekeys().len()
        );

        // Test bytes serialization
        let bytes = bundle.to_bytes().expect("Failed to serialize to bytes");
        let deserialized_bundle2 =
            PreKeyBundle::from_bytes(&bytes).expect("Failed to deserialize from bytes");

        assert_eq!(
            bundle.identity_key().as_bytes(),
            deserialized_bundle2.identity_key().as_bytes()
        );
        assert_eq!(
            bundle.signed_prekey().as_bytes(),
            deserialized_bundle2.signed_prekey().as_bytes()
        );
    }

    #[test]
    fn test_generate_prekey_bundle() {
        let identity_key = generate_identity_key().expect("Failed to generate identity key");
        let onetime_count = 10;

        let (bundle, signed_prekey_private, onetime_private_keys) =
            generate_prekey_bundle(&identity_key, onetime_count)
                .expect("Failed to generate prekey bundle");

        // Verify bundle structure
        assert_eq!(bundle.identity_key(), identity_key.public_key());
        assert_eq!(bundle.signed_prekey(), signed_prekey_private.public_key());
        assert_eq!(bundle.onetime_prekeys().len(), onetime_count);
        assert_eq!(onetime_private_keys.len(), onetime_count);

        // Verify that onetime public keys match private keys
        for (i, private_key) in onetime_private_keys.iter().enumerate() {
            assert_eq!(bundle.onetime_prekeys()[i], private_key.public_key());
        }

        // Verify the signature
        let identity_signing_key = x25519_to_ed25519_signing_key(&identity_key.private_key())
            .expect("Failed to convert to signing key");
        bundle
            .verify_bundle_signature(&identity_signing_key)
            .expect("Failed to verify bundle signature");
    }

    #[test]
    fn test_prekey_bundle_signature_verification() {
        let identity_key = generate_identity_key().expect("Failed to generate identity key");
        let (bundle, _, _) =
            generate_prekey_bundle(&identity_key, 5).expect("Failed to generate prekey bundle");

        // Valid signature should verify
        let identity_signing_key = x25519_to_ed25519_signing_key(&identity_key.private_key())
            .expect("Failed to convert to signing key");
        bundle
            .verify_bundle_signature(&identity_signing_key)
            .expect("Valid signature should verify");

        // Create a bundle with invalid signature by using wrong signing key
        let wrong_identity_key =
            generate_identity_key().expect("Failed to generate wrong identity key");
        let wrong_signing_key = x25519_to_ed25519_signing_key(&wrong_identity_key.private_key())
            .expect("Failed to convert wrong key to signing key");

        // Invalid signature should fail
        assert!(bundle.verify_bundle_signature(&wrong_signing_key).is_err());
    }

    #[test]
    fn test_prekey_bundle_without_timestamp() {
        let identity_key = generate_identity_key().expect("Failed to generate identity key");
        let signed_prekey = generate_signed_prekey().expect("Failed to generate signed prekey");
        let onetime_prekeys =
            generate_onetime_prekeys(3).expect("Failed to generate onetime prekeys");

        let identity_signing_key = x25519_to_ed25519_signing_key(&identity_key.private_key())
            .expect("Failed to convert to signing key");
        let signature = sign_prekey(&identity_signing_key, &signed_prekey.public_key())
            .expect("Failed to sign prekey");

        let onetime_public_keys: Vec<PublicKey> =
            onetime_prekeys.iter().map(|kp| kp.public_key()).collect();

        let bundle = PreKeyBundle::new_without_timestamp(
            identity_key.public_key(),
            identity_signing_key.verifying_key.as_bytes().to_vec(),
            signed_prekey.public_key(),
            signature,
            onetime_public_keys,
        );

        assert!(bundle.timestamp().is_none());
        bundle
            .verify_bundle_signature(&identity_signing_key)
            .expect("Failed to verify bundle signature");
    }

    #[test]
    fn test_x25519_diffie_hellman() {
        let alice_keypair = KeyPair::generate().expect("Failed to generate Alice's keypair");
        let bob_keypair = KeyPair::generate().expect("Failed to generate Bob's keypair");

        // Compute shared secrets from both sides
        let alice_shared =
            x25519_diffie_hellman(&alice_keypair.private_key(), &bob_keypair.public_key())
                .expect("Failed to compute Alice's shared secret");
        let bob_shared =
            x25519_diffie_hellman(&bob_keypair.private_key(), &alice_keypair.public_key())
                .expect("Failed to compute Bob's shared secret");

        // They should be equal
        assert_eq!(alice_shared, bob_shared);

        // Should not be all zeros
        assert_ne!(alice_shared, [0u8; 32]);
    }

    #[test]
    fn test_x3dh_initiator_successful_signature_verification() {
        // Generate Alice's identity key (initiator)
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key (recipient)
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");

        // Generate Bob's pre-key bundle with proper signature
        let (bob_bundle, _bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 3).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Verify that the bundle signature is valid (this should succeed)
        bob_bundle
            .validate_signature(bob_verifying_key)
            .expect("Bundle signature should be valid");

        // Create X3DH parameters
        let params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle.clone(),
            onetime_prekey_index: Some(0),
        };

        // Perform X3DH as initiator - this should succeed because the signature is valid
        let result = x3dh_initiator(params, bob_verifying_key)
            .expect("X3DH should succeed with valid signature");

        // Verify results
        assert_ne!(result.shared_secret, [0u8; 32]);
        assert!(result.used_onetime_prekey.is_some());
        assert_eq!(
            result.used_onetime_prekey.unwrap(),
            *bob_bundle.onetime_prekeys().get(0).unwrap()
        );
    }

    #[test]
    fn test_x3dh_initiator_invalid_signature_failure() {
        // Generate Alice's identity key (initiator)
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key (recipient)
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");

        // Generate Bob's pre-key bundle with proper signature
        let (mut bob_bundle, _bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 3).expect("Failed to generate Bob's bundle");

        // Corrupt the signature with random bytes to make it invalid
        let invalid_signature = [0x42u8; 64]; // Random invalid signature
        bob_bundle.signed_prekey_signature = invalid_signature.to_vec();

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Create X3DH parameters
        let params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle.clone(),
            onetime_prekey_index: Some(0),
        };

        // Perform X3DH as initiator - this should fail due to invalid signature
        let result = x3dh_initiator(params, bob_verifying_key);

        // Verify that the function returns an error due to invalid signature
        assert!(result.is_err());
        match result.unwrap_err() {
            ProtocolError::InvalidPreKeySignature => {
                // This is the expected error type
            }
            other => panic!("Expected InvalidPreKeySignature error, got: {:?}", other),
        }
    }

    #[test]
    fn test_x3dh_initiator_wrong_identity_key_failure() {
        // Generate Alice's identity key (initiator)
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key (recipient)
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");

        // Generate a different identity key (Charlie's) to create a signature mismatch
        let charlie_identity =
            generate_identity_key().expect("Failed to generate Charlie's identity key");

        // Generate Bob's pre-key bundle with proper signature
        let (bob_bundle, _bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 3).expect("Failed to generate Bob's bundle");

        // Convert Charlie's identity private key to Ed25519 signing key, then get verifying key (wrong key for verification)
        let charlie_signing_key = x25519_to_ed25519_signing_key(&charlie_identity.private_key())
            .expect("Failed to convert Charlie's identity key to signing key");
        let charlie_verifying_key = &charlie_signing_key.verifying_key;

        // Create X3DH parameters
        let params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle.clone(),
            onetime_prekey_index: Some(0),
        };

        // Perform X3DH as initiator - this should fail because we're using Charlie's key
        // to verify Bob's signature
        let result = x3dh_initiator(params, charlie_verifying_key);

        // Verify that the function returns an error due to signature verification failure
        assert!(result.is_err());
        match result.unwrap_err() {
            ProtocolError::InvalidPreKeySignature => {
                // This is the expected error type
            }
            other => panic!("Expected InvalidPreKeySignature error, got: {:?}", other),
        }
    }

    #[test]
    fn test_x3dh_initiator_with_onetime_prekey() {
        // Generate Alice's identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key and bundle
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        let (bob_bundle, _bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 5).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Create X3DH parameters
        let params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle.clone(),
            onetime_prekey_index: Some(0),
        };

        // Perform X3DH as initiator
        let result =
            x3dh_initiator(params, bob_verifying_key).expect("Failed to perform X3DH as initiator");

        // Verify results
        assert_ne!(result.shared_secret, [0u8; 32]);
        assert!(result.used_onetime_prekey.is_some());
        assert_eq!(
            result.used_onetime_prekey.unwrap(),
            *bob_bundle.onetime_prekeys().get(0).unwrap()
        );
    }

    #[test]
    fn test_x3dh_initiator_without_onetime_prekey() {
        // Generate Alice's identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key and bundle with no one-time pre-keys
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        let (bob_bundle, _bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 0).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Create X3DH parameters
        let params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle,
            onetime_prekey_index: None,
        };

        // Perform X3DH as initiator
        let result =
            x3dh_initiator(params, bob_verifying_key).expect("Failed to perform X3DH as initiator");

        // Verify results
        assert_ne!(result.shared_secret, [0u8; 32]);
        assert!(result.used_onetime_prekey.is_none());
    }

    #[test]
    fn test_x3dh_initiator_deterministic_with_same_keys() {
        // Generate Alice's identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key and bundle
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        let (bob_bundle, _bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 3).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Create X3DH parameters
        let params1 = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle.clone(),
            onetime_prekey_index: Some(1),
        };

        let params2 = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle,
            onetime_prekey_index: Some(1),
        };

        // Perform X3DH twice
        let result1 = x3dh_initiator(params1, bob_verifying_key)
            .expect("Failed to perform X3DH as initiator (1)");
        let result2 = x3dh_initiator(params2, bob_verifying_key)
            .expect("Failed to perform X3DH as initiator (2)");

        // Results should be different due to different ephemeral keys
        assert_ne!(result1.shared_secret, result2.shared_secret);
        assert_ne!(
            result1.ephemeral_key.private_key().as_bytes(),
            result2.ephemeral_key.private_key().as_bytes()
        );

        // But the used one-time pre-key should be the same
        assert_eq!(result1.used_onetime_prekey, result2.used_onetime_prekey);
    }

    #[test]
    fn test_x3dh_initiator_different_onetime_prekeys() {
        // Generate Alice's identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key and bundle
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        let (bob_bundle, _bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 3).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Create X3DH parameters with different one-time pre-key indices
        let params1 = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle.clone(),
            onetime_prekey_index: Some(0),
        };

        let params2 = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle.clone(),
            onetime_prekey_index: Some(2),
        };

        // Perform X3DH with different one-time pre-keys
        let result1 =
            x3dh_initiator(params1, bob_verifying_key).expect("Failed to perform X3DH with OPK 0");
        let result2 =
            x3dh_initiator(params2, bob_verifying_key).expect("Failed to perform X3DH with OPK 2");

        // Results should be different
        assert_ne!(result1.shared_secret, result2.shared_secret);
        assert_ne!(result1.used_onetime_prekey, result2.used_onetime_prekey);

        // Verify the correct one-time pre-keys were used
        assert_eq!(
            result1.used_onetime_prekey.unwrap(),
            *bob_bundle.onetime_prekeys().get(0).unwrap()
        );
        assert_eq!(
            result2.used_onetime_prekey.unwrap(),
            *bob_bundle.onetime_prekeys().get(2).unwrap()
        );
    }

    #[test]
    fn test_x3dh_recipient_with_onetime_prekey() {
        // Generate Alice's identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key and bundle
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        let (bob_bundle, bob_signed_prekey, bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 3).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Alice performs X3DH as initiator
        let alice_params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle,
            onetime_prekey_index: Some(1),
        };

        let alice_result = x3dh_initiator(alice_params, bob_verifying_key)
            .expect("Failed to perform X3DH as initiator");

        // Create initial message for Bob
        let initial_message = create_initial_message(alice_identity.public_key(), &alice_result);

        // Bob performs X3DH as recipient
        let bob_params = X3dhRecipientParams {
            identity_key: bob_identity,
            signed_prekey: bob_signed_prekey,
            onetime_prekey: Some(bob_onetime_prekeys[1].clone()), // Use the same one-time pre-key
            initial_message,
        };

        let bob_result = x3dh_recipient(bob_params).expect("Failed to perform X3DH as recipient");

        // Verify that both parties derived the same shared secret
        assert_eq!(alice_result.shared_secret, bob_result.shared_secret);
        assert_ne!(alice_result.shared_secret, [0u8; 32]);
    }

    #[test]
    fn test_x3dh_recipient_without_onetime_prekey() {
        // Generate Alice's identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key and bundle with no one-time pre-keys
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        let (bob_bundle, bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 0).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Alice performs X3DH as initiator
        let alice_params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle,
            onetime_prekey_index: None,
        };

        let alice_result = x3dh_initiator(alice_params, bob_verifying_key)
            .expect("Failed to perform X3DH as initiator");

        // Create initial message for Bob
        let initial_message = create_initial_message(alice_identity.public_key(), &alice_result);

        // Bob performs X3DH as recipient
        let bob_params = X3dhRecipientParams {
            identity_key: bob_identity,
            signed_prekey: bob_signed_prekey,
            onetime_prekey: None, // No one-time pre-key used
            initial_message,
        };

        let bob_result = x3dh_recipient(bob_params).expect("Failed to perform X3DH as recipient");

        // Verify that both parties derived the same shared secret
        assert_eq!(alice_result.shared_secret, bob_result.shared_secret);
        assert_ne!(alice_result.shared_secret, [0u8; 32]);
        assert!(alice_result.used_onetime_prekey.is_none());
    }

    #[test]
    fn test_x3dh_recipient_missing_onetime_prekey() {
        // Generate Alice's identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key and bundle
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        let (bob_bundle, bob_signed_prekey, _bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 3).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Alice performs X3DH as initiator
        let alice_params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle,
            onetime_prekey_index: Some(0),
        };

        let alice_result = x3dh_initiator(alice_params, bob_verifying_key)
            .expect("Failed to perform X3DH as initiator");

        // Create initial message for Bob
        let initial_message = create_initial_message(alice_identity.public_key(), &alice_result);

        // Bob tries to perform X3DH without providing the one-time pre-key
        let bob_params = X3dhRecipientParams {
            identity_key: bob_identity,
            signed_prekey: bob_signed_prekey,
            onetime_prekey: None, // Missing the required one-time pre-key
            initial_message,
        };

        let result = x3dh_recipient(bob_params);
        assert!(result.is_err());
    }

    #[test]
    fn test_x3dh_recipient_wrong_onetime_prekey() {
        // Generate Alice's identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");

        // Generate Bob's identity key and bundle
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        let (bob_bundle, bob_signed_prekey, bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 3).expect("Failed to generate Bob's bundle");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Alice performs X3DH as initiator using one-time pre-key index 0
        let alice_params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: bob_bundle,
            onetime_prekey_index: Some(0),
        };

        let alice_result = x3dh_initiator(alice_params, bob_verifying_key)
            .expect("Failed to perform X3DH as initiator");

        // Create initial message for Bob
        let initial_message = create_initial_message(alice_identity.public_key(), &alice_result);

        // Bob tries to perform X3DH with the wrong one-time pre-key (index 1 instead of 0)
        let bob_params = X3dhRecipientParams {
            identity_key: bob_identity,
            signed_prekey: bob_signed_prekey,
            onetime_prekey: Some(bob_onetime_prekeys[1].clone()), // Wrong one-time pre-key
            initial_message,
        };

        let result = x3dh_recipient(bob_params);
        assert!(result.is_err());
    }

    #[test]
    fn test_x3dh_end_to_end_multiple_scenarios() {
        // Test multiple scenarios to ensure robustness
        for onetime_count in [0, 1, 5] {
            for use_onetime_index in [None, Some(0)] {
                if use_onetime_index.is_some() && onetime_count == 0 {
                    continue; // Skip invalid combination
                }

                // Generate Alice's identity key
                let alice_identity =
                    generate_identity_key().expect("Failed to generate Alice's identity key");

                // Generate Bob's identity key and bundle
                let bob_identity =
                    generate_identity_key().expect("Failed to generate Bob's identity key");
                let (bob_bundle, bob_signed_prekey, bob_onetime_prekeys) =
                    generate_prekey_bundle(&bob_identity, onetime_count)
                        .expect("Failed to generate Bob's bundle");

                // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
                let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
                    .expect("Failed to convert Bob's identity key to signing key");
                let bob_verifying_key = &bob_signing_key.verifying_key;

                // Alice performs X3DH as initiator
                let alice_params = X3dhParams {
                    identity_key: alice_identity.clone(),
                    recipient_bundle: bob_bundle,
                    onetime_prekey_index: use_onetime_index,
                };

                let alice_result = x3dh_initiator(alice_params, bob_verifying_key)
                    .expect("Failed to perform X3DH as initiator");

                // Create initial message for Bob
                let initial_message =
                    create_initial_message(alice_identity.public_key(), &alice_result);

                // Determine which one-time pre-key to use
                let onetime_prekey = if alice_result.used_onetime_prekey.is_some()
                    && !bob_onetime_prekeys.is_empty()
                {
                    Some(bob_onetime_prekeys[0].clone())
                } else {
                    None
                };

                // Bob performs X3DH as recipient
                let bob_params = X3dhRecipientParams {
                    identity_key: bob_identity,
                    signed_prekey: bob_signed_prekey,
                    onetime_prekey,
                    initial_message,
                };

                let bob_result =
                    x3dh_recipient(bob_params).expect("Failed to perform X3DH as recipient");

                // Verify that both parties derived the same shared secret
                assert_eq!(alice_result.shared_secret, bob_result.shared_secret);
                assert_ne!(alice_result.shared_secret, [0u8; 32]);
            }
        }
    }

    #[test]
    fn test_x3dh_integration_with_known_test_vectors() {
        // This test uses known test vectors to validate the X3DH implementation
        // Note: These are simplified test vectors for demonstration purposes

        // Create Alice's identity key from known test vector
        let alice_identity_private = PrivateKey::from_bytes([
            0x70, 0x07, 0x6d, 0x0a, 0x73, 0x18, 0xa5, 0x7d, 0x3c, 0x16, 0xc1, 0x72, 0x51, 0xb2,
            0x66, 0x45, 0xdf, 0x4c, 0x2f, 0x87, 0xeb, 0xc0, 0x99, 0x2a, 0xb1, 0x77, 0xfb, 0xa5,
            0x1d, 0xb9, 0x2c, 0x2a,
        ]);

        // Compute Alice's public key
        let alice_scalar = alice_identity_private.to_scalar();
        let alice_point = &alice_scalar * ED25519_BASEPOINT_TABLE;
        let alice_identity_public = PublicKey::from_bytes(alice_point.to_montgomery().to_bytes());
        let alice_identity = KeyPair::from_keys(alice_identity_private, alice_identity_public);

        // Create Bob's identity key from known test vector
        let bob_identity_private = PrivateKey::from_bytes([
            0x58, 0xab, 0x08, 0x7e, 0x62, 0x4a, 0x8a, 0x4b, 0x79, 0xe1, 0x7f, 0x8b, 0x83, 0x80,
            0x0e, 0xe6, 0x6f, 0x3b, 0xb1, 0x29, 0x26, 0x18, 0xb6, 0xfd, 0x1c, 0x2f, 0x8b, 0x27,
            0xff, 0x88, 0xe0, 0xeb,
        ]);

        let bob_scalar = bob_identity_private.to_scalar();
        let bob_point = &bob_scalar * ED25519_BASEPOINT_TABLE;
        let bob_identity_public = PublicKey::from_bytes(bob_point.to_montgomery().to_bytes());
        let bob_identity = KeyPair::from_keys(bob_identity_private, bob_identity_public);

        // Create Bob's signed pre-key from known test vector
        let bob_spk_private = PrivateKey::from_bytes([
            0x18, 0x39, 0x5c, 0x5e, 0x8a, 0x2d, 0x6b, 0x7a, 0x8b, 0x5c, 0x2a, 0x9c, 0x5e, 0x8a,
            0x2d, 0x6b, 0x7a, 0x8b, 0x5c, 0x2a, 0x9c, 0x5e, 0x8a, 0x2d, 0x6b, 0x7a, 0x8b, 0x5c,
            0x2a, 0x9c, 0x5e, 0x8a,
        ]);

        let bob_spk_scalar = bob_spk_private.to_scalar();
        let bob_spk_point = &bob_spk_scalar * ED25519_BASEPOINT_TABLE;
        let bob_spk_public = PublicKey::from_bytes(bob_spk_point.to_montgomery().to_bytes());
        let bob_signed_prekey = KeyPair::from_keys(bob_spk_private, bob_spk_public);

        // Create Bob's one-time pre-key from known test vector
        let bob_opk_private = PrivateKey::from_bytes([
            0xe0, 0xeb, 0x7a, 0x7c, 0x3b, 0x41, 0xb8, 0xae, 0x16, 0x56, 0xe3, 0xfa, 0xf1, 0x9f,
            0xc4, 0x6a, 0xda, 0x09, 0x8d, 0xeb, 0x9c, 0x32, 0xb1, 0xfd, 0x86, 0x62, 0x05, 0x16,
            0x5f, 0x49, 0xb8, 0x00,
        ]);

        let bob_opk_scalar = bob_opk_private.to_scalar();
        let bob_opk_point = &bob_opk_scalar * ED25519_BASEPOINT_TABLE;
        let bob_opk_public = PublicKey::from_bytes(bob_opk_point.to_montgomery().to_bytes());
        let bob_onetime_prekey = KeyPair::from_keys(bob_opk_private, bob_opk_public);

        // Create Bob's bundle
        let bob_identity_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let signature = sign_prekey(&bob_identity_signing_key, &bob_signed_prekey.public_key())
            .expect("Failed to sign Bob's pre-key");

        let bob_bundle = PreKeyBundle::new_without_timestamp(
            bob_identity.public_key(),
            bob_identity_signing_key.verifying_key.as_bytes().to_vec(),
            bob_signed_prekey.public_key(),
            signature,
            vec![bob_onetime_prekey.public_key()],
        );

        // Test serialization and deserialization of the bundle
        let serialized_bundle = bob_bundle.to_json().expect("Failed to serialize bundle");
        let deserialized_bundle =
            PreKeyBundle::from_json(&serialized_bundle).expect("Failed to deserialize bundle");

        // Verify bundle signature
        deserialized_bundle
            .verify_bundle_signature(&bob_identity_signing_key)
            .expect("Failed to verify deserialized bundle signature");

        // Convert Bob's identity private key to Ed25519 signing key, then get verifying key
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        // Alice performs X3DH as initiator
        let alice_params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: deserialized_bundle,
            onetime_prekey_index: Some(0),
        };

        let alice_result = x3dh_initiator(alice_params, bob_verifying_key)
            .expect("Failed to perform X3DH as initiator");

        // Create initial message for Bob
        let initial_message = create_initial_message(alice_identity.public_key(), &alice_result);

        // Bob performs X3DH as recipient
        let bob_params = X3dhRecipientParams {
            identity_key: bob_identity,
            signed_prekey: bob_signed_prekey,
            onetime_prekey: Some(bob_onetime_prekey.clone()),
            initial_message,
        };

        let bob_result = x3dh_recipient(bob_params).expect("Failed to perform X3DH as recipient");

        // Verify that both parties derived the same shared secret
        assert_eq!(alice_result.shared_secret, bob_result.shared_secret);
        assert_ne!(alice_result.shared_secret, [0u8; 32]);

        // Verify that the one-time pre-key was used
        assert!(alice_result.used_onetime_prekey.is_some());
        assert_eq!(
            alice_result.used_onetime_prekey.unwrap(),
            bob_onetime_prekey.public_key()
        );

        println!("✅ X3DH Integration Test Passed!");
        println!("   - Bundle serialization/deserialization: ✓");
        println!("   - Signature verification: ✓");
        println!("   - Initiator calculation: ✓");
        println!("   - Recipient calculation: ✓");
        println!("   - Shared secret agreement: ✓");
        println!("   - One-time pre-key usage: ✓");
    }

    #[test]
    fn test_x3dh_full_protocol_simulation() {
        // This test simulates the complete X3DH protocol flow as it would be used in practice

        println!("🔄 Starting Full X3DH Protocol Simulation...");

        // Step 1: Bob generates his long-term identity key
        let bob_identity = generate_identity_key().expect("Failed to generate Bob's identity key");
        println!("   1. Bob generated identity key");

        // Step 2: Bob generates and publishes a pre-key bundle
        let (bob_bundle, bob_signed_prekey, bob_onetime_prekeys) =
            generate_prekey_bundle(&bob_identity, 10).expect("Failed to generate Bob's bundle");
        println!("   2. Bob generated pre-key bundle with 10 one-time pre-keys");

        // Step 3: Bob serializes and "uploads" his bundle to a server (simulated)
        let serialized_bundle = bob_bundle.to_json().expect("Failed to serialize bundle");
        println!(
            "   3. Bob uploaded bundle to server (serialized: {} bytes)",
            serialized_bundle.len()
        );

        // Step 4: Alice generates her identity key
        let alice_identity =
            generate_identity_key().expect("Failed to generate Alice's identity key");
        println!("   4. Alice generated identity key");

        // Step 5: Alice "downloads" Bob's bundle from the server (simulated)
        let downloaded_bundle =
            PreKeyBundle::from_json(&serialized_bundle).expect("Failed to deserialize bundle");
        println!("   5. Alice downloaded Bob's bundle from server");

        // Step 6: Alice verifies Bob's bundle signature (using Bob's identity key)
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's key to signing key");
        downloaded_bundle
            .verify_bundle_signature(&bob_signing_key)
            .expect("Failed to verify bundle signature");
        println!("   6. Alice verified Bob's bundle signature");

        // Step 7: Alice performs X3DH to establish shared secret
        let bob_signing_key = x25519_to_ed25519_signing_key(&bob_identity.private_key())
            .expect("Failed to convert Bob's identity key to signing key");
        let bob_verifying_key = &bob_signing_key.verifying_key;

        let alice_params = X3dhParams {
            identity_key: alice_identity.clone(),
            recipient_bundle: downloaded_bundle,
            onetime_prekey_index: Some(3), // Use the 4th one-time pre-key
        };

        let alice_result = x3dh_initiator(alice_params, bob_verifying_key)
            .expect("Failed to perform X3DH as initiator");
        println!("   7. Alice calculated shared secret using X3DH");

        // Step 8: Alice sends initial message to Bob
        let initial_message = create_initial_message(alice_identity.public_key(), &alice_result);
        println!("   8. Alice created initial message for Bob");

        // Step 9: Bob receives the initial message and performs X3DH
        let bob_params = X3dhRecipientParams {
            identity_key: bob_identity,
            signed_prekey: bob_signed_prekey,
            onetime_prekey: Some(bob_onetime_prekeys[3].clone()), // Use the same one-time pre-key
            initial_message,
        };

        let bob_result = x3dh_recipient(bob_params).expect("Failed to perform X3DH as recipient");
        println!("   9. Bob calculated shared secret using X3DH");

        // Step 10: Verify the protocol worked correctly
        assert_eq!(alice_result.shared_secret, bob_result.shared_secret);
        assert_ne!(alice_result.shared_secret, [0u8; 32]);
        assert!(alice_result.used_onetime_prekey.is_some());
        assert_eq!(
            alice_result.used_onetime_prekey.unwrap(),
            bob_onetime_prekeys[3].public_key()
        );

        println!("  10. ✅ Protocol completed successfully!");
        println!(
            "      - Shared secret length: {} bytes",
            alice_result.shared_secret.len()
        );
        println!(
            "      - One-time pre-key consumed: {}",
            alice_result.used_onetime_prekey.is_some()
        );
        println!("      - Ephemeral key generated: ✓");

        // Step 11: Simulate Bob removing the used one-time pre-key
        println!("  11. Bob would now remove the used one-time pre-key from his bundle");

        println!("🎉 Full X3DH Protocol Simulation Completed Successfully!");
    }
}
