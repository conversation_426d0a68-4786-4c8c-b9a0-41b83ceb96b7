use crate::error::{<PERSON><PERSON>rror, Result};

use super::keys::{<PERSON><PERSON>ey, PublicKey};

/// Perform X25519 Diffie-<PERSON> key exchange
///
/// Computes the shared secret between a private key and a public key.
///
/// # Arguments
/// * `private_key()` - The private key for the calculation
/// * `public_key` - The public key for the calculation
///
/// # Returns
/// The 32-byte shared secret
/// Performs X25519 Elliptic Curve Di<PERSON><PERSON>-<PERSON> key exchange
///
/// This function computes the shared secret between a private key and a public key
/// using the X25519 elliptic curve. This is used internally by the Double Ratchet
/// algorithm for key derivation.
///
/// # Arguments
///
/// * `private_key` - The private key bytes (32 bytes)
/// * `public_key` - The public key bytes (32 bytes)
///
/// # Returns
///
/// Returns the 32-byte shared secret, or an error if the operation fails.
pub fn ecdh(private_key: &[u8; 32], public_key: &[u8]) -> Result<[u8; 32]> {
    if public_key.len() != 32 {
        return Err(ProtocolError::key_derivation_failure(
            "ECDH",
            "Invalid public key length",
        ));
    }

    let mut pub_key_bytes = [0u8; 32];
    pub_key_bytes.copy_from_slice(public_key);

    let private_key_obj = PrivateKey::from_bytes(*private_key);
    let public_key_obj = PublicKey::from_bytes(pub_key_bytes);

    x25519_diffie_hellman(&private_key_obj, &public_key_obj)
}

pub fn x25519_diffie_hellman(private_key: &PrivateKey, public_key: &PublicKey) -> Result<[u8; 32]> {
    let scalar = private_key.to_scalar();
    let point = public_key.to_montgomery();

    let shared_point = scalar * point;
    Ok(shared_point.to_bytes())
}
