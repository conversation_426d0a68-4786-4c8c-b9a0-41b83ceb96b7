
use ed25519_dalek::{Sign<PERSON>, Signer, Verifier, Veri<PERSON><PERSON><PERSON>};

use crate::error::{ProtocolError, Result};

use super::keys::{<PERSON><PERSON>ey, SigningKeyPair};

/// A 64-byte Ed25519 signature
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>q, Eq)]
pub struct Ed25519Signature([u8; 64]);

impl Ed25519Signature {
    /// Create an Ed25519Signature from a 64-byte array
    pub fn from_bytes(bytes: [u8; 64]) -> Self {
        Self(bytes)
    }

    /// Get the raw bytes of the signature
    pub fn as_bytes(&self) -> &[u8; 64] {
        &self.0
    }

    /// Convert to ed25519_dalek::Signature
    pub fn to_signature(&self) -> Result<Signature> {
        match Signature::try_from(&self.0[..]) {
            Ok(sig) => Ok(sig),
            Err(_) => Err(ProtocolError::invalid_signature(
                "Ed25519 signature conversion",
            )),
        }
    }
}

/// Sign a Signed Pre-key's public key using an Identity Key
///
/// This function creates an Ed25519 signature of the SPK's public key using the
/// Identity Key's private key. This signature proves ownership of the SPK.
///
/// # Arguments
/// * `identity_signing_key` - The signing key pair derived from the Identity Key
/// * `signed_prekey_public` - The public key of the Signed Pre-key to sign
///
/// # Returns
/// An Ed25519 signature of the SPK's public key
pub fn sign_prekey(
    identity_signing_key: &SigningKeyPair,
    signed_prekey_public: &PublicKey,
) -> Result<Ed25519Signature> {
    let signature = identity_signing_key
        .signing_key
        .sign(signed_prekey_public.as_bytes());
    Ok(Ed25519Signature::from_bytes(signature.to_bytes()))
}

/// Verify a Signed Pre-key signature
///
/// This function verifies that a signature was created by the holder of the
/// Identity Key's private key.
///
/// # Arguments
/// * `identity_verifying_key` - The verifying key from the Identity Key
/// * `signed_prekey_public` - The public key that was signed
/// * `signature` - The signature to verify
///
/// # Returns
/// Ok(()) if the signature is valid, or an error if verification fails
pub fn verify_prekey_signature(
    identity_verifying_key: &VerifyingKey,
    signed_prekey_public: &PublicKey,
    signature: &Ed25519Signature,
) -> Result<()> {
    let sig = signature.to_signature()?;
    identity_verifying_key
        .verify(signed_prekey_public.as_bytes(), &sig)
        .map_err(|_| ProtocolError::invalid_signature("Signed pre-key signature verification"))
}
