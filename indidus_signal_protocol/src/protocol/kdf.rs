use crate::error::{ProtocolError, Result};
use hkdf::Hkdf;
use sha2::Sha256;

/// Key derivation function for the root chain (DH ratchet step)
///
/// Takes a root key and the output of a <PERSON><PERSON><PERSON><PERSON> calculation,
/// and uses HKDF to produce a new root key and a new chain key.
///
/// # Arguments
/// * `rk` - The current root key (32 bytes)
/// * `dh_output` - The output of a DH calculation (32 bytes)
///
/// # Returns
/// A tuple containing (new_root_key, new_chain_key)
pub fn kdf_rk(rk: &[u8; 32], dh_output: &[u8; 32]) -> Result<([u8; 32], [u8; 32])> {
    // Use HKDF with the root key as salt and DH output as input key material
    let hkdf = Hkdf::<Sha256>::new(Some(rk), dh_output);

    // Derive 64 bytes total: 32 for new root key + 32 for new chain key
    let mut output = [0u8; 64];
    hkdf.expand(b"DOUBLE_RATCHET_ROOT_CHAIN", &mut output)
        .map_err(|_| {
            ProtocolError::key_derivation_failure("root chain KDF", "HKDF expansion failed")
        })?;

    // Split the output into root key and chain key
    let mut new_root_key = [0u8; 32];
    let mut new_chain_key = [0u8; 32];
    new_root_key.copy_from_slice(&output[0..32]);
    new_chain_key.copy_from_slice(&output[32..64]);

    Ok((new_root_key, new_chain_key))
}

/// Key derivation function for the message chains (symmetric ratchet step)
///
/// Takes a chain key and uses HKDF to produce a new chain key and a message key.
///
/// # Arguments
/// * `ck` - The current chain key (32 bytes)
///
/// # Returns
/// A tuple containing (new_chain_key, message_key)
#[allow(dead_code)]
pub(crate) fn kdf_ck(ck: &[u8; 32]) -> Result<([u8; 32], [u8; 32])> {
    // Use HKDF with no salt and the chain key as input key material
    let hkdf = Hkdf::<Sha256>::new(None, ck);

    // Derive 64 bytes total: 32 for new chain key + 32 for message key
    let mut output = [0u8; 64];
    hkdf.expand(b"DOUBLE_RATCHET_MESSAGE_CHAIN", &mut output)
        .map_err(|_| {
            ProtocolError::key_derivation_failure("message chain KDF", "HKDF expansion failed")
        })?;

    // Split the output into chain key and message key
    let mut new_chain_key = [0u8; 32];
    let mut message_key = [0u8; 32];
    new_chain_key.copy_from_slice(&output[0..32]);
    message_key.copy_from_slice(&output[32..64]);

    Ok((new_chain_key, message_key))
}
