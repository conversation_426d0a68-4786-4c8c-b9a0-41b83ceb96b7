use crate::crypto::keys::{Private<PERSON>ey, PublicKey};
use crate::error::Result;

/// Perform X25519 Diffie-<PERSON> key exchange
///
/// Computes the shared secret between a private key and a public key.
/// This is a local implementation since the crypto module's function is private.
pub fn x25519_diffie_hellman(private_key: &PrivateKey, public_key: &PublicKey) -> Result<[u8; 32]> {
    let scalar = private_key.to_scalar();
    let point = public_key.to_montgomery();

    let shared_point = scalar * point;
    Ok(shared_point.to_bytes())
}
