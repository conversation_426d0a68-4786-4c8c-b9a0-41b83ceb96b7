use super::*;
use crate::crypto::signature::Ed25519Signature;
use crate::crypto::{
    bundle::PreKeyBundle,
    keys::{KeyPair, PublicKey},
};
use crate::session::Session;

// Helper function to create a dummy PreKeyBundle for testing
fn create_test_bundle(public_key: PublicKey) -> PreKeyBundle {
    let dummy_signature = Ed25519Signature::from_bytes([0u8; 64]);
    PreKeyBundle::new_without_timestamp(
        public_key,
        vec![0u8; 32],
        public_key,
        dummy_signature,
        vec![],
    )
}

#[test]
fn test_session_creation() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let session = Session::new(root_key, dh_key.clone(), Some(remote_key), 1000);

    assert_eq!(session.root_key, root_key);
    assert_eq!(session.get_dh_public_key(), dh_key.public_key());
    assert_eq!(session.remote_dh_public_key, Some(remote_key));
    assert_eq!(session.sending_message_number, 0);
    assert_eq!(session.receiving_message_number, 0);
    assert_eq!(session.previous_chain_length, 0);
    assert_eq!(session.max_skip, 1000);
    assert!(!session.can_send());
    assert!(!session.can_receive());
    assert_eq!(session.skipped_keys_count(), 0);
}

#[test]
fn test_session_serialization() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let mut session = Session::new(root_key, dh_key, Some(remote_key), 500);
    session.sending_message_number = 5;
    session.receiving_message_number = 3;
    session.previous_chain_length = 10;

    // Test JSON serialization
    let json = session.to_json().expect("Failed to serialize to JSON");
    let deserialized = Session::from_json(&json).expect("Failed to deserialize from JSON");

    assert_eq!(session.root_key, deserialized.root_key);
    assert_eq!(
        session.dh_ratchet_key.public_key().as_bytes(),
        deserialized.dh_ratchet_key.public_key().as_bytes()
    );
    assert_eq!(
        session.dh_ratchet_key.private_key().as_bytes(),
        deserialized.dh_ratchet_key.private_key().as_bytes()
    );
    assert_eq!(
        session.remote_dh_public_key,
        deserialized.remote_dh_public_key
    );
    assert_eq!(
        session.sending_message_number,
        deserialized.sending_message_number
    );
    assert_eq!(
        session.receiving_message_number,
        deserialized.receiving_message_number
    );
    assert_eq!(
        session.previous_chain_length,
        deserialized.previous_chain_length
    );
    assert_eq!(session.max_skip, deserialized.max_skip);

    // Test bytes serialization
    let bytes = session.to_bytes().expect("Failed to serialize to bytes");
    let deserialized2 = Session::from_bytes(&bytes).expect("Failed to deserialize from bytes");

    assert_eq!(session.root_key, deserialized2.root_key);
    assert_eq!(
        session.sending_message_number,
        deserialized2.sending_message_number
    );
}

#[test]
fn test_message_header_creation() {
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let header = MessageHeader::new(dh_key.public_key(), 5, 10);

    assert_eq!(header.dh_public_key, dh_key.public_key());
    assert_eq!(header.previous_chain_length, 5);
    assert_eq!(header.message_number, 10);
}

#[test]
fn test_message_header_serialization() {
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let header = MessageHeader::new(dh_key.public_key(), 7, 15);

    // Test JSON serialization
    let json = header.to_json().expect("Failed to serialize to JSON");
    let deserialized = MessageHeader::from_json(&json).expect("Failed to deserialize from JSON");

    assert_eq!(header, deserialized);

    // Test bytes serialization
    let bytes = header.to_bytes().expect("Failed to serialize to bytes");
    let deserialized2 =
        MessageHeader::from_bytes(&bytes).expect("Failed to deserialize from bytes");

    assert_eq!(header, deserialized2);
}

#[test]
fn test_message_header_skip_key() {
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let header = MessageHeader::new(dh_key.public_key(), 3, 8);

    let skip_key = header.get_skip_key();
    assert_eq!(skip_key.0, dh_key.public_key().as_bytes().to_vec());
    assert_eq!(skip_key.1, 8);
}

#[test]
fn test_session_skipped_keys_management() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let mut session = Session::new(root_key, dh_key, None, 2);

    // Add some skipped keys
    let key1 = (vec![1, 2, 3], 1);
    let key2 = (vec![4, 5, 6], 2);
    let key3 = (vec![7, 8, 9], 3);

    session.skipped_message_keys.insert(key1, [1u8; 32]);
    session.skipped_message_keys.insert(key2, [2u8; 32]);

    assert_eq!(session.skipped_keys_count(), 2);
    assert!(session.is_at_max_skip());

    // Adding one more should trigger cleanup
    session.skipped_message_keys.insert(key3, [3u8; 32]);
    assert_eq!(session.skipped_keys_count(), 3);

    session.clear_skipped_keys();
    assert_eq!(session.skipped_keys_count(), 0);
}

#[test]
fn test_session_state_queries() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let mut session = Session::new(root_key, dh_key, None, 1000);

    // Initially can't send or receive
    assert!(!session.can_send());
    assert!(!session.can_receive());

    // Set chain keys
    session.sending_chain_key = Some([1u8; 32]);
    session.receiving_chain_key = Some([2u8; 32]);

    // Now can send and receive
    assert!(session.can_send());
    assert!(session.can_receive());
}

#[test]
fn test_session_with_no_remote_key() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let session = Session::new(root_key, dh_key, None, 1000);

    assert_eq!(session.remote_dh_public_key, None);
    assert!(!session.can_send());
    assert!(!session.can_receive());
}

#[test]
fn test_kdf_rk_basic() {
    let root_key = [1u8; 32];
    let dh_output = [2u8; 32];

    let result = kdf_rk(&root_key, &dh_output).expect("KDF RK should succeed");
    let (new_root_key, new_chain_key) = result;

    // Keys should not be all zeros
    assert_ne!(new_root_key, [0u8; 32]);
    assert_ne!(new_chain_key, [0u8; 32]);

    // Keys should be different from inputs
    assert_ne!(new_root_key, root_key);
    assert_ne!(new_chain_key, root_key);
    assert_ne!(new_root_key, dh_output);
    assert_ne!(new_chain_key, dh_output);

    // Root key and chain key should be different
    assert_ne!(new_root_key, new_chain_key);
}

#[test]
fn test_kdf_rk_deterministic() {
    let root_key = [42u8; 32];
    let dh_output = [84u8; 32];

    // Same inputs should produce same outputs
    let result1 = kdf_rk(&root_key, &dh_output).expect("KDF RK should succeed");
    let result2 = kdf_rk(&root_key, &dh_output).expect("KDF RK should succeed");

    assert_eq!(result1.0, result2.0); // Same root key
    assert_eq!(result1.1, result2.1); // Same chain key
}

#[test]
fn test_kdf_rk_different_inputs() {
    let root_key1 = [1u8; 32];
    let root_key2 = [2u8; 32];
    let dh_output = [3u8; 32];

    let result1 = kdf_rk(&root_key1, &dh_output).expect("KDF RK should succeed");
    let result2 = kdf_rk(&root_key2, &dh_output).expect("KDF RK should succeed");

    // Different root keys should produce different outputs
    assert_ne!(result1.0, result2.0);
    assert_ne!(result1.1, result2.1);

    let dh_output1 = [4u8; 32];
    let dh_output2 = [5u8; 32];

    let result3 = kdf_rk(&root_key1, &dh_output1).expect("KDF RK should succeed");
    let result4 = kdf_rk(&root_key1, &dh_output2).expect("KDF RK should succeed");

    // Different DH outputs should produce different outputs
    assert_ne!(result3.0, result4.0);
    assert_ne!(result3.1, result4.1);
}

#[test]
fn test_kdf_ck_basic() {
    let chain_key = [10u8; 32];

    let result = kdf_ck(&chain_key).expect("KDF CK should succeed");
    let (new_chain_key, message_key) = result;

    // Keys should not be all zeros
    assert_ne!(new_chain_key, [0u8; 32]);
    assert_ne!(message_key, [0u8; 32]);

    // Keys should be different from input
    assert_ne!(new_chain_key, chain_key);
    assert_ne!(message_key, chain_key);

    // Chain key and message key should be different
    assert_ne!(new_chain_key, message_key);
}

#[test]
fn test_kdf_ck_deterministic() {
    let chain_key = [123u8; 32];

    // Same input should produce same outputs
    let result1 = kdf_ck(&chain_key).expect("KDF CK should succeed");
    let result2 = kdf_ck(&chain_key).expect("KDF CK should succeed");

    assert_eq!(result1.0, result2.0); // Same chain key
    assert_eq!(result1.1, result2.1); // Same message key
}

#[test]
fn test_kdf_ck_different_inputs() {
    let chain_key1 = [20u8; 32];
    let chain_key2 = [21u8; 32];

    let result1 = kdf_ck(&chain_key1).expect("KDF CK should succeed");
    let result2 = kdf_ck(&chain_key2).expect("KDF CK should succeed");

    // Different inputs should produce different outputs
    assert_ne!(result1.0, result2.0);
    assert_ne!(result1.1, result2.1);
}

#[test]
fn test_kdf_ck_chain_progression() {
    let initial_chain_key = [50u8; 32];

    // Simulate chain key progression
    let (chain_key_1, message_key_1) = kdf_ck(&initial_chain_key).expect("KDF CK should succeed");
    let (chain_key_2, message_key_2) = kdf_ck(&chain_key_1).expect("KDF CK should succeed");
    let (chain_key_3, message_key_3) = kdf_ck(&chain_key_2).expect("KDF CK should succeed");

    // All chain keys should be different
    assert_ne!(initial_chain_key, chain_key_1);
    assert_ne!(chain_key_1, chain_key_2);
    assert_ne!(chain_key_2, chain_key_3);
    assert_ne!(initial_chain_key, chain_key_2);
    assert_ne!(initial_chain_key, chain_key_3);
    assert_ne!(chain_key_1, chain_key_3);

    // All message keys should be different
    assert_ne!(message_key_1, message_key_2);
    assert_ne!(message_key_2, message_key_3);
    assert_ne!(message_key_1, message_key_3);

    // Message keys should be different from chain keys
    assert_ne!(message_key_1, initial_chain_key);
    assert_ne!(message_key_1, chain_key_1);
    assert_ne!(message_key_2, chain_key_1);
    assert_ne!(message_key_2, chain_key_2);
    assert_ne!(message_key_3, chain_key_2);
    assert_ne!(message_key_3, chain_key_3);
}

#[test]
fn test_kdf_domain_separation() {
    // Test that the two KDFs produce different outputs for the same input
    let input = [100u8; 32];

    // Use the same input for both KDFs
    let (rk_out1, rk_out2) = kdf_rk(&input, &input).expect("KDF RK should succeed");
    let (ck_out1, ck_out2) = kdf_ck(&input).expect("KDF CK should succeed");

    // The outputs should be different due to different info parameters
    assert_ne!(rk_out1, ck_out1);
    assert_ne!(rk_out1, ck_out2);
    assert_ne!(rk_out2, ck_out1);
    assert_ne!(rk_out2, ck_out2);
}

#[test]
fn test_kdf_with_known_test_vectors() {
    // Test with known inputs to ensure consistency
    let root_key = [
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
        0x1f, 0x20,
    ];

    let dh_output = [
        0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f,
        0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e,
        0x3f, 0x40,
    ];

    let (new_root_key, new_chain_key) =
        kdf_rk(&root_key, &dh_output).expect("KDF RK should succeed");

    // These values should be consistent across runs
    assert_eq!(new_root_key.len(), 32);
    assert_eq!(new_chain_key.len(), 32);
    assert_ne!(new_root_key, [0u8; 32]);
    assert_ne!(new_chain_key, [0u8; 32]);

    // Test chain key derivation with known input
    let chain_key = [
        0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f,
        0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e,
        0x5f, 0x60,
    ];

    let (new_chain_key_ck, message_key) = kdf_ck(&chain_key).expect("KDF CK should succeed");

    assert_eq!(new_chain_key_ck.len(), 32);
    assert_eq!(message_key.len(), 32);
    assert_ne!(new_chain_key_ck, [0u8; 32]);
    assert_ne!(message_key, [0u8; 32]);
    assert_ne!(new_chain_key_ck, message_key);
}

#[test]
fn test_session_new_initiator() {
    let x3dh_secret = [42u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let bundle = create_test_bundle(their_dh_key.public_key());
    let session = Session::new_initiator(x3dh_secret, &bundle, 1000, None)
        .expect("Failed to create initiator session");

    // Initiator should have a sending chain but no receiving chain initially
    assert!(session.can_send());
    assert!(!session.can_receive());

    // Should have their DH key set
    assert_eq!(
        session.remote_dh_public_key,
        Some(their_dh_key.public_key())
    );

    // Message numbers should be initialized to 0
    assert_eq!(session.sending_message_number, 0);
    assert_eq!(session.receiving_message_number, 0);
    assert_eq!(session.previous_chain_length, 0);

    // Root key should be derived (different from X3DH secret)
    assert_ne!(session.root_key, x3dh_secret);

    // Should have max_skip set correctly
    assert_eq!(session.max_skip, 1000);

    // Should have no skipped keys initially
    assert_eq!(session.skipped_keys_count(), 0);
}

#[test]
fn test_session_new_responder() {
    let x3dh_secret = [84u8; 32];
    let our_dh_key = KeyPair::generate().expect("Failed to generate our DH key");

    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");
    let session = Session::new_responder(
        x3dh_secret,
        our_dh_key.clone(),
        their_dh_key.public_key(),
        500,
        None,
    )
    .expect("Failed to create responder session");

    // Responder should not be able to send or receive initially
    assert!(!session.can_send());
    assert!(!session.can_receive());

    // Should not have remote DH key set initially
    assert_eq!(session.remote_dh_public_key, None);

    // Should have our DH key set
    assert_eq!(session.dh_ratchet_key.public_key(), our_dh_key.public_key());
    assert_eq!(
        session.dh_ratchet_key.private_key().as_bytes(),
        our_dh_key.private_key().as_bytes()
    );

    // Message numbers should be initialized to 0
    assert_eq!(session.sending_message_number, 0);
    assert_eq!(session.receiving_message_number, 0);
    assert_eq!(session.previous_chain_length, 0);

    // Root key should be the X3DH secret
    assert_eq!(session.root_key, x3dh_secret);

    // Should have max_skip set correctly
    assert_eq!(session.max_skip, 500);

    // Should have no skipped keys initially
    assert_eq!(session.skipped_keys_count(), 0);
}

#[test]
fn test_session_initialization_deterministic() {
    let x3dh_secret = [123u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    // Same inputs should produce sessions with same root key and sending chain key
    // (but different DH ratchet keys due to random generation)
    let bundle = create_test_bundle(their_dh_key.public_key());
    let session1 = Session::new_initiator(x3dh_secret, &bundle, 1000, None)
        .expect("Failed to create initiator session 1");
    let session2 = Session::new_initiator(x3dh_secret, &bundle, 1000, None)
        .expect("Failed to create initiator session 2");

    // DH ratchet keys should be different (randomly generated)
    assert_ne!(
        session1.dh_ratchet_key.private_key().as_bytes(),
        session2.dh_ratchet_key.private_key().as_bytes()
    );
    assert_ne!(
        session1.dh_ratchet_key.public_key().as_bytes(),
        session2.dh_ratchet_key.public_key().as_bytes()
    );

    // But other properties should be the same
    assert_eq!(session1.remote_dh_public_key, session2.remote_dh_public_key);
    assert_eq!(session1.max_skip, session2.max_skip);
    assert_eq!(
        session1.sending_message_number,
        session2.sending_message_number
    );
    assert_eq!(
        session1.receiving_message_number,
        session2.receiving_message_number
    );
}

#[test]
fn test_session_initialization_different_secrets() {
    let x3dh_secret1 = [111u8; 32];
    let x3dh_secret2 = [222u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let bundle = create_test_bundle(their_dh_key.public_key());
    let session1 = Session::new_initiator(x3dh_secret1, &bundle, 1000, None)
        .expect("Failed to create initiator session 1");
    let session2 = Session::new_initiator(x3dh_secret2, &bundle, 1000, None)
        .expect("Failed to create initiator session 2");

    // Different X3DH secrets should produce different root keys and sending chain keys
    assert_ne!(session1.root_key, session2.root_key);
    assert_ne!(session1.sending_chain_key, session2.sending_chain_key);
}

#[test]
fn test_session_initialization_different_dh_keys() {
    let x3dh_secret = [200u8; 32];
    let their_dh_key1 = KeyPair::generate().expect("Failed to generate their DH key 1");
    let their_dh_key2 = KeyPair::generate().expect("Failed to generate their DH key 2");

    let bundle1 = create_test_bundle(their_dh_key1.public_key());
    let session1 = Session::new_initiator(x3dh_secret, &bundle1, 1000, None)
        .expect("Failed to create initiator session 1");
    let bundle2 = create_test_bundle(their_dh_key2.public_key());
    let session2 = Session::new_initiator(x3dh_secret, &bundle2, 1000, None)
        .expect("Failed to create initiator session 2");

    // Different DH keys should produce different root keys and sending chain keys
    assert_ne!(session1.root_key, session2.root_key);
    assert_ne!(session1.sending_chain_key, session2.sending_chain_key);
    assert_ne!(session1.remote_dh_public_key, session2.remote_dh_public_key);
}

#[test]
fn test_x25519_diffie_hellman_local() {
    let alice_keypair = KeyPair::generate().expect("Failed to generate Alice's keypair");
    let bob_keypair = KeyPair::generate().expect("Failed to generate Bob's keypair");

    // Compute shared secrets from both sides
    let alice_shared =
        x25519_diffie_hellman(&alice_keypair.private_key(), &bob_keypair.public_key())
            .expect("Failed to compute Alice's shared secret");
    let bob_shared = x25519_diffie_hellman(&bob_keypair.private_key(), &alice_keypair.public_key())
        .expect("Failed to compute Bob's shared secret");

    // They should be equal
    assert_eq!(alice_shared, bob_shared);

    // Should not be all zeros
    assert_ne!(alice_shared, [0u8; 32]);
}

#[test]
fn test_session_responder_serialization() {
    let x3dh_secret = [99u8; 32];
    let our_dh_key = KeyPair::generate().expect("Failed to generate our DH key");

    let their_identity_key = KeyPair::generate().expect("Failed to generate their identity key");
    let session = Session::new_responder(
        x3dh_secret,
        our_dh_key,
        their_identity_key.public_key(),
        750,
        None,
    )
    .expect("Failed to create responder session");

    // Test serialization
    let json = session.to_json().expect("Failed to serialize session");
    let deserialized = Session::from_json(&json).expect("Failed to deserialize session");

    assert_eq!(session.root_key, deserialized.root_key);
    assert_eq!(
        session.dh_ratchet_key.public_key().as_bytes(),
        deserialized.dh_ratchet_key.public_key().as_bytes()
    );
    assert_eq!(
        session.dh_ratchet_key.private_key().as_bytes(),
        deserialized.dh_ratchet_key.private_key().as_bytes()
    );
    assert_eq!(
        session.remote_dh_public_key,
        deserialized.remote_dh_public_key
    );
    assert_eq!(session.sending_chain_key, deserialized.sending_chain_key);
    assert_eq!(
        session.receiving_chain_key,
        deserialized.receiving_chain_key
    );
    assert_eq!(session.max_skip, deserialized.max_skip);
}

#[test]
fn test_session_initiator_serialization() {
    let x3dh_secret = [77u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let bundle = create_test_bundle(their_dh_key.public_key());
    let session = Session::new_initiator(x3dh_secret, &bundle, 250, None)
        .expect("Failed to create initiator session");

    // Test serialization
    let bytes = session.to_bytes().expect("Failed to serialize session");
    let deserialized = Session::from_bytes(&bytes).expect("Failed to deserialize session");

    assert_eq!(session.root_key, deserialized.root_key);
    assert_eq!(
        session.remote_dh_public_key,
        deserialized.remote_dh_public_key
    );
    assert_eq!(session.sending_chain_key, deserialized.sending_chain_key);
    assert_eq!(
        session.receiving_chain_key,
        deserialized.receiving_chain_key
    );
    assert_eq!(session.max_skip, deserialized.max_skip);
}

#[test]
fn test_session_encrypt_basic() {
    let x3dh_secret = [55u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let bundle = create_test_bundle(their_dh_key.public_key());
    let mut session = Session::new_initiator(x3dh_secret, &bundle, 1000, None)
        .expect("Failed to create initiator session");

    let plaintext = b"Hello, Double Ratchet!";
    let (header, ciphertext) = session
        .encrypt(plaintext)
        .expect("Failed to encrypt message");

    // Header should contain correct information
    assert_eq!(header.dh_public_key, session.get_dh_public_key());
    assert_eq!(header.previous_chain_length, 0);
    assert_eq!(header.message_number, 0);

    // Ciphertext should be longer than plaintext (includes nonce + auth tag)
    assert!(ciphertext.len() > plaintext.len());

    // Session state should be updated
    assert_eq!(session.sending_message_number, 1);
    assert!(session.can_send());
}

#[test]
fn test_session_encrypt_multiple_messages() {
    let x3dh_secret = [66u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let bundle = create_test_bundle(their_dh_key.public_key());
    let mut session = Session::new_initiator(x3dh_secret, &bundle, 1000, None)
        .expect("Failed to create initiator session");

    let messages = [
        &b"First message"[..],
        &b"Second message"[..],
        &b"Third message"[..],
    ];

    let mut headers = Vec::new();
    let mut ciphertexts = Vec::new();

    for (i, message) in messages.iter().enumerate() {
        let (header, ciphertext) = session.encrypt(message).expect("Failed to encrypt message");

        // Each message should have incrementing message number
        assert_eq!(header.message_number, i as u32);
        assert_eq!(header.previous_chain_length, 0);
        assert_eq!(header.dh_public_key, session.get_dh_public_key());

        headers.push(header);
        ciphertexts.push(ciphertext);
    }

    // All ciphertexts should be different
    assert_ne!(ciphertexts[0], ciphertexts[1]);
    assert_ne!(ciphertexts[1], ciphertexts[2]);
    assert_ne!(ciphertexts[0], ciphertexts[2]);

    // Session message number should be updated
    assert_eq!(session.sending_message_number, 3);
}

#[test]
fn test_session_encrypt_responder_fails() {
    let x3dh_secret = [77u8; 32];
    let our_dh_key = KeyPair::generate().expect("Failed to generate our DH key");

    let mut session = Session::new_responder(
        x3dh_secret,
        our_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create responder session");

    let plaintext = b"This should fail";
    let result = session.encrypt(plaintext);

    // Responder cannot encrypt without a sending chain key
    assert!(result.is_err());
}

#[test]
fn test_session_encrypt_deterministic_with_same_key() {
    let x3dh_secret = [88u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let bundle = create_test_bundle(their_dh_key.public_key());
    let mut session1 = Session::new_initiator(x3dh_secret, &bundle, 1000, None)
        .expect("Failed to create initiator session 1");
    let mut session2 = Session::new_initiator(x3dh_secret, &bundle, 1000, None)
        .expect("Failed to create initiator session 2");

    let plaintext = b"Same message";

    let (header1, ciphertext1) = session1
        .encrypt(plaintext)
        .expect("Failed to encrypt with session 1");
    let (header2, ciphertext2) = session2
        .encrypt(plaintext)
        .expect("Failed to encrypt with session 2");

    // Headers should have same message number but different DH keys (due to random generation)
    assert_eq!(header1.message_number, header2.message_number);
    assert_eq!(header1.previous_chain_length, header2.previous_chain_length);
    assert_ne!(header1.dh_public_key, header2.dh_public_key);

    // Ciphertexts should be different (different keys and nonces)
    assert_ne!(ciphertext1, ciphertext2);
}

#[test]
fn test_session_encrypt_empty_message() {
    let x3dh_secret = [99u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let mut session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(their_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create initiator session");

    let plaintext = b"";
    let (header, ciphertext) = session
        .encrypt(plaintext)
        .expect("Failed to encrypt empty message");

    // Should still work with empty message
    assert_eq!(header.message_number, 0);
    assert!(ciphertext.len() > 0); // Still has nonce + auth tag
    assert_eq!(session.sending_message_number, 1);
}

#[test]
fn test_session_encrypt_large_message() {
    let x3dh_secret = [111u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let mut session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(their_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create initiator session");

    // Create a large message (1MB)
    let plaintext = vec![0x42u8; 1024 * 1024];
    let (header, ciphertext) = session
        .encrypt(&plaintext)
        .expect("Failed to encrypt large message");

    // Should handle large messages
    assert_eq!(header.message_number, 0);
    assert!(ciphertext.len() > plaintext.len());
    assert_eq!(session.sending_message_number, 1);
}

#[test]
fn test_session_encrypt_chain_key_advancement() {
    let x3dh_secret = [122u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let mut session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(their_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create initiator session");

    // Store initial chain key
    let initial_chain_key = session.sending_chain_key.unwrap();

    let plaintext = b"Test message";
    let (_, _) = session
        .encrypt(plaintext)
        .expect("Failed to encrypt message");

    // Chain key should have advanced
    let new_chain_key = session.sending_chain_key.unwrap();
    assert_ne!(initial_chain_key, new_chain_key);

    // Encrypt another message
    let (_, _) = session
        .encrypt(plaintext)
        .expect("Failed to encrypt second message");

    // Chain key should have advanced again
    let newer_chain_key = session.sending_chain_key.unwrap();
    assert_ne!(new_chain_key, newer_chain_key);
    assert_ne!(initial_chain_key, newer_chain_key);
}

#[test]
fn test_session_encrypt_nonce_uniqueness() {
    let x3dh_secret = [133u8; 32];
    let their_dh_key = KeyPair::generate().expect("Failed to generate their DH key");

    let mut session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(their_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create initiator session");

    let plaintext = b"Same plaintext";
    let mut nonces = Vec::new();

    // Encrypt the same message multiple times
    for _ in 0..10 {
        let (_, ciphertext) = session
            .encrypt(plaintext)
            .expect("Failed to encrypt message");

        // Extract nonce (first 12 bytes for AES-GCM)
        let nonce = &ciphertext[0..12];
        nonces.push(nonce.to_vec());
    }

    // All nonces should be unique
    for i in 0..nonces.len() {
        for j in (i + 1)..nonces.len() {
            assert_ne!(nonces[i], nonces[j], "Nonces should be unique");
        }
    }
}

#[test]
fn test_session_encrypt_decrypt_basic() {
    let x3dh_secret = [200u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    // Alice is the initiator
    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");

    // Bob is the responder
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    let plaintext = b"Hello from Alice to Bob!";

    // Alice encrypts a message
    let (header, ciphertext) = alice_session
        .encrypt(plaintext)
        .expect("Failed to encrypt message");

    // Bob decrypts the message
    let decrypted = bob_session
        .decrypt(&header, &ciphertext)
        .expect("Failed to decrypt message");

    assert_eq!(plaintext.as_slice(), decrypted.as_slice());

    // Bob should now be able to send messages back
    assert!(bob_session.can_send());
    assert!(bob_session.can_receive());
}

#[test]
fn test_session_bidirectional_communication() {
    let x3dh_secret = [210u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Alice sends first message
    let alice_msg1 = b"Hello Bob!";
    let (header1, ciphertext1) = alice_session
        .encrypt(alice_msg1)
        .expect("Failed to encrypt Alice's message");
    let decrypted1 = bob_session
        .decrypt(&header1, &ciphertext1)
        .expect("Failed to decrypt Alice's message");
    assert_eq!(alice_msg1.as_slice(), decrypted1.as_slice());

    // Bob responds
    let bob_msg1 = b"Hello Alice!";
    let (header2, ciphertext2) = bob_session
        .encrypt(bob_msg1)
        .expect("Failed to encrypt Bob's message");
    let decrypted2 = alice_session
        .decrypt(&header2, &ciphertext2)
        .expect("Failed to decrypt Bob's message");
    assert_eq!(bob_msg1.as_slice(), decrypted2.as_slice());

    // Alice sends another message
    let alice_msg2 = b"How are you?";
    let (header3, ciphertext3) = alice_session
        .encrypt(alice_msg2)
        .expect("Failed to encrypt Alice's second message");
    let decrypted3 = bob_session
        .decrypt(&header3, &ciphertext3)
        .expect("Failed to decrypt Alice's second message");
    assert_eq!(alice_msg2.as_slice(), decrypted3.as_slice());
}

#[test]
fn test_session_out_of_order_messages() {
    let x3dh_secret = [220u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Alice encrypts multiple messages
    let msg1 = b"Message 1";
    let msg2 = b"Message 2";
    let msg3 = b"Message 3";

    let (header1, ciphertext1) = alice_session
        .encrypt(msg1)
        .expect("Failed to encrypt message 1");
    let (header2, ciphertext2) = alice_session
        .encrypt(msg2)
        .expect("Failed to encrypt message 2");
    let (header3, ciphertext3) = alice_session
        .encrypt(msg3)
        .expect("Failed to encrypt message 3");

    // Bob receives messages out of order: 3, 1, 2
    let decrypted3 = bob_session
        .decrypt(&header3, &ciphertext3)
        .expect("Failed to decrypt message 3");
    assert_eq!(msg3.as_slice(), decrypted3.as_slice());

    let decrypted1 = bob_session
        .decrypt(&header1, &ciphertext1)
        .expect("Failed to decrypt message 1");
    assert_eq!(msg1.as_slice(), decrypted1.as_slice());

    let decrypted2 = bob_session
        .decrypt(&header2, &ciphertext2)
        .expect("Failed to decrypt message 2");
    assert_eq!(msg2.as_slice(), decrypted2.as_slice());
}

#[test]
fn test_session_skipped_message_limits() {
    let x3dh_secret = [230u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    // Set a low max_skip limit for testing
    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        5,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        5,
        None,
    )
    .expect("Failed to create Bob's session");

    // Alice encrypts many messages
    let mut headers = Vec::new();
    let mut ciphertexts = Vec::new();

    for i in 0..10 {
        let msg = format!("Message {}", i);
        let (header, ciphertext) = alice_session
            .encrypt(msg.as_bytes())
            .expect("Failed to encrypt message");
        headers.push(header);
        ciphertexts.push(ciphertext);
    }

    // Bob tries to decrypt a message that's too far ahead
    let result = bob_session.decrypt(&headers[9], &ciphertexts[9]);
    assert!(result.is_err()); // Should fail due to too many skipped messages
}

#[test]
fn test_session_decrypt_invalid_ciphertext() {
    let x3dh_secret = [240u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    let plaintext = b"Valid message";
    let (header, mut ciphertext) = alice_session
        .encrypt(plaintext)
        .expect("Failed to encrypt message");

    // Corrupt the ciphertext
    if let Some(byte) = ciphertext.last_mut() {
        *byte = byte.wrapping_add(1);
    }

    // Bob should fail to decrypt corrupted message
    let result = bob_session.decrypt(&header, &ciphertext);
    assert!(result.is_err());
}

#[test]
fn test_session_decrypt_too_short_ciphertext() {
    let x3dh_secret = [250u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    let alice_dh_key = KeyPair::generate().expect("Failed to generate Alice's DH key");
    let header = MessageHeader::new(alice_dh_key.public_key(), 0, 0);

    // Ciphertext too short (less than nonce size)
    let short_ciphertext = vec![1, 2, 3];

    let result = bob_session.decrypt(&header, &short_ciphertext);
    assert!(result.is_err());
}

#[test]
fn test_session_dh_ratchet_advancement() {
    let x3dh_secret = [60u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Store initial DH keys
    let alice_initial_dh = alice_session.get_dh_public_key();
    let bob_initial_dh = bob_session.get_dh_public_key();

    // Alice sends first message
    let msg1 = b"First message";
    let (header1, ciphertext1) = alice_session
        .encrypt(msg1)
        .expect("Failed to encrypt first message");
    let decrypted1 = bob_session
        .decrypt(&header1, &ciphertext1)
        .expect("Failed to decrypt first message");
    assert_eq!(msg1.as_slice(), decrypted1.as_slice());

    // Bob responds (this should trigger DH ratchet)
    let msg2 = b"Response message";
    let (header2, ciphertext2) = bob_session
        .encrypt(msg2)
        .expect("Failed to encrypt response");
    let decrypted2 = alice_session
        .decrypt(&header2, &ciphertext2)
        .expect("Failed to decrypt response");
    assert_eq!(msg2.as_slice(), decrypted2.as_slice());

    // DH keys should have changed
    let alice_new_dh = alice_session.get_dh_public_key();
    let bob_new_dh = bob_session.get_dh_public_key();

    // Both keys should have changed due to DH ratchet steps
    // Bob's key changes when he receives Alice's first message
    assert_ne!(bob_initial_dh, bob_new_dh);
    // Alice's key changes when she receives Bob's response
    assert_ne!(alice_initial_dh, alice_new_dh);
}

#[test]
fn test_session_multiple_dh_ratchets() {
    let x3dh_secret = [70u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Simulate multiple rounds of communication
    for round in 0..5 {
        // Alice sends
        let alice_msg = format!("Alice message {}", round);
        let (header_a, ciphertext_a) = alice_session
            .encrypt(alice_msg.as_bytes())
            .expect("Failed to encrypt Alice's message");
        let decrypted_a = bob_session
            .decrypt(&header_a, &ciphertext_a)
            .expect("Failed to decrypt Alice's message");
        assert_eq!(alice_msg.as_bytes(), decrypted_a.as_slice());

        // Bob responds
        let bob_msg = format!("Bob response {}", round);
        let (header_b, ciphertext_b) = bob_session
            .encrypt(bob_msg.as_bytes())
            .expect("Failed to encrypt Bob's message");
        let decrypted_b = alice_session
            .decrypt(&header_b, &ciphertext_b)
            .expect("Failed to decrypt Bob's message");
        assert_eq!(bob_msg.as_bytes(), decrypted_b.as_slice());
    }

    // Both sessions should still be functional
    assert!(alice_session.can_send());
    assert!(alice_session.can_receive());
    assert!(bob_session.can_send());
    assert!(bob_session.can_receive());
}

#[test]
fn test_session_decrypt_with_stored_skipped_key() {
    let x3dh_secret = [80u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Alice encrypts multiple messages
    let msg1 = b"Message 1";
    let msg2 = b"Message 2";
    let msg3 = b"Message 3";

    let (header1, ciphertext1) = alice_session
        .encrypt(msg1)
        .expect("Failed to encrypt message 1");
    let (header2, ciphertext2) = alice_session
        .encrypt(msg2)
        .expect("Failed to encrypt message 2");
    let (header3, ciphertext3) = alice_session
        .encrypt(msg3)
        .expect("Failed to encrypt message 3");

    // Bob receives message 3 first (skipping 1 and 2)
    let decrypted3 = bob_session
        .decrypt(&header3, &ciphertext3)
        .expect("Failed to decrypt message 3");
    assert_eq!(msg3.as_slice(), decrypted3.as_slice());

    // Bob should have stored keys for messages 1 and 2
    assert_eq!(bob_session.skipped_keys_count(), 2);

    // Bob receives message 1 (should use stored key)
    let decrypted1 = bob_session
        .decrypt(&header1, &ciphertext1)
        .expect("Failed to decrypt message 1");
    assert_eq!(msg1.as_slice(), decrypted1.as_slice());

    // One key should be removed from storage
    assert_eq!(bob_session.skipped_keys_count(), 1);

    // Bob receives message 2 (should use stored key)
    let decrypted2 = bob_session
        .decrypt(&header2, &ciphertext2)
        .expect("Failed to decrypt message 2");
    assert_eq!(msg2.as_slice(), decrypted2.as_slice());

    // All stored keys should be used up
    assert_eq!(bob_session.skipped_keys_count(), 0);
}

#[test]
fn test_comprehensive_session_lifecycle() {
    let x3dh_secret = [90u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    // Initialize Alice (initiator) and Bob (responder)
    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Store initial state
    let alice_initial_dh = alice_session.get_dh_public_key();
    let bob_initial_dh = bob_session.get_dh_public_key();

    // Phase 1: Alice sends first message (establishes communication)
    let alice_msg1 = b"Hello Bob, this is Alice!";
    let (header1, ciphertext1) = alice_session
        .encrypt(alice_msg1)
        .expect("Failed to encrypt Alice's first message");

    // Verify Alice's session state after encryption
    assert_eq!(alice_session.sending_message_number, 1);
    assert!(alice_session.can_send());

    // Bob receives and decrypts Alice's first message
    let decrypted1 = bob_session
        .decrypt(&header1, &ciphertext1)
        .expect("Failed to decrypt Alice's first message");
    assert_eq!(alice_msg1.as_slice(), decrypted1.as_slice());

    // Verify Bob's session state after first message
    assert!(bob_session.can_send()); // Should now be able to send
    assert!(bob_session.can_receive());
    assert_eq!(bob_session.receiving_message_number, 1);

    // Verify DH ratchet occurred for Bob
    assert_ne!(bob_session.get_dh_public_key(), bob_initial_dh);

    // Phase 2: Bob responds (triggers DH ratchet for Alice)
    let bob_msg1 = b"Hi Alice, nice to hear from you!";
    let (header2, ciphertext2) = bob_session
        .encrypt(bob_msg1)
        .expect("Failed to encrypt Bob's response");

    let decrypted2 = alice_session
        .decrypt(&header2, &ciphertext2)
        .expect("Failed to decrypt Bob's response");
    assert_eq!(bob_msg1.as_slice(), decrypted2.as_slice());

    // Verify Alice's DH key changed after receiving Bob's message
    assert_ne!(alice_session.get_dh_public_key(), alice_initial_dh);

    // Phase 3: Extended conversation with multiple DH ratchet steps
    let conversation = [
        ("Alice", &b"How's your day going?"[..]),
        ("Bob", &b"Pretty good! Working on some crypto stuff."[..]),
        (
            "Alice",
            &b"That sounds interesting! What kind of crypto?"[..],
        ),
        (
            "Bob",
            &b"Signal protocol implementation. Very cool stuff!"[..],
        ),
        (
            "Alice",
            &b"I love cryptography! Forward secrecy is amazing."[..],
        ),
        ("Bob", &b"Absolutely! The Double Ratchet is elegant."[..]),
    ];

    for (sender, message) in conversation.iter() {
        if *sender == "Alice" {
            let (header, ciphertext) = alice_session
                .encrypt(message)
                .expect("Failed to encrypt Alice's message");
            let decrypted = bob_session
                .decrypt(&header, &ciphertext)
                .expect("Failed to decrypt Alice's message");
            assert_eq!(*message, decrypted.as_slice());
        } else {
            let (header, ciphertext) = bob_session
                .encrypt(message)
                .expect("Failed to encrypt Bob's message");
            let decrypted = alice_session
                .decrypt(&header, &ciphertext)
                .expect("Failed to decrypt Bob's message");
            assert_eq!(*message, decrypted.as_slice());
        }
    }

    // Verify both sessions are still functional
    assert!(alice_session.can_send());
    assert!(alice_session.can_receive());
    assert!(bob_session.can_send());
    assert!(bob_session.can_receive());

    // Verify message numbers have advanced (they may reset during DH ratchet steps)
    // The important thing is that both sessions are functional
    assert!(alice_session.can_send());
    assert!(alice_session.can_receive());
    assert!(bob_session.can_send());
    assert!(bob_session.can_receive());
}

#[test]
fn test_comprehensive_out_of_order_delivery() {
    let x3dh_secret = [95u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Alice encrypts multiple messages in sequence
    let messages = [
        &b"Message 1: First message"[..],
        &b"Message 2: Second message"[..],
        &b"Message 3: Third message"[..],
        &b"Message 4: Fourth message"[..],
        &b"Message 5: Fifth message"[..],
    ];

    let mut headers = Vec::new();
    let mut ciphertexts = Vec::new();

    for message in messages.iter() {
        let (header, ciphertext) = alice_session
            .encrypt(message)
            .expect("Failed to encrypt message");
        headers.push(header);
        ciphertexts.push(ciphertext);
    }

    // Verify Alice's state after encrypting all messages
    assert_eq!(alice_session.sending_message_number, 5);

    // Bob receives messages in out-of-order sequence: 1, 3, 5, 2, 4
    let delivery_order = [0, 2, 4, 1, 3]; // Indices for messages 1, 3, 5, 2, 4
    let mut decrypted_messages = vec![Vec::new(); 5];

    for &msg_idx in delivery_order.iter() {
        let decrypted = bob_session
            .decrypt(&headers[msg_idx], &ciphertexts[msg_idx])
            .expect(&format!("Failed to decrypt message {}", msg_idx + 1));
        decrypted_messages[msg_idx] = decrypted;
    }

    // Verify all messages were decrypted correctly
    for (i, (original, decrypted)) in messages.iter().zip(decrypted_messages.iter()).enumerate() {
        assert_eq!(
            *original,
            decrypted.as_slice(),
            "Message {} was not decrypted correctly",
            i + 1
        );
    }

    // Verify Bob's final state
    assert_eq!(bob_session.receiving_message_number, 5);
    assert_eq!(bob_session.skipped_keys_count(), 0); // All keys should be used
}

#[test]
fn test_session_lifecycle_with_gaps() {
    let x3dh_secret = [100u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        100,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        100,
        None,
    )
    .expect("Failed to create Bob's session");

    // Alice sends many messages
    let mut headers = Vec::new();
    let mut ciphertexts = Vec::new();

    for i in 0..20 {
        let message = format!("Message number {}", i + 1);
        let (header, ciphertext) = alice_session
            .encrypt(message.as_bytes())
            .expect("Failed to encrypt message");
        headers.push(header);
        ciphertexts.push(ciphertext);
    }

    // Bob receives only some messages with gaps
    let received_indices = [0, 2, 5, 7, 10, 15, 19]; // Sparse delivery

    for &idx in received_indices.iter() {
        let expected_message = format!("Message number {}", idx + 1);
        let decrypted = bob_session
            .decrypt(&headers[idx], &ciphertexts[idx])
            .expect(&format!("Failed to decrypt message {}", idx + 1));
        assert_eq!(expected_message.as_bytes(), decrypted.as_slice());
    }

    // Verify Bob has stored keys for skipped messages
    assert!(bob_session.skipped_keys_count() > 0);

    // Bob receives some of the previously skipped messages
    let late_indices = [1, 3, 6, 11];

    for &idx in late_indices.iter() {
        let expected_message = format!("Message number {}", idx + 1);
        let decrypted = bob_session
            .decrypt(&headers[idx], &ciphertexts[idx])
            .expect(&format!("Failed to decrypt late message {}", idx + 1));
        assert_eq!(expected_message.as_bytes(), decrypted.as_slice());
    }
}

#[test]
fn test_bidirectional_out_of_order_conversation() {
    let x3dh_secret = [105u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Simulate a complex conversation with interleaved messages

    // Alice sends initial messages
    let alice_msgs = [
        &b"Alice: Hello!"[..],
        &b"Alice: How are you?"[..],
        &b"Alice: What's new?"[..],
    ];

    let mut alice_headers = Vec::new();
    let mut alice_ciphertexts = Vec::new();

    for msg in alice_msgs.iter() {
        let (header, ciphertext) = alice_session
            .encrypt(msg)
            .expect("Failed to encrypt Alice's message");
        alice_headers.push(header);
        alice_ciphertexts.push(ciphertext);
    }

    // Bob receives Alice's first message
    let decrypted = bob_session
        .decrypt(&alice_headers[0], &alice_ciphertexts[0])
        .expect("Failed to decrypt Alice's first message");
    assert_eq!(alice_msgs[0], decrypted.as_slice());

    // Bob responds with multiple messages
    let bob_msgs = [
        &b"Bob: Hi Alice!"[..],
        &b"Bob: I'm doing well, thanks!"[..],
        &b"Bob: Working on some interesting projects."[..],
    ];

    let mut bob_headers = Vec::new();
    let mut bob_ciphertexts = Vec::new();

    for msg in bob_msgs.iter() {
        let (header, ciphertext) = bob_session
            .encrypt(msg)
            .expect("Failed to encrypt Bob's message");
        bob_headers.push(header);
        bob_ciphertexts.push(ciphertext);
    }

    // Messages arrive out of order
    // Alice receives Bob's messages: 2, 1, 3
    let decrypted = alice_session
        .decrypt(&bob_headers[1], &bob_ciphertexts[1])
        .expect("Failed to decrypt Bob's second message");
    assert_eq!(bob_msgs[1], decrypted.as_slice());

    let decrypted = alice_session
        .decrypt(&bob_headers[0], &bob_ciphertexts[0])
        .expect("Failed to decrypt Bob's first message");
    assert_eq!(bob_msgs[0], decrypted.as_slice());

    let decrypted = alice_session
        .decrypt(&bob_headers[2], &bob_ciphertexts[2])
        .expect("Failed to decrypt Bob's third message");
    assert_eq!(bob_msgs[2], decrypted.as_slice());

    // Bob receives Alice's remaining messages: 3, 2
    let decrypted = bob_session
        .decrypt(&alice_headers[2], &alice_ciphertexts[2])
        .expect("Failed to decrypt Alice's third message");
    assert_eq!(alice_msgs[2], decrypted.as_slice());

    let decrypted = bob_session
        .decrypt(&alice_headers[1], &alice_ciphertexts[1])
        .expect("Failed to decrypt Alice's second message");
    assert_eq!(alice_msgs[1], decrypted.as_slice());

    // Continue conversation
    let alice_final = b"Alice: Great to hear! Let's keep in touch.";
    let (header, ciphertext) = alice_session
        .encrypt(alice_final)
        .expect("Failed to encrypt Alice's final message");
    let decrypted = bob_session
        .decrypt(&header, &ciphertext)
        .expect("Failed to decrypt Alice's final message");
    assert_eq!(alice_final.as_slice(), decrypted.as_slice());

    let bob_final = b"Bob: Absolutely! Talk to you soon.";
    let (header, ciphertext) = bob_session
        .encrypt(bob_final)
        .expect("Failed to encrypt Bob's final message");
    let decrypted = alice_session
        .decrypt(&header, &ciphertext)
        .expect("Failed to decrypt Bob's final message");
    assert_eq!(bob_final.as_slice(), decrypted.as_slice());

    // Verify both sessions are in good state
    assert!(alice_session.can_send());
    assert!(alice_session.can_receive());
    assert!(bob_session.can_send());
    assert!(bob_session.can_receive());

    // Verify minimal skipped keys (should be cleaned up)
    assert!(alice_session.skipped_keys_count() <= 5);
    assert!(bob_session.skipped_keys_count() <= 5);
}

#[test]
fn test_session_persistence_and_restoration() {
    let x3dh_secret = [110u8; 32];
    let bob_dh_key = KeyPair::generate().expect("Failed to generate Bob's DH key");

    // Create initial sessions
    let mut alice_session = Session::new_initiator(
        x3dh_secret,
        &create_test_bundle(bob_dh_key.public_key()),
        1000,
        None,
    )
    .expect("Failed to create Alice's session");
    let mut bob_session = Session::new_responder(
        x3dh_secret,
        bob_dh_key,
        KeyPair::generate()
            .expect("Failed to generate remote identity key")
            .public_key(),
        1000,
        None,
    )
    .expect("Failed to create Bob's session");

    // Exchange some messages
    let msg1 = b"Message before serialization";
    let (header1, ciphertext1) = alice_session
        .encrypt(msg1)
        .expect("Failed to encrypt message");
    let decrypted1 = bob_session
        .decrypt(&header1, &ciphertext1)
        .expect("Failed to decrypt message");
    assert_eq!(msg1.as_slice(), decrypted1.as_slice());

    // Serialize sessions
    let alice_json = alice_session
        .to_json()
        .expect("Failed to serialize Alice's session");
    let bob_json = bob_session
        .to_json()
        .expect("Failed to serialize Bob's session");

    // Restore sessions from serialization
    let mut alice_restored =
        Session::from_json(&alice_json).expect("Failed to restore Alice's session");
    let mut bob_restored = Session::from_json(&bob_json).expect("Failed to restore Bob's session");

    // Verify restored sessions work correctly
    let msg2 = b"Message after restoration";
    let (header2, ciphertext2) = alice_restored
        .encrypt(msg2)
        .expect("Failed to encrypt with restored session");
    let decrypted2 = bob_restored
        .decrypt(&header2, &ciphertext2)
        .expect("Failed to decrypt with restored session");
    assert_eq!(msg2.as_slice(), decrypted2.as_slice());

    // Verify bidirectional communication still works
    let msg3 = b"Bob's response after restoration";
    let (header3, ciphertext3) = bob_restored
        .encrypt(msg3)
        .expect("Failed to encrypt Bob's response");
    let decrypted3 = alice_restored
        .decrypt(&header3, &ciphertext3)
        .expect("Failed to decrypt Bob's response");
    assert_eq!(msg3.as_slice(), decrypted3.as_slice());
}
