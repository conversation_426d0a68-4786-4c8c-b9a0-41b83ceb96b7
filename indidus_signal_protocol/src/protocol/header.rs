use crate::crypto::keys::<PERSON><PERSON><PERSON>;
use crate::error::{ProtocolError, Result};
use serde::{Deserialize, Serialize};

/// Message header containing metadata needed for Double Ratchet decryption
///
/// This header is sent with each encrypted message and contains the information
/// the recipient needs to decrypt the message and advance their ratchet state.
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq)]
pub struct MessageHeader {
    /// Sender's current DH ratchet public key
    /// This may be the same as previous messages or a new key if the DH ratchet advanced
    pub dh_public_key: PublicKey,

    /// Previous chain length (PN)
    /// Number of messages in the previous sending chain before this DH ratchet step
    pub previous_chain_length: u32,

    /// Message number (N) in the current chain
    /// Sequential number of this message in the current sending chain
    pub message_number: u32,
}

impl MessageHeader {
    /// Create a new message header
    ///
    /// # Arguments
    /// * `dh_public_key` - The sender's current DH ratchet public key
    /// * `previous_chain_length` - Number of messages in the previous sending chain
    /// * `message_number` - Sequential number of this message in the current chain
    pub fn new(dh_public_key: PublicKey, previous_chain_length: u32, message_number: u32) -> Self {
        Self {
            dh_public_key,
            previous_chain_length,
            message_number,
        }
    }

    /// Get a key for indexing skipped message keys
    /// Returns a tuple of (DH public key bytes, message number)
    pub fn get_skip_key(&self) -> (Vec<u8>, u32) {
        (self.dh_public_key.as_bytes().to_vec(), self.message_number)
    }

    /// Serialize the header to JSON
    pub fn to_json(&self) -> Result<String> {
        serde_json::to_string(self)
            .map_err(|e| ProtocolError::serialization_error("MessageHeader", e.to_string()))
    }

    /// Deserialize the header from JSON
    pub fn from_json(json: &str) -> Result<Self> {
        serde_json::from_str(json)
            .map_err(|e| ProtocolError::deserialization_error("MessageHeader", e.to_string()))
    }

    /// Serialize the header to bytes (using JSON internally)
    pub fn to_bytes(&self) -> Result<Vec<u8>> {
        let json = self.to_json()?;
        Ok(json.into_bytes())
    }

    /// Deserialize the header from bytes (expecting JSON format)
    pub fn from_bytes(bytes: &[u8]) -> Result<Self> {
        let json = std::str::from_utf8(bytes)
            .map_err(|e| ProtocolError::deserialization_error("MessageHeader", e.to_string()))?;
        Self::from_json(json)
    }
}
