# Security Scanning

This document describes the automated security scanning setup for the Indidus E2EE project.

## Overview

The project includes automated security scanning as part of the CI/CD pipeline to ensure code security and identify potential vulnerabilities. The security scanning runs in parallel with other CI jobs and will fail the build if security issues are detected.

## Security Tools

### cargo-audit

**Purpose**: Scans dependencies for known security vulnerabilities.

**What it does**:
- Checks all dependencies against the RustSec Advisory Database
- Identifies crates with known security vulnerabilities
- Reports outdated dependencies with security fixes available

**Configuration**: Runs with default settings to catch all known vulnerabilities.

**Failure conditions**:
- Any dependency has a known security vulnerability
- Dependencies are significantly outdated with security patches available

### cargo-geiger

**Purpose**: Detects unsafe code usage throughout the codebase.

**What it does**:
- Scans all Rust code for `unsafe` blocks
- Analyzes dependencies for unsafe code usage
- Provides a security report showing "radiation" levels of unsafe code

**Configuration**: 
- `--deny-unsound`: Fails on unsound unsafe code patterns
- `--forbid-unsafe`: Fails if any unsafe code is detected

**Failure conditions**:
- Any `unsafe` blocks are found in the codebase
- Dependencies contain unsound unsafe code patterns

## CI Integration

The security scanning is integrated into the GitHub Actions workflow (`.github/workflows/ci.yml`) as a separate job called `security-scan` that runs in parallel with tests.

### Job Structure

```yaml
security-scan:
  name: Security Scan
  runs-on: ubuntu-latest
  
  steps:
  - uses: actions/checkout@v4
  - name: Install Rust
    uses: dtolnay/rust-toolchain@stable
  - name: Cache cargo registry
    uses: actions/cache@v3
    # ... cache configuration
  - name: Install cargo-audit
    run: cargo install cargo-audit
  - name: Run cargo-audit
    run: cargo audit
  - name: Install cargo-geiger
    run: cargo install cargo-geiger
  - name: Run cargo-geiger
    run: cargo geiger --deny-unsound --forbid-unsafe
```

## Local Development

Developers can run the same security checks locally before pushing code:

### Running cargo-audit locally

```bash
# Install cargo-audit (one-time setup)
cargo install cargo-audit

# Run security audit
cargo audit
```

### Running cargo-geiger locally

```bash
# Install cargo-geiger (one-time setup)
cargo install cargo-geiger

# Run unsafe code detection
cargo geiger --deny-unsound --forbid-unsafe
```

## Security Policy

### Unsafe Code

This project maintains a **zero-unsafe policy**. No `unsafe` blocks are permitted in the codebase. This ensures:

- Memory safety guarantees are maintained
- Reduced attack surface
- Easier security auditing
- Better maintainability

**Exception Process**: If unsafe code becomes absolutely necessary for performance or interoperability reasons:

1. Create a detailed security review document
2. Get approval from project maintainers
3. Add comprehensive documentation and safety comments
4. Update CI configuration to allow specific unsafe patterns
5. Schedule regular security reviews of unsafe code

### Dependency Security

All dependencies must be free of known security vulnerabilities. The CI will fail if:

- Any dependency has a known CVE
- Dependencies are more than 6 months behind on security updates

**Remediation Process**:

1. Update vulnerable dependencies to patched versions
2. If no patch is available, find alternative dependencies
3. If no alternatives exist, document the risk and create mitigation plans

## Monitoring and Alerts

- Security scan failures will block merges to the main branch
- Failed security scans generate notifications to project maintainers
- Regular dependency updates should be scheduled to prevent security debt

## Security Scanning Results

The security scanning provides the following benefits:

- **Proactive vulnerability detection**: Issues are caught before they reach production
- **Dependency hygiene**: Ensures all dependencies are up-to-date and secure
- **Code safety verification**: Confirms no unsafe code patterns are introduced
- **Compliance support**: Provides audit trails for security reviews

## Troubleshooting

### Common Issues

**cargo-audit fails with dependency vulnerabilities**:
- Update the vulnerable dependency to the latest version
- Check if alternative dependencies are available
- Review the vulnerability details to assess impact

**cargo-geiger fails with unsafe code detection**:
- Remove any `unsafe` blocks from the code
- Check if dependencies introduce unsafe code
- Ensure no unsafe patterns are used in macros or generated code

**Security tools installation fails**:
- Check Rust toolchain version compatibility
- Verify network connectivity for crate downloads
- Clear cargo cache if installation is corrupted

### Getting Help

For security-related questions or issues:

1. Check the RustSec Advisory Database: https://rustsec.org/
2. Review Rust security guidelines: https://doc.rust-lang.org/nomicon/
3. Consult project maintainers for policy exceptions
4. Create security-focused issues in the project repository