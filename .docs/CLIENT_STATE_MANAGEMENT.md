# Client State Management Tutorial

This tutorial explains how to persist and restore a client's cryptographic state in the Indidus E2EE system, ensuring your application maintains a consistent identity across restarts and sessions.

## Table of Contents

1. [Why State Management Matters](#why-state-management-matters)
2. [Understanding Client State](#understanding-client-state)
3. [Basic State Operations](#basic-state-operations)
4. [Complete Example: Persistent Chat Client](#complete-example-persistent-chat-client)
5. [Best Practices for Secure Storage](#best-practices-for-secure-storage)
6. [Error Handling](#error-handling)
7. [Advanced Patterns](#advanced-patterns)
8. [Troubleshooting](#troubleshooting)

## Why State Management Matters

In end-to-end encrypted messaging systems, maintaining a persistent cryptographic identity is crucial for:

- **Session Continuity**: Preserving ongoing conversations when your application restarts
- **Identity Consistency**: Ensuring other clients recognize you as the same participant
- **Performance**: Avoiding the overhead of re-establishing cryptographic sessions
- **User Experience**: Seamless operation without requiring users to re-authenticate

Without proper state management, each application restart would create a new cryptographic identity, breaking existing conversations and requiring session re-establishment with all contacts.

## Understanding Client State

The Indidus E2EE client maintains several types of cryptographic state:

- **Identity Keys**: Your client's long-term cryptographic identity
- **Session Keys**: Ephemeral keys for ongoing conversations with other clients
- **Pre-keys**: A pool of one-time keys for initiating new sessions
- **Protocol State**: Internal Signal Protocol state including ratchet chains

All of this state is encapsulated in an opaque binary blob that can be safely persisted and restored.

## Basic State Operations

### Saving Client State

The `save_state()` method serializes all cryptographic state into a binary blob:

```rust
use indidus_e2ee_client::{Client, ClientConfig};
use url::Url;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ClientConfig::new(Url::parse("https://your-server.com")?);
    let mut client = Client::new(config)?;
    client.initialize().await?;
    
    // Perform some operations...
    client.connect().await?;
    
    // Save the current state
    let state_blob = client.save_state()?;
    
    // Store the blob securely (see best practices below)
    tokio::fs::write("client_state.bin", state_blob).await?;
    
    println!("Client state saved successfully");
    Ok(())
}
```

### Restoring Client State

Use `Client::new()` with an optional state parameter to restore a previous session:

```rust
use indidus_e2ee_client::{Client, ClientConfig};
use url::Url;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config = ClientConfig::new(Url::parse("https://your-server.com")?);
    
    // Try to load existing state
    let saved_state = match tokio::fs::read("client_state.bin").await {
        Ok(data) => Some(data),
        Err(_) => {
            println!("No saved state found, creating fresh client");
            None
        }
    };
    
    // Create client with optional state
    let mut client = Client::new(config, saved_state)?;
    
    if saved_state.is_some() {
        println!("Client restored from saved state");
    } else {
        // Only initialize if creating a fresh client
        client.initialize().await?;
        println!("New client initialized");
    }
    
    client.connect().await?;
    Ok(())
}
```

## Complete Example: Persistent Chat Client

Here's a complete example showing how to build a chat client that maintains state across restarts:

```rust
use indidus_e2ee_client::{Client, ClientConfig, ClientEvent, ConnectionState};
use std::path::PathBuf;
use tokio::io::{self, AsyncBufReadExt, BufReader};
use url::Url;
use uuid::Uuid;

struct PersistentChatClient {
    client: Client,
    state_file: PathBuf,
}

impl PersistentChatClient {
    /// Create a new persistent chat client
    pub async fn new(server_url: Url, state_file: PathBuf) -> Result<Self, Box<dyn std::error::Error>> {
        let config = ClientConfig::new(server_url)
            .with_display_name("Persistent Chat Client".to_string())
            .with_debug_mode(true);
        
        // Try to restore from saved state
        let saved_state = match tokio::fs::read(&state_file).await {
            Ok(data) => {
                println!("📁 Restoring client from saved state...");
                Some(data)
            }
            Err(_) => {
                println!("🆕 No saved state found, creating new client...");
                None
            }
        };
        
        let mut client = Client::new(config, saved_state)?;
        
        // Only initialize if we don't have saved state
        if saved_state.is_none() {
            client.initialize().await?;
            println!("✅ New client initialized with ID: {}", client.client_id());
        } else {
            println!("✅ Client restored with ID: {}", client.client_id());
        }
        
        Ok(Self {
            client,
            state_file,
        })
    }
    
    /// Connect to the server and start the event loop
    pub async fn run(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Connect to server
        self.client.connect().await?;
        
        // Save state after successful connection
        self.save_state().await?;
        
        println!("🔗 Connected to server. Type messages to send, or 'quit' to exit.");
        println!("💡 Your client ID: {}", self.client.client_id());
        
        // Set up stdin reader for user input
        let stdin = io::stdin();
        let mut lines = BufReader::new(stdin).lines();
        
        loop {
            tokio::select! {
                // Handle user input
                line = lines.next_line() => {
                    match line? {
                        Some(input) => {
                            if input.trim() == "quit" {
                                break;
                            }
                            
                            if let Some((recipient_str, message)) = input.split_once(' ') {
                                if let Ok(recipient_id) = Uuid::parse_str(recipient_str.trim()) {
                                    match self.client.send_message(recipient_id, message.as_bytes()).await {
                                        Ok(_) => {
                                            println!("📤 Message sent to {}", recipient_id);
                                            // Save state after sending message
                                            self.save_state().await?;
                                        }
                                        Err(e) => println!("❌ Failed to send message: {}", e),
                                    }
                                } else {
                                    println!("❌ Invalid recipient ID format. Use: <uuid> <message>");
                                }
                            } else {
                                println!("❌ Usage: <recipient_id> <message>");
                            }
                        }
                        None => break,
                    }
                }
                
                // Handle client events
                event = self.client.next_event() => {
                    match event? {
                        Some(ClientEvent::MessageReceived { sender_id, encrypted_payload, .. }) => {
                            match self.client.decrypt_message(sender_id, &encrypted_payload).await {
                                Ok(decrypted) => {
                                    println!("📨 Message from {}: {}", 
                                        sender_id, 
                                        String::from_utf8_lossy(&decrypted)
                                    );
                                    // Save state after receiving message
                                    self.save_state().await?;
                                }
                                Err(e) => println!("❌ Failed to decrypt message: {}", e),
                            }
                        }
                        
                        Some(ClientEvent::ConnectionStateChanged(state)) => {
                            match state {
                                ConnectionState::Connected => {
                                    println!("🟢 Connected to server");
                                }
                                ConnectionState::Disconnected => {
                                    println!("🔴 Disconnected from server");
                                }
                                ConnectionState::Reconnecting => {
                                    println!("🟡 Reconnecting to server...");
                                }
                                other => {
                                    println!("🔄 Connection state: {:?}", other);
                                }
                            }
                        }
                        
                        Some(other_event) => {
                            println!("ℹ️ Event: {:?}", other_event);
                        }
                        
                        None => {
                            println!("⚠️ Event stream ended");
                            break;
                        }
                    }
                }
            }
        }
        
        // Save final state before shutdown
        self.save_state().await?;
        println!("💾 Final state saved. Goodbye!");
        
        Ok(())
    }
    
    /// Save the current client state to disk
    async fn save_state(&self) -> Result<(), Box<dyn std::error::Error>> {
        let state_blob = self.client.save_state()?;
        tokio::fs::write(&self.state_file, state_blob).await?;
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let server_url = Url::parse("ws://127.0.0.1:8080/ws")?;
    let state_file = PathBuf::from("persistent_chat_state.bin");
    
    let mut chat_client = PersistentChatClient::new(server_url, state_file).await?;
    chat_client.run().await?;
    
    Ok(())
}
```

## Best Practices for Secure Storage

### 1. File Permissions

Always set restrictive file permissions on state files:

```rust
use std::os::unix::fs::PermissionsExt;

async fn save_state_secure(client: &Client, path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let state_blob = client.save_state()?;
    
    // Write the file
    tokio::fs::write(path, state_blob).await?;
    
    // Set restrictive permissions (owner read/write only)
    let mut perms = tokio::fs::metadata(path).await?.permissions();
    perms.set_mode(0o600);
    tokio::fs::set_permissions(path, perms).await?;
    
    Ok(())
}
```

### 2. Encryption at Rest

For additional security, encrypt the state blob before storing:

```rust
use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, KeyInit, OsRng}};
use rand::RngCore;

async fn save_state_encrypted(
    client: &Client, 
    path: &str, 
    encryption_key: &[u8; 32]
) -> Result<(), Box<dyn std::error::Error>> {
    let state_blob = client.save_state()?;
    
    // Generate random nonce
    let mut nonce_bytes = [0u8; 12];
    OsRng.fill_bytes(&mut nonce_bytes);
    let nonce = Nonce::from_slice(&nonce_bytes);
    
    // Encrypt the state
    let cipher = Aes256Gcm::new(Key::from_slice(encryption_key));
    let encrypted = cipher.encrypt(nonce, state_blob.as_ref())?;
    
    // Store nonce + encrypted data
    let mut output = Vec::with_capacity(12 + encrypted.len());
    output.extend_from_slice(&nonce_bytes);
    output.extend_from_slice(&encrypted);
    
    tokio::fs::write(path, output).await?;
    Ok(())
}

async fn load_state_encrypted(
    path: &str, 
    encryption_key: &[u8; 32]
) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
    let encrypted_data = tokio::fs::read(path).await?;
    
    if encrypted_data.len() < 12 {
        return Err("Invalid encrypted state file".into());
    }
    
    // Extract nonce and encrypted data
    let (nonce_bytes, encrypted) = encrypted_data.split_at(12);
    let nonce = Nonce::from_slice(nonce_bytes);
    
    // Decrypt
    let cipher = Aes256Gcm::new(Key::from_slice(encryption_key));
    let decrypted = cipher.decrypt(nonce, encrypted)?;
    
    Ok(decrypted)
}
```

### 3. Atomic Writes

Use atomic writes to prevent corruption during save operations:

```rust
use tempfile::NamedTempFile;

async fn save_state_atomic(client: &Client, path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let state_blob = client.save_state()?;
    
    // Write to temporary file first
    let temp_file = NamedTempFile::new_in(
        std::path::Path::new(path).parent().unwrap_or(std::path::Path::new("."))
    )?;
    
    tokio::fs::write(temp_file.path(), state_blob).await?;
    
    // Atomically move to final location
    temp_file.persist(path)?;
    
    Ok(())
}
```

### 4. Backup and Rotation

Implement state file rotation to prevent data loss:

```rust
async fn save_state_with_backup(client: &Client, path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let backup_path = format!("{}.backup", path);
    
    // If current state exists, back it up
    if tokio::fs::metadata(path).await.is_ok() {
        tokio::fs::copy(path, &backup_path).await?;
    }
    
    // Save new state
    let state_blob = client.save_state()?;
    tokio::fs::write(path, state_blob).await?;
    
    println!("State saved with backup at {}", backup_path);
    Ok(())
}
```

## Error Handling

Proper error handling is crucial for state management:

```rust
use indidus_e2ee_client::{Client, ClientConfig, SdkError};

async fn robust_state_management() -> Result<(), Box<dyn std::error::Error>> {
    let config = ClientConfig::new(Url::parse("https://server.com")?);
    let state_path = "client_state.bin";
    
    // Attempt to load state with fallback
    let client = match load_client_state(&config, state_path).await {
        Ok(client) => {
            println!("✅ Client restored from state");
            client
        }
        Err(e) => {
            eprintln!("⚠️ Failed to restore state: {}", e);
            println!("🆕 Creating new client");
            
            let mut client = Client::new(config, None)?;
            client.initialize().await?;
            client
        }
    };
    
    // Use client...
    
    // Save state with error handling
    if let Err(e) = save_client_state(&client, state_path).await {
        eprintln!("❌ Failed to save state: {}", e);
        eprintln!("⚠️ Client state may be lost on restart");
    }
    
    Ok(())
}

async fn load_client_state(
    config: &ClientConfig, 
    path: &str
) -> Result<Client, Box<dyn std::error::Error>> {
    let state_data = tokio::fs::read(path).await
        .map_err(|e| format!("Failed to read state file: {}", e))?;
    
    let client = Client::new(config.clone(), Some(state_data))
        .map_err(|e| format!("Failed to restore client from state: {}", e))?;
    
    Ok(client)
}

async fn save_client_state(
    client: &Client, 
    path: &str
) -> Result<(), Box<dyn std::error::Error>> {
    let state_blob = client.save_state()
        .map_err(|e| format!("Failed to serialize client state: {}", e))?;
    
    tokio::fs::write(path, state_blob).await
        .map_err(|e| format!("Failed to write state file: {}", e))?;
    
    Ok(())
}
```

## Advanced Patterns

### 1. Periodic State Saving

Automatically save state at regular intervals:

```rust
use tokio::time::{interval, Duration};

async fn run_with_periodic_saves(mut client: Client) -> Result<(), Box<dyn std::error::Error>> {
    let mut save_interval = interval(Duration::from_secs(30));
    let state_path = "client_state.bin";
    
    loop {
        tokio::select! {
            // Handle client events
            event = client.next_event() => {
                if let Some(event) = event? {
                    handle_client_event(event).await?;
                }
            }
            
            // Periodic state save
            _ = save_interval.tick() => {
                if let Err(e) = save_client_state(&client, state_path).await {
                    eprintln!("⚠️ Periodic state save failed: {}", e);
                }
            }
        }
    }
}
```

### 2. State Migration

Handle state format changes across application versions:

```rust
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
struct StateWrapper {
    version: u32,
    data: Vec<u8>,
}

async fn load_state_with_migration(path: &str) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
    let file_data = tokio::fs::read(path).await?;
    
    // Try to parse as versioned state
    if let Ok(wrapper) = bincode::deserialize::<StateWrapper>(&file_data) {
        match wrapper.version {
            1 => Ok(wrapper.data),
            2 => migrate_from_v1_to_v2(wrapper.data),
            _ => Err("Unsupported state version".into()),
        }
    } else {
        // Assume legacy format (version 1)
        migrate_from_v1_to_v2(file_data)
    }
}

fn migrate_from_v1_to_v2(v1_data: Vec<u8>) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
    // Implement migration logic here
    println!("🔄 Migrating state from v1 to v2");
    Ok(v1_data) // Placeholder
}
```

### 3. Multi-Device State Synchronization

For applications supporting multiple devices:

```rust
struct MultiDeviceStateManager {
    local_state_path: String,
    device_id: String,
    sync_enabled: bool,
}

impl MultiDeviceStateManager {
    async fn save_state(&self, client: &Client) -> Result<(), Box<dyn std::error::Error>> {
        let state_blob = client.save_state()?;
        
        // Save locally
        tokio::fs::write(&self.local_state_path, &state_blob).await?;
        
        // Optionally sync to cloud storage
        if self.sync_enabled {
            self.upload_to_cloud_storage(&state_blob).await?;
        }
        
        Ok(())
    }
    
    async fn load_state(&self) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // Try local first
        if let Ok(data) = tokio::fs::read(&self.local_state_path).await {
            return Ok(data);
        }
        
        // Fallback to cloud storage
        if self.sync_enabled {
            return self.download_from_cloud_storage().await;
        }
        
        Err("No state found".into())
    }
    
    async fn upload_to_cloud_storage(&self, data: &[u8]) -> Result<(), Box<dyn std::error::Error>> {
        // Implement cloud storage upload
        println!("☁️ Uploading state to cloud storage");
        Ok(())
    }
    
    async fn download_from_cloud_storage(&self) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // Implement cloud storage download
        println!("☁️ Downloading state from cloud storage");
        Err("Not implemented".into())
    }
}
```

## Troubleshooting

### Common Issues and Solutions

#### 1. State Corruption

**Problem**: Client fails to restore from saved state.

**Solution**: Implement state validation and fallback:

```rust
async fn load_state_with_validation(
    config: &ClientConfig,
    path: &str
) -> Result<Client, Box<dyn std::error::Error>> {
    let state_data = tokio::fs::read(path).await?;
    
    // Validate state data before using
    if state_data.len() < 32 {
        return Err("State file too small, likely corrupted".into());
    }
    
    // Try to create client
    match Client::new(config.clone(), Some(state_data)) {
        Ok(client) => Ok(client),
        Err(SdkError::Serialization(_)) => {
            eprintln!("⚠️ State appears corrupted, creating fresh client");
            let mut client = Client::new(config.clone(), None)?;
            client.initialize().await?;
            Ok(client)
        }
        Err(e) => Err(e.into()),
    }
}
```

#### 2. Permission Errors

**Problem**: Cannot write to state file location.

**Solution**: Check and create directories with proper permissions:

```rust
async fn ensure_state_directory(path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let state_path = std::path::Path::new(path);
    
    if let Some(parent) = state_path.parent() {
        tokio::fs::create_dir_all(parent).await?;
        
        // Set directory permissions
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(parent).await?.permissions();
            perms.set_mode(0o700);
            tokio::fs::set_permissions(parent, perms).await?;
        }
    }
    
    Ok(())
}
```

#### 3. State Size Growth

**Problem**: State file grows unexpectedly large.

**Solution**: Monitor state size and implement cleanup:

```rust
async fn monitor_state_size(client: &Client) -> Result<(), Box<dyn std::error::Error>> {
    let state_blob = client.save_state()?;
    let size_mb = state_blob.len() as f64 / (1024.0 * 1024.0);
    
    println!("Current state size: {:.2} MB", size_mb);
    
    if size_mb > 10.0 {
        eprintln!("⚠️ State size is unusually large: {:.2} MB", size_mb);
        eprintln!("Consider investigating for potential issues");
    }
    
    Ok(())
}
```

### Performance Considerations

1. **Save Frequency**: Don't call `save_state()` after every operation. Save at logical points like:
   - After successful connection
   - After sending/receiving messages
   - Before application shutdown
   - Periodically (every 30 seconds to 5 minutes)

2. **State Size**: Monitor state file size. Typical sizes should be:
   - Fresh client: ~1-5 KB
   - Active client with sessions: ~10-50 KB
   - Very active client: ~100 KB - 1 MB

3. **I/O Performance**: Use async I/O operations and consider batching multiple state changes.

## Summary

Client state management is essential for building robust, user-friendly encrypted messaging applications. Key takeaways:

- **Always save state** after important operations and before shutdown
- **Handle restoration gracefully** with fallbacks for corrupted or missing state
- **Secure your state files** with proper permissions and encryption
- **Monitor and validate** state to catch issues early
- **Plan for migration** when your application evolves

By following these patterns and best practices, you'll ensure your users have a seamless experience with persistent, secure messaging sessions.