# Indidus E2EE Rust Crates - Product Requirements Document (Detailed Version)

## Executive Summary

This PRD defines the requirements for building a comprehensive, relay-based end-to-end encrypted (E2EE) Rust ecosystem consisting of three production-ready crates. The foundational `indidus_signal_protocol` crate provides a from-scratch implementation of the Signal Protocol, while the `indidus_e2ee_client` and `indidus_e2ee_server` crates provide user-facing APIs with stateless design. This architecture enables secure communication through a relay server while maintaining clean separation of concerns and complete user control over persistence.

## 1. Product Vision & Goals

### 1.1 Vision Statement
Create the most developer-friendly, secure, and maintainable relay-based E2EE communication libraries in the Rust ecosystem that provide clean separation between crypto functionality and HTTP implementation, allowing seamless integration into any web framework with complete user control over state persistence.

### 1.2 Primary Goals
- **Developer Experience**: 3-line integration for basic messaging, framework-agnostic server integration
- **Security**: Enterprise-grade encryption with zero-trust architecture
- **Flexibility**: Users control HTTP endpoints, authentication, and server implementation
- **Simplicity**: SDK libraries designed for single developer maintenance (50-60 hours/month), while core protocol requires specialized cryptographic expertise
- **Reliability**: Production-ready with comprehensive error handling
- **Performance**: Support 10GB+ file transfers with progress tracking

### 1.3 Success Metrics
- **Adoption**: 1000+ downloads on crates.io within 6 months **of the official 1.0 public release**
- **Integration Time**: New developers can send encrypted messages in <10 minutes
- **Framework Support**: Works with Axum, Actix, Warp, and other frameworks
- **Reliability**: 99.9% message delivery success rate
- **Security**: Zero critical vulnerabilities in **mandatory third-party security audit**
- **Maintainability**: SDK issues resolved within 48 hours, core protocol issues within 1 week

### 1.4 Project Scope, Phasing, and Strategy

This project encompasses two primary engineering efforts:
1. **Core Protocol Implementation:** The development of a new, from-scratch Rust implementation of the Signal Protocol (`indidus_signal_protocol`).
2. **Client/Server SDK Development:** The creation of the user-facing `indidus_e2ee_client` and `indidus_e2ee_server` crates that consume the core protocol library.

Given the complexity and security-critical nature of the core protocol, development will proceed in distinct phases:
- **Phase 1: Core Protocol Development** (6-9 months): Implementation of the Signal Protocol with comprehensive testing and internal validation.
- **Phase 2: SDK Integration** (3-4 months): Development of client and server libraries that consume the core protocol.
- **Phase 3: Security Audit and Hardening** (2-3 months): Professional third-party security audit and remediation of findings. The `1.0` production release is contingent on the successful completion of this audit.

**Total Project Timeline**: 11-16 months from start to production release.

**Resource Requirements**: This is a specialized, security-critical project requiring dedicated cryptographic expertise and significantly more resources than a typical SDK development effort.

## 2. Target Users & Use Cases

### 2.1 Primary Users

#### Enterprise Backend Developers
- **Need**: Secure inter-service communication
- **Pain Point**: Complex crypto libraries and relay networking
- **Solution**: Drop-in encrypted messaging with minimal configuration and stateless design

#### Desktop Application Developers  
- **Need**: Secure file sharing between desktop apps
- **Pain Point**: Complex state management and persistence
- **Solution**: Reliable relay-based file transfer with user-controlled state management

#### Mobile App Developers
- **Need**: Cross-platform encrypted messaging
- **Pain Point**: State persistence across app lifecycles
- **Solution**: Efficient server-relay architecture with externalized state management

### 2.2 Use Cases

#### UC-001: Client - Secure Messaging with State Management
```rust
// Send encrypted message with user-managed state
use indidus_e2ee_client::{Client, ClientConfig, PeerId};

let config = ClientConfig {
    server_url: "https://myserver.com".to_string(),
    endpoints: EndpointConfig {
        websocket: "/api/ws".to_string(),
        upload_chunk: "/api/upload".to_string(),
        download_chunk: "/api/download".to_string(),
        ..Default::default()
    },
    peer_id: PeerId::new("alice"),
    ..Default::default()
};

// Load previous state (user's responsibility)
let saved_state = load_state_from_storage().await?; // User implements this

let client = Client::new(config, saved_state).await?;
client.send_message(&PeerId::new("bob"), "Confidential data").await?;

// Save state after operations (user's responsibility)
let state_blob = client.save_state();
save_state_to_storage(state_blob).await?; // User implements this
```

#### UC-002: Client - Large File Transfer with State Persistence
```rust
// Send 5GB file with progress tracking and state management
let mut transfer = client.send_file(&PeerId::new("bob"), Path::new("dataset.zip")).await?;
while let Some(progress) = transfer.progress().await {
    println!("Progress: {}%", progress.percentage);
    
    // Periodically save state during long transfers
    if progress.percentage % 10.0 == 0.0 {
        let state_blob = client.save_state();
        save_state_to_storage(state_blob).await?;
    }
}

// Save final state
let state_blob = client.save_state();
save_state_to_storage(state_blob).await?;
```

#### UC-003: Client - Real-time Communication with State Management
```rust
// Pure WebSocket-based real-time communication with state persistence
// All messages and files are received through WebSocket connection
loop {
    match client.receive().await? {
        Event::Message { from, content, .. } => {
            handle_message(from, content);
            // State should be persisted periodically or on application exit, not on every event.
        },
        Event::File { from, name, path, .. } => {
            save_file(name, path); // File saved to temporary path, not in memory
            // State should be persisted periodically or on application exit, not on every event.
        },
        Event::ConnectionStatus { status } => handle_connection_change(status),
        Event::DeliveryReceipt { message_id, status } => handle_delivery_confirmation(message_id, status),
        _ => {}
    }
}
```

#### UC-004: Server - Framework Integration (Axum)
```rust
use indidus_e2ee_server::{ServerState, handle_websocket_connection};
use axum::{Router, routing::get, extract::{State, WebSocketUpgrade}, response::Response};

#[tokio::main]
async fn main() {
    let server_state = ServerState::new().await;
    
    let app = Router::new()
        .route("/api/ws", get(websocket_handler))
        .route("/api/upload", post(upload_handler))
        .route("/api/download/:chunk_id", get(download_handler))
        .with_state(server_state);
        
    // User handles HTTP server setup
    axum::Server::bind(&"0.0.0.0:3000".parse().unwrap())
        .serve(app.into_make_service())
        .await.unwrap();
}

async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(state): State<ServerState>,
    headers: HeaderMap,
) -> Response {
    // User handles authentication their way
    let user_id = authenticate_user(&headers).unwrap();
    
    // Library handles WebSocket connection and message routing
    ws.on_upgrade(move |socket| handle_websocket_connection(socket, state, user_id))
}
```

#### UC-005: Server - Framework Integration (Actix)
```rust
use indidus_e2ee_server::{ServerState, handle_websocket_connection};
use actix_web::{web, App, HttpServer, HttpRequest, HttpResponse, Result};
use actix_web_actors::ws;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    let server_state = web::Data::new(ServerState::new().await);
    
    HttpServer::new(move || {
        App::new()
            .app_data(server_state.clone())
            .route("/api/ws", web::get().to(websocket_handler))
            .route("/api/upload", web::post().to(upload_handler))
    })
    .bind("127.0.0.1:8080")?
    .run()
    .await
}

async fn send_handler(
    state: web::Data<ServerState>,
    payload: web::Json<SendMessageRequest>
) -> HttpResponse {
    // User's custom auth logic
    // Then call library function
    match handle_send_message(&state, payload.into_inner()).await {
        Ok(response) => HttpResponse::Ok().json(response),
        Err(e) => HttpResponse::InternalServerError().json(e),
    }
}
```

#### UC-006: Custom Endpoints and Authentication with State Management
```rust
// Client with custom endpoints and user-managed state
let config = ClientConfig {
    server_url: "https://myapi.com".to_string(),
    endpoints: EndpointConfig {
        websocket: "/custom/messaging/ws".to_string(),
        upload_chunk: "/custom/files/upload".to_string(),
        download_chunk: "/custom/files/download".to_string(),
    },
    // User adds their own auth headers
    auth_headers: vec![
        ("Authorization".to_string(), "Bearer my_jwt_token".to_string()),
        ("X-API-Key".to_string(), "my_api_key".to_string()),
    ],
    peer_id: PeerId::new("alice"),
    ..Default::default()
};

// Load state from user's preferred storage
let saved_state = load_from_database("alice").await?; // User implements this
let client = Client::new(config, saved_state).await?;

// ... perform operations ...

// Save state to user's preferred storage
let state_blob = client.save_state();
save_to_database("alice", state_blob).await?; // User implements this
```

## 3. Functional Requirements

### 3.1 Core Messaging (Priority: P0)

#### FR-001: Text Message Encryption
- **Requirement**: All text messages must be end-to-end encrypted
- **Implementation**: ChaCha20-Poly1305 with X25519 key exchange
- **Validation**: Server cannot decrypt message content
- **Limits**: Maximum 64KB per message

#### FR-002: Message Delivery
- **Requirement**: Reliable message delivery with acknowledgments
- **Implementation**: Pure WebSocket with automatic retry logic and delivery receipts
- **Validation**: 99.9% delivery success rate under normal conditions
- **Timeout**: 30 seconds for delivery confirmation

#### FR-003: Offline Message Storage
- **Requirement**: Messages to offline peers stored on server
- **Implementation**: Encrypted message queue per peer ID
- **Validation**: Messages delivered when peer comes online
- **Retention**: 30 days maximum storage

#### FR-003a: Stateless Client Architecture
- **Requirement**: Client library must not perform any storage I/O operations
- **Implementation**: All cryptographic state returned as opaque `Vec<u8>` blob via `save_state()`
- **Validation**: Library user is responsible for persisting and loading state
- **Rationale**: Supports environments where processes are not long-lived (mobile apps, serverless functions)
- **Performance Warning**: The `save_state()` operation encapsulates the entire cryptographic session. Developers should avoid calling it in tight loops or after every network event, as the associated I/O for persistence can be slow. It is recommended to save state periodically, after a batch of user actions, or during application lifecycle events (e.g., shutdown, pause).

### 3.2 File Transfer (Priority: P0)

#### FR-004: Large File Support
- **Requirement**: Support files up to 10GB
- **Implementation**: Chunked streaming with 1MB chunks
- **Validation**: Memory usage stays under 100MB during transfer
- **Performance**: >10MB/s on local network

#### FR-005: File Integrity
- **Requirement**: Verify file integrity after transfer
- **Implementation**: BLAKE3 hash verification
- **Validation**: Reject files with mismatched checksums
- **Error Handling**: Automatic retry on corruption

#### FR-006: Transfer Progress
- **Requirement**: Real-time progress tracking for file transfers
- **Implementation**: Progress channel with percentage and transfer rate
- **Validation**: Progress updates at least every 1MB transferred
- **Cancellation**: Support transfer cancellation

#### FR-007: File Encryption
- **Requirement**: All files encrypted end-to-end
- **Implementation**: ChaCha20-Poly1305 per chunk with unique nonces
- **Validation**: Server cannot access file content
- **Performance**: Encryption overhead <10%

### 3.3 Connection Management (Priority: P1)

#### FR-008: Automatic Reconnection
- **Requirement**: Automatically reconnect on network failures
- **Implementation**: Exponential backoff with jitter
- **Validation**: Reconnect within 30 seconds of network restoration
- **Configuration**: Configurable retry attempts and timeouts

#### FR-009: Connection Status
- **Requirement**: Real-time connection status monitoring
- **Implementation**: WebSocket heartbeat with status events
- **Validation**: Status updates within 5 seconds of changes
- **Events**: Connected, disconnected, reconnecting states

#### FR-010: Peer Discovery
- **Requirement**: Discover online peers
- **Implementation**: Server maintains peer presence registry
- **Validation**: Peer status updates within 10 seconds
- **Privacy**: The server provides a mechanism to query a peer's online status. The application developer is responsible for implementing access control (e.g., a contact list) in their own API endpoints to determine who can query whom.

### 3.4 Security & Cryptography (Priority: P0)

#### FR-011: Key Exchange
- **Requirement**: Automatic secure key exchange between peers
- **Implementation**: X25519 Elliptic Curve Diffie-Hellman
- **Validation**: Perfect Forward Secrecy for all communications

#### FR-012: Authentication
- **Requirement**: Authenticate peers to prevent impersonation
- **Implementation**: Ed25519 digital signatures
- **Validation**: Reject messages with invalid signatures
- **Key Management**: Automatic key generation and storage

#### FR-013: Memory Security
- **Requirement**: Secure handling of cryptographic material
- **Implementation**: Zeroize sensitive data after use
- **Validation**: No plaintext keys in memory dumps
- **Protection**: Constant-time operations to prevent timing attacks

### 3.5 Developer Experience (Priority: P1)

#### FR-014: Simple API
- **Requirement**: Minimal API surface for common operations
- **Implementation**: 5 core methods: new, save_state, send_message, send_file, receive (all via WebSocket)
- **Validation**: New developers can send messages in <10 minutes
- **Documentation**: Comprehensive examples and tutorials with state management patterns

#### FR-015: Error Handling
- **Requirement**: Clear, actionable error messages
- **Implementation**: Structured error types with context
- **Validation**: All errors include suggested remediation
- **Logging**: Detailed debug logs for troubleshooting

#### FR-016: Configuration
- **Requirement**: Sensible defaults with customization options
- **Implementation**: ClientConfig struct with builder pattern
- **Validation**: Works out-of-box with minimal configuration
- **Flexibility**: All timeouts and limits configurable

## 4. Non-Functional Requirements

### 4.1 Performance Requirements

#### NFR-001: Latency
- **Message Delivery**: <500ms via server relay
- **Connection Establishment**: <2 seconds
- **File Transfer Initiation**: <1 second

#### NFR-002: Throughput
- **Message Rate**: >100 messages/second per peer
- **File Transfer**: >10MB/s on local network, >1MB/s over internet
- **Concurrent Connections**: Server supports 1000+ simultaneous peers

#### NFR-003: Resource Usage
- **Memory**: <100MB per client instance during file transfers
- **CPU**: <5% during idle, <20% during active transfers
- **Battery**: Optimized for mobile with efficient WebSocket usage

### 4.2 Reliability Requirements

#### NFR-004: Availability
- **Client Library**: 99.99% uptime (library doesn't crash)
- **Message Delivery**: 99.9% success rate under normal conditions
- **Error Recovery**: Automatic recovery from transient failures

#### NFR-005: Data Integrity
- **Message Integrity**: 100% detection of corrupted messages
- **File Integrity**: 100% detection of corrupted files
- **Cryptographic Integrity**: No successful attacks on encryption

### 4.3 Security Requirements

#### NFR-006: Encryption Standards
- **Symmetric Encryption**: ChaCha20-Poly1305 (IETF variant)
- **Key Exchange**: X25519 Elliptic Curve Diffie-Hellman
- **Digital Signatures**: Ed25519
- **Hashing**: BLAKE3 for file integrity

#### NFR-007: Security Properties
- **Confidentiality**: Only intended recipients can read messages
- **Authenticity**: Messages verified to be from claimed sender
- **Integrity**: Tampering with messages/files is detected
- **Forward Secrecy**: Past communications secure if keys compromised

#### NFR-008: Threat Model
- **Trusted**: Client devices and their local storage
- **Untrusted**: Network, relay server, other peers
- **Assumptions**: Secure initial key exchange, trusted client software

### 4.4 Platform Requirements

#### NFR-009: Rust Compatibility
- **Minimum Version**: Rust 1.70.0
- **Edition**: 2021
- **Features**: async/await, const generics
- **Dependencies**: Pure Rust preferred, minimal C dependencies

#### NFR-010: Platform Support
- **Tier 1**: Linux x86_64, Windows x86_64, macOS x86_64/ARM64, Android ARM64, iOS ARM64
- **Tier 2**: WebAssembly (future consideration)
- **Cross-Platform**: Generic Rust core library compatible with any platform supporting Rust

#### NFR-011: Cross-Platform Features
- **Networking**: Works behind NAT/firewalls
- **File System**: Handles different path separators and permissions
- **Crypto**: Consistent behavior across all platforms

#### NFR-012: Third-Party Security Audit
- **Requirement**: The `indidus_signal_protocol` core library must undergo a comprehensive third-party security audit before the 1.0 release
- **Validation**: Public audit report with all critical and high-severity findings resolved
- **Rationale**: From-scratch cryptographic implementations require independent validation to ensure security correctness and identify potential vulnerabilities that internal testing may miss

## 5. Technical Architecture

### 5.1 System Architecture

```
┌─────────────────┐    HTTPS         ┌─────────────────┐    HTTPS         ┌─────────────────┐
│   Client A      │◄─────────────────►│ User's Server   │◄─────────────────►│   Client B      │
│                 │                   │                 │                   │                 │
│ ┌─────────────┐ │                   │ ┌─────────────┐ │                   │ ┌─────────────┐ │
│ │indidus_e2ee_│ │                   │ │User's HTTP  │ │                   │ │indidus_e2ee_│ │
│ │client       │ │                   │ │Framework    │ │                   │ │client       │ │
│ │(stateless)  │ │                   │ │   +         │ │                   │ │(stateless)  │ │
│ └─────────────┘ │                   │ │indidus_e2ee_│ │                   │ └─────────────┘ │
└─────────────────┘                   │ │server lib   │ │                   └─────────────────┘
                                      │ └─────────────┘ │
                                      └─────────────────┘

Clean Separation:
- indidus_signal_protocol: Core cryptographic protocol implementation (foundational layer)
- indidus_e2ee_client: Handles crypto wrapper, WebSocket connections, stateless design
- indidus_e2ee_server: Provides pure functions for message handling
- User's Server: Handles WebSocket connections, HTTP routing for file transfers, auth, business logic, storage backend
- User's App: Handles state persistence and loading for client library
```

### 5.2 Component Architecture

#### Core Protocol Library (`indidus_signal_protocol`)
```
indidus_signal_protocol/
├── Cargo.toml
├── src/
│   ├── lib.rs              # Public API for Signal Protocol (200 lines)
│   ├── x3dh.rs             # X3DH key agreement protocol (600 lines)
│   ├── double_ratchet.rs   # Double Ratchet implementation (800 lines)
│   ├── ratchet/
│   │   ├── mod.rs          # Ratchet module organization (100 lines)
│   │   ├── root_chain.rs   # Root chain KDF ratchet (300 lines)
│   │   ├── sending_chain.rs # Sending chain management (250 lines)
│   │   └── receiving_chain.rs # Receiving chain management (300 lines)
│   ├── crypto/
│   │   ├── mod.rs          # Crypto primitives organization (100 lines)
│   │   ├── keys.rs         # Key generation and management (400 lines)
│   │   ├── encryption.rs   # ChaCha20-Poly1305 operations (200 lines)
│   │   └── signatures.rs   # Ed25519 signature operations (150 lines)
│   ├── state.rs            # Protocol state management (500 lines)
│   ├── errors.rs           # Protocol-specific error types (200 lines)
│   └── types.rs            # Core protocol data types (300 lines)
├── examples/
│   ├── basic_session.rs    # Simple two-party session example
│   └── key_exchange.rs     # X3DH key exchange example
└── tests/
    ├── x3dh_tests.rs       # X3DH protocol tests
    ├── ratchet_tests.rs    # Double Ratchet tests
    ├── crypto_tests.rs     # Cryptographic primitive tests
    └── integration_tests.rs # Full protocol integration tests

Total: ~4200 lines

Key Design Principles:
- Pure cryptographic implementation with no I/O operations
- Comprehensive state serialization for stateless usage
- Extensive test coverage for all protocol components
- Memory-safe implementation of all cryptographic operations
```

#### Client Library (`indidus_e2ee_client`)
```
indidus_e2ee_client/
├── Cargo.toml
├── src/
│   ├── lib.rs              # Public API with state management (250 lines)
│   ├── client.rs           # Stateless client implementation (600 lines)
│   ├── crypto.rs           # Wrapper around `indidus_signal_protocol` (200 lines)
│   ├── transport.rs        # WebSocket transport (400 lines)
│   ├── config.rs           # Configuration management (300 lines)
│   ├── file_transfer.rs    # File chunking and transfer (400 lines)
│   ├── state.rs            # State serialization/deserialization (200 lines)
│   ├── errors.rs           # Error types and handling (200 lines)
│   └── types.rs            # Data types and serialization (200 lines)
├── examples/
│   ├── basic_chat.rs       # Simple messaging with state management
│   ├── file_sharing.rs     # File transfer with state persistence
│   └── state_management.rs # State persistence patterns
└── tests/
    ├── crypto_tests.rs     # Cryptographic tests
    ├── state_tests.rs      # State management tests
    └── integration_tests.rs # End-to-end tests

Total: ~2750 lines
```

#### Server Library (`indidus_e2ee_server`)
```
indidus_e2ee_server/
├── Cargo.toml
├── src/
│   ├── lib.rs              # Public API (150 lines)
│   ├── handlers.rs         # Message handling functions (400 lines)
│   ├── storage.rs          # Storage abstraction (300 lines)
│   ├── state.rs            # Server state management (200 lines)
│   ├── types.rs            # Request/response types (200 lines)
│   └── errors.rs           # Error types (150 lines)
├── examples/
│   ├── axum_server.rs      # Axum integration example
│   ├── actix_server.rs     # Actix integration example
│   └── warp_server.rs      # Warp integration example
└── tests/
    ├── handler_tests.rs    # Handler function tests
    └── storage_tests.rs    # Storage tests

Total: ~1400 lines
```

### 5.3 Data Flow

#### Message Sending Flow
1. **Client A**: `send_message("bob", "hello")`
2. **Encrypt**: Generate shared secret with Bob's public key
3. **Encrypt**: ChaCha20-Poly1305 encrypt message
4. **Send**: WebSocket message with encrypted payload
5. **Server**: Store encrypted message in Bob's queue
6. **Deliver**: Immediate WebSocket delivery to Bob (if online)
7. **Client B**: Receive encrypted message via WebSocket
8. **Decrypt**: ChaCha20-Poly1305 decrypt with shared secret
9. **Deliver**: `Event::Message` to application
10. **Acknowledge**: WebSocket delivery receipt back to Client A

#### File Transfer Flow
1. **Client A**: `send_file("bob", "large.zip")`
2. **Chunk**: Split file into 1MB encrypted chunks
3. **Initiate**: WebSocket message to initiate file transfer
4. **Upload**: Stream chunks via HTTP with progress tracking
5. **Server**: Store encrypted chunks temporarily
6. **Notify**: WebSocket notification to Bob with file metadata
7. **Client B**: Download chunks via HTTP (fallback to WebSocket for small files)
8. **Progress**: Real-time progress updates via WebSocket
9. **Reassemble**: Combine chunks and verify BLAKE3 checksum
10. **Deliver**: `Event::File` to application
11. **Acknowledge**: WebSocket completion receipt back to Client A

### 5.4 Security Architecture

#### Trust Model: Trust On First Use (TOFU)
The system implements a simplified **Trust On First Use (TOFU)** security model for optimal user experience:

```
TOFU Key Exchange Process:
1. Client A requests to communicate with Client B.
2. The server provides Client B's public identity key.
3. On first encounter, Client A trusts and stores a hash of this identity key.
4. On subsequent connections, Client A re-fetches the identity key and verifies it against the stored hash. If it matches, the session proceeds.
5. If the identity key does not match, the library must refuse to establish a session and return a critical error (e.g., `Error::PeerIdentityChanged`), delegating the handling of this security event to the developer.
6. Ephemeral key rotation during a valid session is handled automatically by the protocol.
```

#### Key Management
The system implements Signal Protocol-style Double Ratchet for forward secrecy and post-compromise security:

```
Initial Key Exchange (X3DH Protocol):
1. Each peer generates identity keypair (Ed25519)
2. Each peer generates signed prekey (X25519)
3. Each peer generates ephemeral prekeys (X25519)
4. Public keys published to server
5. Initiator performs triple Diffie-Hellman:
   - ECDH(IK_A, SPK_B)  # Identity key to signed prekey
   - ECDH(EK_A, IK_B)   # Ephemeral key to identity key
   - ECDH(EK_A, SPK_B)  # Ephemeral key to signed prekey
6. Initial root key derived from combined secrets

Double Ratchet Protocol:
├── Root Chain (KDF Ratchet)
│   ├── Root Key: Updated with each DH ratchet step
│   ├── Chain Keys: Derived for sending/receiving chains
│   └── HKDF: Key derivation function
├── Sending Chain
│   ├── Chain Key: Updated with each sent message
│   ├── Message Keys: Derived for each message
│   └── Forward Secrecy: Keys deleted after use
├── Receiving Chain
│   ├── Chain Key: Updated with each received message
│   ├── Message Keys: Derived for each message
│   └── Out-of-order: Support for delayed messages
└── DH Ratchet
    ├── New X25519 keypair per conversation turn
    ├── Root key updated with new shared secret
    └── Chain keys reset for new epoch

Key Rotation Schedule:
- Message Keys: Generated and deleted per message
- Chain Keys: Updated with each message in chain
- DH Keys: Rotated on each conversation direction change
- Root Key: Updated with each DH ratchet step
- Identity Keys: Long-term, rotated annually
```

#### Encryption Process
```
Message Encryption (Double Ratchet):
1. Check if DH ratchet step needed (first message or direction change)
2. If DH ratchet needed:
   - Generate new X25519 ephemeral keypair
   - Perform ECDH with recipient's current public key
   - Update root key using HKDF(root_key, dh_output)
   - Derive new sending chain key from updated root key
3. Derive message key from current sending chain key
4. Update sending chain key for next message
5. Encrypt message with ChaCha20-Poly1305 using message key
6. Generate message header with:
   - Current DH public key
   - Previous chain length
   - Message number in current chain
7. Send: message_header + encrypted_data + MAC

Message Decryption (Double Ratchet):
1. Parse message header to get DH key and chain info
2. If new DH key received:
   - Perform DH ratchet step
   - Update root key and derive new receiving chain key
3. Derive message key from receiving chain key
4. Decrypt message with ChaCha20-Poly1305
5. Update receiving chain key for next message
6. Handle out-of-order messages using skipped message keys
7. Delete used message keys immediately

Security Properties:
- Forward Secrecy: Past messages secure if current keys compromised
- Post-Compromise Security: Future messages secure after key rotation
- Break-in Recovery: Security restored after DH ratchet step
- Deniability: Messages cannot be proven to come from sender
```

### 5.5 Security Assumptions and Developer Responsibilities

#### Critical Security Assumptions
This architecture makes specific security assumptions that developers **must** understand and address:

#### 5.5.1 Untrusted Server, Trusted Channel
- **Server Trust Model**: The `indidus_e2ee_server` is considered **untrusted** regarding message content
- **Channel Trust Model**: The communication channel between client and server is assumed to be **secure**
- **Implication**: Server cannot read message content, but the connection to the server must be protected

#### 5.5.2 Man-in-the-Middle (MitM) Vulnerability
- **Risk**: This architecture is **vulnerable to MitM attacks** during initial key exchange
- **Attack Vector**: A compromised or malicious server could present attacker's keys instead of legitimate recipient's keys
- **Impact**: Attacker could decrypt all communications if they control the initial key exchange

#### 5.5.3 Developer Security Responsibilities
**It is the app developer's sole responsibility to secure the client-server channel.** This includes:

1. **TLS 1.3 Implementation**: Use modern TLS with strong cipher suites
2. **Certificate Validation**: Properly validate server certificates
3. **Certificate Pinning**: For high-security applications, implement TLS certificate pinning to prevent sophisticated MitM attacks
4. **Network Security**: Ensure secure network configuration and monitoring

#### 5.5.4 Recommended Security Practices
To implement certificate pinning, developers should configure their chosen HTTP client (e.g., `reqwest`) accordingly. The `indidus_e2ee_client` library will provide an API to accept a pre-configured HTTP client instance, thus giving the developer full control over transport layer security without adding complexity to the library's own API.

#### 5.5.5 Security Trade-offs
- **Simplified UX**: No manual key verification improves user experience
- **Reduced Security**: Vulnerable to sophisticated MitM attacks
- **Developer Responsibility**: Security burden shifted to proper channel protection
- **Suitable For**: Applications where ease of use outweighs maximum security paranoia
```

## 6. Dependencies & Technology Stack

### 6.1 Client Dependencies (`indidus_e2ee_client`)
```toml
[dependencies]
# Async runtime
tokio = { version = "1", features = ["rt", "net", "time", "macros"] }

# Core Signal Protocol implementation (our from-scratch implementation)
indidus_signal_protocol = { path = "../indidus_signal_protocol" }

# Additional utilities
rand = "0.8"                 # Secure random generation
blake3 = "1.5"               # Fast hashing for file integrity

# WebSocket client and HTTP client for file transfers
tokio-tungstenite = { version = "0.20", features = ["native-tls"] }
reqwest = { version = "0.11", features = ["json", "stream"] }

# Serialization & Utilities
serde = { version = "1", features = ["derive"] }
bincode = "1"                # Binary serialization
uuid = { version = "1", features = ["v4"] }
thiserror = "1"              # Error handling
tracing = "0.1"              # Logging
futures = "0.3"              # Stream utilities
```

### 6.1a Core Protocol Dependencies (`indidus_signal_protocol`)
```toml
[dependencies]
# Cryptography (Pure Rust) - Low-level primitives for from-scratch implementation
chacha20poly1305 = "0.10"    # Authenticated encryption
x25519-dalek = "2.0"         # Key exchange
ed25519-dalek = "2.0"        # Digital signatures
hkdf = "0.12"                # Key derivation function
rand = "0.8"                 # Secure random generation

# Serialization & Utilities
serde = { version = "1", features = ["derive"] }
bincode = "1"                # Binary serialization
thiserror = "1"              # Error handling
zeroize = "1"                # Secure memory clearing
```

### 6.2 Server Dependencies (`indidus_e2ee_server`)
```toml
[dependencies]
# Minimal async runtime (no networking)
tokio = { version = "1", features = ["rt", "sync", "time", "macros"] }

# Serialization & Utilities (no crypto - server doesn't decrypt)
serde = { version = "1", features = ["derive"] }
bincode = "1"                # Binary serialization
uuid = { version = "1", features = ["v4"] }
thiserror = "1"              # Error handling
tracing = "0.1"              # Logging

# Storage abstraction (optional backends)
[features]
default = ["memory-storage"]
memory-storage = []
redis-storage = ["redis"]
postgres-storage = ["sqlx"]

[dependencies.redis]
version = "0.24"
optional = true

[dependencies.sqlx]
version = "0.7"
features = ["postgres", "runtime-tokio-rustls"]
optional = true
```

### 6.2 Development Dependencies
```toml
[dev-dependencies]
tokio-test = "0.4"           # Async testing
criterion = "0.5"            # Benchmarking
proptest = "1"               # Property testing
tempfile = "3"               # Temporary files for tests
```

### 6.3 Platform-Specific Dependencies
```toml
# Mobile platforms (via uniffi)
[target.'cfg(target_os = "android")'.dependencies]
jni = "0.21"

[target.'cfg(target_os = "ios")'.dependencies]
objc = "0.2"
```

## 7. Framework Integration Architecture

### 7.1 Design Philosophy

The architecture prioritizes **clean separation of concerns** between cryptographic functionality and WebSocket/HTTP implementation. This approach provides maximum flexibility while maintaining security and simplicity.

#### Core Principles
- **Framework Agnostic**: Server library provides pure functions, not HTTP handlers
- **User Control**: Developers choose their own endpoints, authentication, and routing
- **Clean Integration**: Library functions integrate seamlessly into existing APIs
- **No Lock-in**: Works with any Rust web framework (Axum, Actix, Warp, etc.)

### 7.2 Client-Side Configuration

#### Configurable Endpoints
The client library allows complete customization of server endpoints, enabling integration with any API design:

```rust
use indidus_e2ee_client::{Client, ClientConfig, EndpointConfig};

let config = ClientConfig {
    server_url: "https://myapi.com".to_string(),
    endpoints: EndpointConfig {
        websocket: "/my/custom/ws".to_string(),
        upload_chunk: "/my/custom/upload".to_string(),
        download_chunk: "/my/custom/download".to_string(),
        // Users define their own paths
    },
    peer_id: PeerId::new("alice"),
    // ... other config
};

let client = Client::new(config).await?;
```

#### Benefits
- **WebSocket Endpoints**: `/api/v1/ws`, `/messaging/realtime`
- **Custom Paths**: `/my-app/chat/ws`, `/secure/communications`
- **Protocol Flexibility**: Pure WebSocket for real-time messaging, HTTP for file transfers
- **Legacy Integration**: Fit into existing WebSocket infrastructure

### 7.3 Server-Side Integration Patterns

#### Pure Function Architecture
The server library provides pure functions that handle cryptographic operations and data management without WebSocket/HTTP coupling:

```rust
use indidus_e2ee_server::{ServerState, handle_websocket_message};

// Server state (storage, queues, etc.)
let server_state = ServerState::new().await;

// Pure function calls - no WebSocket/HTTP dependencies
let result = handle_websocket_message(&server_state, message).await?;
```

#### Framework Integration Examples

##### Axum Integration
```rust
use axum::{Router, extract::{State, WebSocketUpgrade}, http::HeaderMap, response::Response};
use indidus_e2ee_server::{ServerState, handle_websocket_connection};

async fn my_websocket_handler(
    ws: WebSocketUpgrade,
    State(server_state): State<ServerState>,
    headers: HeaderMap,
) -> Response {
    // Custom authentication
    let user_id = authenticate_user(&headers).unwrap();
    
    // Library handles WebSocket connection and message routing
    ws.on_upgrade(move |socket| handle_websocket_connection(socket, server_state, user_id))
}

let app = Router::new()
    .route("/my/custom/ws", get(my_websocket_handler))
    .route("/my/custom/upload", post(my_upload_handler))
    .with_state(server_state);
```

##### Actix Web Integration
```rust
use actix_web::{web, App, HttpRequest, HttpResponse};
use indidus_e2ee_server::{ServerState, handle_send_message};

async fn send_handler(
    req: HttpRequest,
    data: web::Json<SendMessageRequest>,
    state: web::Data<ServerState>
) -> Result<HttpResponse, Error> {
    // Custom auth with Actix extractors
    let user = authenticate_actix(&req)?;
    
    // Same library function
    let result = handle_send_message(&state, data.into_inner()).await?;
    
    Ok(HttpResponse::Ok().json(result))
}

let app = App::new()
    .app_data(web::Data::new(server_state))
    .route("/send", web::post().to(send_handler));
```

##### Warp Integration
```rust
use warp::{Filter, reply};
use p2p_e2ee_server::{ServerState, handle_send_message};

let send_route = warp::post()
    .and(warp::path("send"))
    .and(warp::header::headers_cloned())
    .and(warp::body::json())
    .and(with_state(server_state.clone()))
    .and_then(|headers, body, state| async move {
        // Custom auth
        let user = authenticate_warp(&headers)?;
        
        // Library function
        let result = handle_send_message(&state, body).await?;
        
        Ok(reply::json(&result))
    });
```

### 7.4 Authentication Integration

#### Framework-Agnostic Design
The library functions accept pure data types, allowing any authentication mechanism:

```rust
// JWT Authentication
async fn jwt_protected_handler(
    headers: HeaderMap,
    Json(request): Json<SendMessageRequest>
) -> Result<Json<SendMessageResponse>, StatusCode> {
    let claims = verify_jwt(extract_token(&headers)?)?;
    let result = handle_send_message(&state, request).await?;
    Ok(Json(result))
}

// API Key Authentication
async fn api_key_handler(
    headers: HeaderMap,
    Json(request): Json<SendMessageRequest>
) -> Result<Json<SendMessageResponse>, StatusCode> {
    let user = verify_api_key(&headers)?;
    let result = handle_send_message(&state, request).await?;
    Ok(Json(result))
}

// OAuth Integration
async fn oauth_handler(
    Extension(oauth_user): Extension<OAuthUser>,
    Json(request): Json<SendMessageRequest>
) -> Result<Json<SendMessageResponse>, StatusCode> {
    let result = handle_send_message(&state, request).await?;
    Ok(Json(result))
}
```

### 7.5 Data Types and Interfaces

#### Request/Response Types
The library defines clean data types without HTTP coupling:

```rust
#[derive(Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub to: String,
    pub encrypted_data: Vec<u8>,
    pub message_id: String,
    pub timestamp: u64,
}

#[derive(Serialize, Deserialize)]
pub struct SendMessageResponse {
    pub message_id: String,
    pub status: DeliveryStatus,
}

#[derive(Serialize, Deserialize)]
pub struct EncryptedMessage {
    pub from: String,
    pub encrypted_data: Vec<u8>,
    pub message_id: String,
    pub timestamp: u64,
}
```

#### Error Handling
```rust
#[derive(Debug, thiserror::Error)]
pub enum ServerError {
    #[error("Storage error: {0}")]
    Storage(String),
    
    #[error("Invalid message format: {0}")]
    InvalidMessage(String),
    
    #[error("Peer not found: {0}")]
    PeerNotFound(String),
}
```

### 7.6 Storage Backend Flexibility

#### Pluggable Storage
The server library supports multiple storage backends through feature flags:

```toml
[features]
default = ["memory-storage"]
memory-storage = []
redis-storage = ["redis"]
postgres-storage = ["sqlx"]
mongodb-storage = ["mongodb"]
```

#### Custom Storage Implementation
```rust
#[async_trait]
pub trait MessageStorage: Send + Sync {
    async fn store_message(&self, peer_id: &str, message: EncryptedMessage) -> Result<(), StorageError>;
    async fn get_messages(&self, peer_id: &str) -> Result<Vec<EncryptedMessage>, StorageError>;
    async fn delete_message(&self, message_id: &str) -> Result<(), StorageError>;
}

// Users can implement custom storage
struct CustomStorage { /* ... */ }

#[async_trait]
impl MessageStorage for CustomStorage {
    // Custom implementation
}
```

### 7.7 Advantages of This Architecture

#### 1. Maximum Flexibility
- **Endpoint Design**: Users control URL structure and HTTP methods
- **Authentication**: Integrate with any auth system (JWT, OAuth, API keys, custom)
- **Framework Choice**: Works with any Rust web framework
- **Business Logic**: Easy integration with existing application logic

#### 2. Clean Separation
- **Library Responsibility**: Cryptography, message handling, data formats
- **User Responsibility**: HTTP routing, authentication, business logic, storage choice
- **No Framework Lock-in**: Library doesn't depend on specific HTTP frameworks
- **Testability**: Pure functions are easy to unit test

#### 3. Easy Integration
```rust
// Integrate into existing endpoint
app.route("/existing/chat/send", post(|req| async {
    // Existing business logic
    validate_business_rules(&req)?;
    
    // Add encrypted messaging
    let result = handle_send_message(&state, req.into()).await?;
    
    // More existing logic
    update_analytics(&result)?;
    
    Ok(Json(result))
}));
```

#### 4. Future-Proof Design
- **New Frameworks**: Automatically compatible with future Rust web frameworks
- **Protocol Changes**: HTTP implementation changes don't affect crypto library
- **Scaling**: Easy to add load balancing, caching, rate limiting at HTTP layer
- **Monitoring**: Standard HTTP middleware works out of the box

This architecture ensures the library remains focused on its core competency (secure relay-based communication) while providing maximum flexibility for integration into diverse applications and infrastructure, with complete user control over state persistence and security channel protection.
