# Relay Server Deployment Guide

This comprehensive guide covers how to configure, integrate, and deploy the `indidus_e2ee_server` in production-ready environments. The Indidus E2EE Server acts as a secure relay for encrypted messages and coordinates file transfers between clients without having access to the plaintext content.

## Table of Contents

1. [Understanding the Server Architecture](#understanding-the-server-architecture)
2. [Server Configuration](#server-configuration)
3. [Integration Patterns](#integration-patterns)
4. [Framework-Specific Examples](#framework-specific-examples)
5. [Production Deployment](#production-deployment)
6. [Security Considerations](#security-considerations)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Troubleshooting](#troubleshooting)

## Understanding the Server Architecture

The Indidus E2EE Server is designed as a library that integrates into your existing web application rather than running as a standalone service. This design provides several advantages:

- **Flexible Integration**: Works with any Rust web framework (Axum, Warp, Actix, Rocket, etc.)
- **Shared Infrastructure**: Leverage your existing authentication, logging, and monitoring systems
- **Custom Business Logic**: Easily add application-specific features around the E2EE core
- **Resource Efficiency**: Single process handles both your application and E2EE relay functionality

### Core Components

The server library consists of several key components:

- **ServerConfig**: Configuration management for all server parameters
- **ServerState**: Manages client sessions, pre-keys, and message queues
- **Handlers**: WebSocket and HTTP handlers for different message types
- **Storage Services**: File chunk storage and retrieval functionality
- **Routing Table**: Manages peer-to-peer connection mapping

## Server Configuration

### Basic Configuration

The `ServerConfig` struct provides comprehensive configuration options for the E2EE server:

```rust
use indidus_e2ee_server::ServerConfig;

// Basic configuration with defaults
let config = ServerConfig::new();

// Production configuration with custom settings
let config = ServerConfig::with_address("0.0.0.0".to_string(), 8080)
    .with_max_connections(2000)
    .with_request_timeout(60)
    .with_max_request_size(10 * 1024 * 1024) // 10MB
    .with_logging(true, "info".to_string());
```

### Configuration Parameters

#### Network Settings

| Parameter | Description | Default | Production Recommendation |
|-----------|-------------|---------|---------------------------|
| `host` | Bind address | `"127.0.0.1"` | `"0.0.0.0"` for containers |
| `port` | Listen port | `8080` | Environment-dependent |
| `max_connections` | Concurrent connection limit | `1000` | Based on server capacity |
| `request_timeout_secs` | Request timeout | `30` | `60-120` for file transfers |
| `max_request_size` | Maximum request body size | `1MB` | `10MB+` for file uploads |

#### CORS Configuration

```rust
let config = ServerConfig::new()
    .with_cors(
        true, 
        vec![
            "https://myapp.com".to_string(),
            "https://app.myapp.com".to_string(),
        ]
    );
```

#### TLS Configuration

```rust
use std::path::PathBuf;

let config = ServerConfig::new()
    .with_tls(
        PathBuf::from("/etc/ssl/certs/server.crt"),
        PathBuf::from("/etc/ssl/private/server.key")
    );
```

#### Database Integration

```rust
use url::Url;

let database_url = Url::parse("postgresql://user:password@localhost/indidus_db")?;
let config = ServerConfig::new()
    .with_database(database_url);
```

#### Session Management

```rust
let config = ServerConfig::new()
    .with_session_cleanup(
        3600,  // Cleanup every hour
        86400  // Sessions expire after 24 hours
    )
    .with_prekey_limits(200); // Max 200 pre-keys per client
```

### Environment-Based Configuration

For production deployments, use environment variables or configuration files:

```rust
use std::env;
use std::path::PathBuf;

fn create_production_config() -> Result<ServerConfig, Box<dyn std::error::Error>> {
    let host = env::var("INDIDUS_HOST").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port: u16 = env::var("INDIDUS_PORT")
        .unwrap_or_else(|_| "8080".to_string())
        .parse()?;
    
    let mut config = ServerConfig::with_address(host, port)
        .with_max_connections(
            env::var("INDIDUS_MAX_CONNECTIONS")
                .unwrap_or_else(|_| "2000".to_string())
                .parse()?
        );
    
    // Configure TLS if certificates are provided
    if let (Ok(cert_path), Ok(key_path)) = (
        env::var("INDIDUS_TLS_CERT"),
        env::var("INDIDUS_TLS_KEY")
    ) {
        config = config.with_tls(
            PathBuf::from(cert_path),
            PathBuf::from(key_path)
        );
    }
    
    // Configure database if URL is provided
    if let Ok(db_url) = env::var("INDIDUS_DATABASE_URL") {
        config = config.with_database(db_url.parse()?);
    }
    
    Ok(config)
}
```

### Configuration File Support

Save and load configurations using JSON:

```rust
use std::path::PathBuf;

// Save configuration
let config = ServerConfig::new();
config.save_to_file(&PathBuf::from("config/indidus.json"))?;

// Load configuration
let config = ServerConfig::from_file(&PathBuf::from("config/indidus.json"))?;
```

## Integration Patterns

The Indidus E2EE Server follows a trait-based integration pattern that works with any Rust web framework. The core pattern involves:

1. **State Management**: Create and share server state across handlers
2. **Handler Integration**: Add WebSocket and HTTP handlers to your application
3. **Service Coordination**: Coordinate between different E2EE services

### Common Integration Steps

1. **Initialize Server State**:
```rust
use indidus_e2ee_server::{ServerConfig, server::ServerState};
use std::sync::Arc;

let config = ServerConfig::new();
let server_state = Arc::new(ServerState::new(config));
```

2. **Set Up Supporting Services**:
```rust
use indidus_e2ee_server::{
    handlers::RoutingTable,
    storage::{ChunkStorageService, StorageConfig}
};

let routing_table = Arc::new(RoutingTable::new());
let storage_service = Arc::new(ChunkStorageService::new(StorageConfig::default()));
```

3. **Add Handler Routes**:
```rust
use indidus_e2ee_server::handlers::{handle_websocket, handle_file_chunk};

// WebSocket handler for real-time messaging
// HTTP handler for file chunk operations
```

## Framework-Specific Examples

### Axum Integration

Axum's type-safe state management makes integration straightforward:

```rust
use anyhow::Result;
use axum::{
    extract::{State, WebSocketUpgrade},
    response::Response,
    routing::{get, post},
    Router,
};
use indidus_e2ee_server::{
    handlers::{handle_file_chunk, handle_websocket, RoutingTable},
    server::ServerState,
    storage::{ChunkStorageService, StorageConfig},
    ServerConfig,
};
use std::sync::Arc;

#[derive(Clone)]
struct AppState {
    server_state: Arc<ServerState>,
    storage_service: Arc<ChunkStorageService>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Configure the E2EE server
    let config = ServerConfig::with_address("0.0.0.0".to_string(), 3000)
        .with_max_connections(1000);

    // Initialize services
    let server_state = Arc::new(ServerState::new(config));
    let storage_service = Arc::new(ChunkStorageService::new(StorageConfig::default()));

    let app_state = AppState {
        server_state,
        storage_service,
    };

    // Create router with E2EE endpoints
    let app = Router::new()
        .route("/", get(health_check))
        .route("/ws", get(websocket_handler))
        .route("/v1/files/chunk", post(file_chunk_handler))
        .with_state(app_state);

    // Start the server
    let listener = tokio::net::TcpListener::bind("0.0.0.0:3000").await?;
    axum::serve(listener, app.into_make_service()).await?;

    Ok(())
}

async fn health_check() -> &'static str {
    "E2EE Server is running"
}

async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(_app_state): State<AppState>,
) -> Response {
    let routing_table = RoutingTable::new();
    
    ws.on_upgrade(move |socket| async move {
        if let Err(e) = handle_websocket(socket, routing_table).await {
            tracing::error!("WebSocket error: {}", e);
        }
    })
}

async fn file_chunk_handler(
    State(app_state): State<AppState>,
    multipart: axum::extract::Multipart,
) -> Result<
    axum::response::Json<indidus_e2ee_server::handlers::FileChunkResponse>,
    (
        axum::http::StatusCode,
        axum::response::Json<indidus_e2ee_server::handlers::FileChunkError>,
    ),
> {
    handle_file_chunk(
        axum::extract::State(app_state.storage_service),
        multipart
    ).await
}
```

### Warp Integration

Warp's functional filter approach requires careful state management:

```rust
use anyhow::Result;
use indidus_e2ee_server::{
    ServerConfig,
    server::ServerState,
    storage::{ChunkStorageService, StorageConfig},
    handlers::RoutingTable,
};
use std::sync::Arc;
use tokio::sync::Mutex;
use warp::Filter;

#[derive(Clone)]
pub struct AppState {
    pub server_state: Arc<Mutex<ServerState>>,
    pub storage_service: Arc<ChunkStorageService>,
    pub routing_table: Arc<Mutex<RoutingTable>>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize E2EE services
    let config = ServerConfig::with_address("0.0.0.0".to_string(), 3030);
    let server_state = Arc::new(Mutex::new(ServerState::new(config)));
    let storage_service = Arc::new(ChunkStorageService::new(StorageConfig::default()));
    let routing_table = Arc::new(Mutex::new(RoutingTable::new()));

    let app_state = AppState {
        server_state,
        storage_service,
        routing_table,
    };

    // Create state filter for dependency injection
    let state_filter = warp::any().map(move || app_state.clone());

    // Health check endpoint
    let health = warp::path::end()
        .and(warp::get())
        .map(|| "E2EE Server is running");

    // WebSocket endpoint
    let websocket = warp::path("ws")
        .and(warp::ws())
        .and(state_filter.clone())
        .map(|ws: warp::ws::Ws, state: AppState| {
            ws.on_upgrade(move |socket| handle_websocket_connection(socket, state))
        });

    // File chunk endpoint
    let file_chunk = warp::path!("v1" / "files" / "chunk")
        .and(warp::post())
        .and(warp::multipart::form())
        .and(state_filter)
        .and_then(handle_file_chunk_warp);

    // Combine all routes
    let routes = health
        .or(websocket)
        .or(file_chunk);

    warp::serve(routes)
        .run(([0, 0, 0, 0], 3030))
        .await;

    Ok(())
}

async fn handle_websocket_connection(
    socket: warp::ws::WebSocket,
    state: AppState
) {
    let routing_table = {
        let rt = state.routing_table.lock().await;
        rt.clone()
    };

    if let Err(e) = indidus_e2ee_server::handlers::handle_websocket(socket, routing_table).await {
        tracing::error!("WebSocket error: {}", e);
    }
}

async fn handle_file_chunk_warp(
    form: warp::multipart::FormData,
    _state: AppState
) -> Result<impl warp::Reply, warp::Rejection> {
    // Convert Warp's multipart to Axum's format and delegate
    // Implementation details would handle the conversion
    Ok(warp::reply::with_status(
        "File chunk processed",
        warp::http::StatusCode::OK
    ))
}
```

### Actix Web Integration

For Actix Web, use the app data pattern:

```rust
use actix_web::{web, App, HttpServer, HttpResponse, Result};
use indidus_e2ee_server::{ServerConfig, server::ServerState};
use std::sync::Arc;

#[derive(Clone)]
struct AppState {
    server_state: Arc<ServerState>,
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    let config = ServerConfig::with_address("0.0.0.0".to_string(), 8080);
    let server_state = Arc::new(ServerState::new(config));
    
    let app_state = AppState { server_state };

    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(app_state.clone()))
            .route("/", web::get().to(health_check))
            .route("/ws", web::get().to(websocket_handler))
            .route("/v1/files/chunk", web::post().to(file_chunk_handler))
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await
}

async fn health_check() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().body("E2EE Server is running"))
}

// WebSocket and file handlers would be implemented similarly
```

## Production Deployment

### Docker Deployment

Create a multi-stage Dockerfile for optimal production builds:

```dockerfile
# Build stage
FROM rust:1.75-slim as builder

WORKDIR /app
COPY . .

# Install dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -r -s /bin/false indidus

# Copy binary
COPY --from=builder /app/target/release/your-app /usr/local/bin/app

# Set ownership and permissions
RUN chown indidus:indidus /usr/local/bin/app
USER indidus

EXPOSE 8080

CMD ["app"]
```

### Docker Compose for Development

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - INDIDUS_HOST=0.0.0.0
      - INDIDUS_PORT=8080
      - INDIDUS_MAX_CONNECTIONS=2000
      - INDIDUS_DATABASE_URL=**************************************/indidus
      - RUST_LOG=info
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=indidus
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: indidus-e2ee-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: indidus-e2ee-server
  template:
    metadata:
      labels:
        app: indidus-e2ee-server
    spec:
      containers:
      - name: indidus-e2ee-server
        image: your-registry/indidus-e2ee-server:latest
        ports:
        - containerPort: 8080
        env:
        - name: INDIDUS_HOST
          value: "0.0.0.0"
        - name: INDIDUS_PORT
          value: "8080"
        - name: INDIDUS_MAX_CONNECTIONS
          value: "2000"
        - name: INDIDUS_DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: indidus-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: indidus-e2ee-service
spec:
  selector:
    app: indidus-e2ee-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

### Reverse Proxy Configuration

#### Nginx Configuration

```nginx
upstream indidus_backend {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    listen 443 ssl http2;
    server_name myapp.com;

    ssl_certificate /etc/ssl/certs/myapp.com.crt;
    ssl_certificate_key /etc/ssl/private/myapp.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # WebSocket upgrade headers
    location /ws {
        proxy_pass http://indidus_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket timeout settings
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
    }

    # File upload endpoints
    location /v1/files/ {
        proxy_pass http://indidus_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Large file upload settings
        client_max_body_size 100M;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Regular HTTP endpoints
    location / {
        proxy_pass http://indidus_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### Caddy Configuration

```caddyfile
myapp.com {
    reverse_proxy /ws* localhost:8080 {
        header_up Host {host}
        header_up X-Real-IP {remote}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}
    }
    
    reverse_proxy /v1/files/* localhost:8080 {
        header_up Host {host}
        header_up X-Real-IP {remote}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}
    }
    
    reverse_proxy * localhost:8080 {
        header_up Host {host}
        header_up X-Real-IP {remote}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}
    }
}
```

### TLS Termination

For production deployments, implement proper TLS:

1. **Application-Level TLS**: Configure TLS directly in your application
2. **Proxy-Level TLS**: Terminate TLS at the reverse proxy (recommended)
3. **Load Balancer TLS**: Use cloud provider load balancers

### Scaling Considerations

#### Horizontal Scaling

- **Stateless Design**: The E2EE server is designed to be stateless for easy scaling
- **Session Affinity**: Not required for basic messaging functionality
- **File Transfer Coordination**: Consider shared storage for file chunks across instances

#### Database Scaling

```rust
// Configure connection pooling
let config = ServerConfig::new()
    .with_database(database_url)
    .with_max_connections(50); // Per instance
```

#### Load Testing

Use tools like `wrk` or `artillery` to test WebSocket and HTTP endpoints:

```bash
# HTTP endpoint testing
wrk -t12 -c400 -d30s http://localhost:8080/health

# WebSocket testing with artillery
artillery run websocket-test.yml
```

## Security Considerations

### Network Security

1. **TLS Configuration**: Always use TLS 1.2+ in production
2. **CORS Policy**: Configure restrictive CORS origins
3. **Rate Limiting**: Implement rate limiting at the proxy level
4. **IP Whitelisting**: Restrict access to known client networks if applicable

### Application Security

1. **Input Validation**: All inputs are validated by the E2EE library
2. **Authentication**: Implement your own authentication layer
3. **Authorization**: Control access to WebSocket and file endpoints
4. **Audit Logging**: Log security-relevant events

### Infrastructure Security

1. **Container Security**: Use distroless or minimal base images
2. **Secret Management**: Use proper secret management for database credentials
3. **Network Isolation**: Use VPCs and security groups to isolate services
4. **Regular Updates**: Keep dependencies and base images updated

### Example Security Configuration

```rust
use indidus_e2ee_server::ServerConfig;

fn create_secure_config() -> ServerConfig {
    ServerConfig::with_address("127.0.0.1".to_string(), 8080) // Bind to localhost only
        .with_cors(
            true,
            vec!["https://myapp.com".to_string()] // Restrictive CORS
        )
        .with_max_connections(1000) // Prevent resource exhaustion
        .with_request_timeout(60) // Reasonable timeout
        .with_max_request_size(10 * 1024 * 1024) // 10MB limit
        .with_tls(
            "/etc/ssl/certs/server.crt".into(),
            "/etc/ssl/private/server.key".into()
        )
}
```

## Monitoring and Maintenance

### Health Checks

Implement comprehensive health checks:

```rust
use serde_json::json;
use axum::{response::Json, extract::State};

async fn health_check(
    State(app_state): State<AppState>
) -> Json<serde_json::Value> {
    // Check server state
    let is_healthy = app_state.server_state.is_healthy();
    
    // Check storage service
    let storage_healthy = app_state.storage_service.health_check().await;
    
    Json(json!({
        "status": if is_healthy && storage_healthy { "healthy" } else { "unhealthy" },
        "timestamp": chrono::Utc::now(),
        "services": {
            "server_state": is_healthy,
            "storage": storage_healthy
        }
    }))
}
```

### Metrics Collection

Integrate with Prometheus or similar monitoring systems:

```rust
use prometheus::{Counter, Histogram, register_counter, register_histogram};

lazy_static! {
    static ref WEBSOCKET_CONNECTIONS: Counter = register_counter!(
        "websocket_connections_total",
        "Total number of WebSocket connections"
    ).unwrap();
    
    static ref MESSAGE_PROCESSING_TIME: Histogram = register_histogram!(
        "message_processing_seconds",
        "Time spent processing messages"
    ).unwrap();
}

// In your handlers
async fn websocket_handler(/* ... */) -> Response {
    WEBSOCKET_CONNECTIONS.inc();
    
    let timer = MESSAGE_PROCESSING_TIME.start_timer();
    // ... handle WebSocket
    timer.observe_duration();
    
    // ... rest of handler
}
```

### Logging Configuration

Configure structured logging for production:

```rust
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

fn init_logging() {
    tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into())
        ))
        .with(tracing_subscriber::fmt::layer().json())
        .init();
}
```

### Maintenance Tasks

1. **Session Cleanup**: The server automatically cleans up expired sessions
2. **Log Rotation**: Configure log rotation to prevent disk space issues
3. **Certificate Renewal**: Automate TLS certificate renewal
4. **Dependency Updates**: Regularly update dependencies for security patches

## Troubleshooting

### Common Issues

#### WebSocket Connection Failures

**Symptoms**: Clients cannot establish WebSocket connections

**Diagnosis**:
```bash
# Test WebSocket endpoint
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Key: test" \
     -H "Sec-WebSocket-Version: 13" \
     http://localhost:8080/ws
```

**Solutions**:
- Check reverse proxy WebSocket configuration
- Verify CORS settings for WebSocket origins
- Ensure proper upgrade headers are passed through

#### File Upload Failures

**Symptoms**: File chunk uploads fail or timeout

**Diagnosis**:
```bash
# Test file upload endpoint
curl -X POST http://localhost:8080/v1/files/chunk \
     -F "file=@test.txt" \
     -F "chunk_id=1" \
     -F "total_chunks=1"
```

**Solutions**:
- Increase `max_request_size` in ServerConfig
- Check proxy timeout settings for file uploads
- Verify storage service configuration

#### High Memory Usage

**Symptoms**: Server memory usage grows over time

**Diagnosis**:
- Monitor session cleanup logs
- Check for WebSocket connection leaks
- Profile with tools like `valgrind` or `heaptrack`

**Solutions**:
- Tune session cleanup intervals
- Implement connection limits
- Review WebSocket handler cleanup logic

#### Performance Issues

**Symptoms**: High latency or low throughput

**Diagnosis**:
- Monitor connection pool utilization
- Check database query performance
- Profile CPU usage patterns

**Solutions**:
- Increase connection pool size
- Optimize database queries
- Add caching layers
- Scale horizontally

### Debug Mode Configuration

For troubleshooting, enable debug logging:

```rust
let config = ServerConfig::new()
    .with_logging(true, "debug".to_string());
```

### Log Analysis

Key log patterns to monitor:

```bash
# Connection issues
grep "WebSocket" /var/log/app.log | grep -i error

# Performance issues
grep "slow" /var/log/app.log

# Security events
grep -E "(auth|security|cors)" /var/log/app.log
```

### Support Resources

- **Documentation**: Refer to the rustdoc documentation for detailed API information
- **Examples**: Check the `examples/` directory for integration patterns
- **Community**: Engage with the Rust community for web framework-specific issues
- **Monitoring**: Implement comprehensive monitoring to catch issues early

---

This deployment guide provides a comprehensive foundation for running the Indidus E2EE Server in production environments. Always test thoroughly in staging environments before deploying to production, and implement proper monitoring and alerting to ensure reliable operation.
