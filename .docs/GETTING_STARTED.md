# Getting Started with Indidus E2EE

This tutorial will guide you through setting up your first end-to-end encrypted messaging application using the Indidus E2EE library. By the end of this guide, you'll have a working client that can send and receive encrypted messages.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Project Setup](#project-setup)
3. [Server Setup](#server-setup)
4. [Basic Client Implementation](#basic-client-implementation)
5. [Two-Client Communication](#two-client-communication)
6. [File Transfer](#file-transfer)
7. [State Management](#state-management)
8. [Next Steps](#next-steps)

## Prerequisites

Before starting, ensure you have:

- **Rust 1.70 or later** installed ([rustup.rs](https://rustup.rs/))
- **Basic Rust knowledge** (variables, functions, async/await)
- **Network connectivity** for downloading dependencies
- **A text editor or IDE** (VS Code with rust-analyzer recommended)

### Verify Your Setup

```bash
# Check Rust version
rustc --version
# Should show: rustc 1.70.0 or later

# Check Cargo version
cargo --version
# Should show: cargo 1.70.0 or later
```

## Project Setup

### 1. Create a New Rust Project

```bash
# Create a new binary project
cargo new indidus_chat_app
cd indidus_chat_app
```

### 2. Add Dependencies

Edit your `Cargo.toml` file:

```toml
[package]
name = "indidus_chat_app"
version = "0.1.0"
edition = "2021"

[dependencies]
# Indidus E2EE components
indidus_e2ee_client = "0.1.0"
indidus_e2ee_server = "0.1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
url = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# CLI and user interaction
clap = { version = "4.0", features = ["derive"] }
dialoguer = "0.11"
console = "0.15"

# Error handling
anyhow = "1.0"
```

### 3. Project Structure

Create the following directory structure:

```
indidus_chat_app/
├── Cargo.toml
├── src/
│   ├── main.rs
│   ├── client.rs
│   └── server.rs
└── examples/
    ├── simple_client.rs
    └── file_transfer.rs
```

## Server Setup

First, let's create a simple relay server that clients can connect to.

### Create `src/server.rs`

```rust
use indidus_e2ee_server::{Server, ServerConfig};
use anyhow::Result;
use std::net::SocketAddr;

pub async fn start_server(host: &str, port: u16) -> Result<()> {
    println!("🚀 Starting Indidus E2EE Server...");
    
    // Create server configuration
    let config = ServerConfig::with_address(host.to_string(), port)
        .with_max_connections(100)
        .with_logging(true, "info".to_string())
        .with_cors_enabled(true);

    // Create and start the server
    let server = Server::new(config).await?;
    let app = server.create_router().await?;
    
    let addr = SocketAddr::new(host.parse()?, port);
    println!("✅ Server listening on http://{}", addr);
    println!("📡 WebSocket endpoint: ws://{}/ws", addr);
    
    // Start the server
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;
    
    Ok(())
}
```

### Create `src/client.rs`

```rust
use indidus_e2ee_client::{Client, ClientConfig, ClientEvent, ConnectionState};
use anyhow::{Result, Context};
use url::Url;
use uuid::Uuid;
use std::collections::HashMap;
use tokio::io::{self, AsyncBufReadExt, BufReader};

pub struct ChatClient {
    client: Client,
    client_id: Uuid,
}

impl ChatClient {
    pub async fn new(server_url: &str, display_name: Option<String>) -> Result<Self> {
        println!("🔧 Creating new chat client...");
        
        // Parse server URL
        let url = Url::parse(server_url)
            .context("Invalid server URL")?;
        
        // Create client configuration
        let config = ClientConfig::new(url)
            .with_display_name(display_name.unwrap_or_else(|| "Anonymous".to_string()))
            .with_debug_mode(true)
            .with_max_prekeys(50)
            .with_timeout(30);
        
        // Create and initialize client
        let mut client = Client::new(config)
            .context("Failed to create client")?;
        
        client.initialize().await
            .context("Failed to initialize client")?;
        
        let client_id = client.client_id();
        println!("✅ Client created with ID: {}", client_id);
        
        Ok(Self { client, client_id })
    }

    pub async fn connect(&mut self) -> Result<()> {
        println!("🔌 Connecting to server...");
        
        self.client.connect().await
            .context("Failed to connect to server")?;
        
        // Wait for connection confirmation
        while let Some(event) = self.client.next_event().await? {
            match event {
                ClientEvent::ConnectionStateChanged(ConnectionState::Connected) => {
                    println!("✅ Connected to server successfully!");
                    break;
                }
                ClientEvent::ConnectionStateChanged(ConnectionState::ConnectionError(err)) => {
                    anyhow::bail!("Connection failed: {}", err);
                }
                _ => {
                    // Ignore other events during connection
                }
            }
        }
        
        Ok(())
    }

    pub async fn send_message(&mut self, recipient_id: Uuid, message: &str) -> Result<()> {
        println!("📤 Sending message to {}...", recipient_id);
        
        self.client.send_message(recipient_id, message.as_bytes(), "text").await
            .context("Failed to send message")?;
        
        println!("✅ Message sent!");
        Ok(())
    }

    pub async fn listen_for_messages(&mut self) -> Result<()> {
        println!("👂 Listening for messages... (Press Ctrl+C to exit)");
        
        while let Some(event) = self.client.next_event().await? {
            match event {
                ClientEvent::MessageReceived { sender_id, encrypted_payload, .. } => {
                    // Decrypt the message
                    match self.client.decrypt_message(sender_id, &encrypted_payload).await {
                        Ok(decrypted) => {
                            let message = String::from_utf8_lossy(&decrypted);
                            println!("📨 Message from {}: {}", sender_id, message);
                        }
                        Err(e) => {
                            println!("❌ Failed to decrypt message from {}: {}", sender_id, e);
                        }
                    }
                }
                ClientEvent::ConnectionStateChanged(state) => {
                    println!("🔄 Connection state: {:?}", state);
                }
                ClientEvent::FileTransferInitiated { sender_id, file_name, file_size, .. } => {
                    println!("📁 File transfer from {}: {} ({} bytes)", sender_id, file_name, file_size);
                }
                ClientEvent::ServerError { error_code, error_message } => {
                    println!("🚨 Server error {}: {}", error_code, error_message);
                }
                _ => {
                    // Handle other events as needed
                }
            }
        }
        
        Ok(())
    }

    pub fn client_id(&self) -> Uuid {
        self.client_id
    }

    pub async fn save_state(&self, path: &str) -> Result<()> {
        self.client.save_state(path).await
            .context("Failed to save client state")
    }

    pub async fn load_state(path: &str, server_url: &str) -> Result<Self> {
        let url = Url::parse(server_url)?;
        let config = ClientConfig::new(url);
        
        let client = Client::load_state(path, config).await
            .context("Failed to load client state")?;
        
        let client_id = client.client_id();
        
        Ok(Self { client, client_id })
    }
}
```

### Update `src/main.rs`

```rust
mod client;
mod server;

use anyhow::Result;
use clap::{Parser, Subcommand};
use dialoguer::{Input, Select};
use uuid::Uuid;

#[derive(Parser)]
#[command(name = "indidus-chat")]
#[command(about = "A simple E2EE chat application using Indidus")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Start the relay server
    Server {
        /// Host to bind to
        #[arg(long, default_value = "127.0.0.1")]
        host: String,
        /// Port to bind to
        #[arg(long, default_value = "8080")]
        port: u16,
    },
    /// Start a chat client
    Client {
        /// Server URL to connect to
        #[arg(long, default_value = "http://127.0.0.1:8080")]
        server: String,
        /// Display name for this client
        #[arg(long)]
        name: Option<String>,
        /// Path to saved state file
        #[arg(long)]
        state: Option<String>,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    match cli.command {
        Commands::Server { host, port } => {
            server::start_server(&host, port).await
        }
        Commands::Client { server, name, state } => {
            run_client(&server, name, state).await
        }
    }
}

async fn run_client(server_url: &str, display_name: Option<String>, state_path: Option<String>) -> Result<()> {
    // Try to load existing state or create new client
    let mut client = if let Some(path) = &state_path {
        match client::ChatClient::load_state(path, server_url).await {
            Ok(client) => {
                println!("📂 Loaded existing client state from {}", path);
                client
            }
            Err(_) => {
                println!("🆕 Creating new client (couldn't load state)");
                client::ChatClient::new(server_url, display_name).await?
            }
        }
    } else {
        client::ChatClient::new(server_url, display_name).await?
    };

    // Connect to server
    client.connect().await?;

    // Show client ID
    println!("🆔 Your client ID: {}", client.client_id());
    println!("📋 Share this ID with others to receive messages");

    // Main interaction loop
    loop {
        let options = vec![
            "Send a message",
            "Listen for messages",
            "Save state and exit",
            "Exit without saving",
        ];

        let selection = Select::new()
            .with_prompt("What would you like to do?")
            .items(&options)
            .interact()?;

        match selection {
            0 => {
                // Send message
                let recipient: String = Input::new()
                    .with_prompt("Recipient ID")
                    .interact_text()?;

                let message: String = Input::new()
                    .with_prompt("Message")
                    .interact_text()?;

                match Uuid::parse_str(&recipient) {
                    Ok(recipient_id) => {
                        if let Err(e) = client.send_message(recipient_id, &message).await {
                            println!("❌ Failed to send message: {}", e);
                        }
                    }
                    Err(_) => {
                        println!("❌ Invalid recipient ID format");
                    }
                }
            }
            1 => {
                // Listen for messages
                if let Err(e) = client.listen_for_messages().await {
                    println!("❌ Error while listening: {}", e);
                }
            }
            2 => {
                // Save and exit
                if let Some(path) = state_path {
                    if let Err(e) = client.save_state(&path).await {
                        println!("❌ Failed to save state: {}", e);
                    } else {
                        println!("💾 State saved to {}", path);
                    }
                }
                break;
            }
            3 => {
                // Exit without saving
                break;
            }
            _ => unreachable!(),
        }
    }

    println!("👋 Goodbye!");
    Ok(())
}
```

## Basic Client Implementation

Now let's test our implementation with a simple example.

### 1. Start the Server

In one terminal:

```bash
cargo run -- server --host 127.0.0.1 --port 8080
```

You should see:
```
🚀 Starting Indidus E2EE Server...
✅ Server listening on http://127.0.0.1:8080
📡 WebSocket endpoint: ws://127.0.0.1:8080/ws
```

### 2. Start the First Client

In a second terminal:

```bash
cargo run -- client --name "Alice" --state alice_state.json
```

You should see:
```
🔧 Creating new chat client...
✅ Client created with ID: 550e8400-e29b-41d4-a716-446655440000
🔌 Connecting to server...
✅ Connected to server successfully!
🆔 Your client ID: 550e8400-e29b-41d4-a716-446655440000
📋 Share this ID with others to receive messages
```

### 3. Start the Second Client

In a third terminal:

```bash
cargo run -- client --name "Bob" --state bob_state.json
```

Note the client ID that Bob gets - you'll need it for Alice to send messages.

## Two-Client Communication

Now let's send messages between Alice and Bob:

### 1. Send a Message from Alice to Bob

In Alice's terminal:
1. Select "Send a message"
2. Enter Bob's client ID when prompted
3. Type your message

### 2. Listen for Messages on Bob's Side

In Bob's terminal:
1. Select "Listen for messages"
2. You should see Alice's message appear

### 3. Verify End-to-End Encryption

Check the server logs - you should see encrypted payloads being relayed, but no plaintext messages. The server cannot read your messages!

## File Transfer

Let's add file transfer capability. Create `examples/file_transfer.rs`:

```rust
use indidus_chat_app::client::ChatClient;
use anyhow::Result;
use std::path::Path;
use uuid::Uuid;

#[tokio::main]
async fn main() -> Result<()> {
    // Create two clients
    let mut alice = ChatClient::new("http://127.0.0.1:8080", Some("Alice".to_string())).await?;
    let mut bob = ChatClient::new("http://127.0.0.1:8080", Some("Bob".to_string())).await?;

    // Connect both clients
    alice.connect().await?;
    bob.connect().await?;

    println!("Alice ID: {}", alice.client_id());
    println!("Bob ID: {}", bob.client_id());

    // Create a test file
    std::fs::write("test_file.txt", "Hello from Alice! This is a test file.")?;

    // Send file from Alice to Bob
    let file_path = Path::new("test_file.txt");
    println!("📁 Alice sending file to Bob...");
    
    let transfer_id = alice.send_file(bob.client_id(), file_path).await?;
    println!("🚀 File transfer initiated: {}", transfer_id);

    // Bob listens for the file
    println!("👂 Bob listening for file...");
    // Implementation would handle file reception here

    Ok(())
}
```

## State Management

The client automatically manages cryptographic state, but you can control when it's saved:

### Automatic State Management

```rust
// State is automatically maintained in memory
let mut client = ChatClient::new("http://server.com", Some("Alice".to_string())).await?;

// Sessions are established automatically when sending messages
client.send_message(recipient_id, "Hello!").await?;
```

### Manual State Persistence

```rust
// Save state to file
client.save_state("my_client_state.json").await?;

// Load state from file
let client = ChatClient::load_state("my_client_state.json", "http://server.com").await?;
```

### State File Security

⚠️ **Important**: State files contain cryptographic keys and should be protected:

```bash
# Set restrictive permissions on state files
chmod 600 alice_state.json bob_state.json
```

## Testing Your Implementation

### 1. Basic Functionality Test

```bash
# Terminal 1: Start server
cargo run -- server

# Terminal 2: Start Alice
cargo run -- client --name Alice --state alice.json

# Terminal 3: Start Bob  
cargo run -- client --name Bob --state bob.json
```

### 2. Message Exchange Test

1. Alice sends message to Bob
2. Bob receives and can read the message
3. Bob sends reply to Alice
4. Alice receives the reply

### 3. Persistence Test

1. Send messages between clients
2. Save state and exit both clients
3. Restart clients with saved state
4. Verify they can still communicate

### 4. Security Verification

Check that:
- Server logs show only encrypted payloads
- Messages are properly decrypted on recipient side
- Invalid recipient IDs are handled gracefully

## Troubleshooting

### Common Issues

#### "Connection refused"
- Ensure the server is running
- Check the server URL and port
- Verify firewall settings

#### "Failed to decrypt message"
- Ensure both clients have established a session
- Check that client IDs are correct
- Verify state files aren't corrupted

#### "Invalid recipient ID"
- Ensure the recipient ID is a valid UUID
- Check that the recipient client is connected
- Verify copy-paste didn't introduce errors

### Debug Mode

Enable debug logging for more information:

```rust
let config = ClientConfig::new(url)
    .with_debug_mode(true);  // Enable debug output
```

### Logging

Add logging to see what's happening:

```toml
# Add to Cargo.toml
[dependencies]
tracing = "0.1"
tracing-subscriber = "0.3"
```

```rust
// Add to main.rs
use tracing_subscriber;

#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::fmt::init();
    // ... rest of your code
}
```

## Next Steps

Congratulations! You now have a working end-to-end encrypted chat application. Here are some ideas for extending it:

### Feature Enhancements

1. **Group Messaging**: Extend to support multiple recipients
2. **File Transfer UI**: Add progress bars and file management
3. **Contact Management**: Store and manage contact lists
4. **Message History**: Persist and display message history
5. **Typing Indicators**: Show when someone is typing

### Security Improvements

1. **Key Verification**: Implement out-of-band key verification
2. **Perfect Forward Secrecy**: Ensure proper key rotation
3. **Secure Storage**: Use platform keychain for key storage
4. **Audit Logging**: Log security-relevant events

### User Experience

1. **GUI Interface**: Build a desktop or web interface
2. **Mobile App**: Create mobile applications
3. **Notifications**: Add desktop notifications for new messages
4. **Themes**: Implement customizable themes

### Advanced Topics

- [Client State Management Tutorial](.docs/CLIENT_STATE_MANAGEMENT.md)
- [Relay Server Deployment Guide](.docs/SERVER_DEPLOYMENT.md)
- [Security Considerations](.docs/SECURITY.md)

## Resources

- **API Documentation**: Run `cargo doc --open` for detailed API docs
- **Examples**: Check the `examples/` directory for more code samples
- **Signal Protocol**: Learn more about the underlying cryptography
- **Rust Async**: Improve your async Rust skills for better performance

Happy coding! 🚀