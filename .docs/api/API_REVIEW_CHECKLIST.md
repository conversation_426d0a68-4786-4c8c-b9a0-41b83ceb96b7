# Indidus E2EE API Review Checklist

## Overview

This checklist serves as the standard for reviewing all public APIs across the Indidus E2EE crates. It is based on the official [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/) and the project's specific goals, particularly the "3-line integration" requirement for basic messaging functionality.

## Project-Specific Goals

- **3-Line Integration**: Basic messaging should require no more than 3 lines of code
- **Sensible Defaults**: Configuration should work out-of-the-box with minimal setup
- **Framework Agnostic**: Server components should integrate seamlessly with Axum, Actix, Warp, etc.
- **Stateless Design**: Clear separation between crypto operations and state persistence
- **Developer Experience**: New developers should send encrypted messages in <10 minutes

## 1. Naming Conventions (C-CASE)

### ✅ Type Names
- [ ] Types use `UpperCamelCase` (e.g., `ClientConfig`, `SessionState`)
- [ ] Acronyms in type names are `UpperCamelCase` (e.g., `E2eeClient`, not `E2EEClient`)
- [ ] Type names are descriptive and unambiguous
- [ ] Avoid stuttering (e.g., `client::Client` should be `client::Manager`)

### ✅ Function and Method Names
- [ ] Functions use `snake_case` (e.g., `send_message`, `establish_session`)
- [ ] Method names are verbs or verb phrases
- [ ] Getter methods omit `get_` prefix (e.g., `client_id()`, not `get_client_id()`)
- [ ] Boolean methods use `is_`, `has_`, `can_`, or `should_` prefixes

### ✅ Module Names
- [ ] Modules use `snake_case`
- [ ] Module names are singular nouns (e.g., `session`, not `sessions`)
- [ ] Avoid deep nesting (prefer flat module structure)

### ✅ Constants and Statics
- [ ] Use `SCREAMING_SNAKE_CASE`
- [ ] Group related constants in modules or associated constants

## 2. Error Handling (C-ERROR)

### ✅ Error Types
- [ ] Each crate has a unified error enum (e.g., `ClientError`, `ServerError`, `ProtocolError`)
- [ ] Error types implement `std::error::Error`
- [ ] Use `thiserror` for error derivation consistency
- [ ] Error variants are descriptive and actionable

### ✅ Result Types
- [ ] Define crate-specific `Result` type aliases (e.g., `type ClientResult<T> = Result<T, ClientError>`)
- [ ] Public functions return `Result` for fallible operations
- [ ] Avoid `unwrap()` and `expect()` in public APIs

### ✅ Error Context
- [ ] Errors provide sufficient context for debugging
- [ ] Chain errors appropriately using `source()` or `#[from]`
- [ ] Include relevant data in error variants (IDs, paths, etc.)

## 3. Configuration and Builder Patterns (C-BUILDER)

### ✅ Configuration Structs
- [ ] Use builder pattern for complex configuration
- [ ] Provide sensible defaults via `Default` trait
- [ ] Configuration is validated at build time, not runtime
- [ ] Required fields are enforced by the type system

### ✅ Builder Methods
- [ ] Builder methods are chainable (return `Self`)
- [ ] Use `with_` prefix for optional configuration
- [ ] Use `new()` for required parameters
- [ ] Provide `build()` method that validates and constructs

### ✅ Example Pattern
```rust
// ✅ Good: Clear builder with sensible defaults
let config = ClientConfig::new(server_url)
    .with_display_name("Alice".to_string())
    .with_timeout(Duration::from_secs(30));

// ❌ Bad: Too many required parameters
let config = ClientConfig::new(url, name, timeout, retries, buffer_size);
```

## 4. Documentation Standards (C-DOCS)

### ✅ Crate-Level Documentation
- [ ] Crate root has comprehensive module documentation
- [ ] Includes quick start example showing 3-line integration
- [ ] Documents feature flags and optional dependencies
- [ ] Provides links to guides and tutorials

### ✅ Type Documentation
- [ ] All public types have doc comments
- [ ] Includes usage examples for complex types
- [ ] Documents invariants and assumptions
- [ ] Explains relationship to other types

### ✅ Function Documentation
- [ ] All public functions have doc comments
- [ ] Documents parameters, return values, and errors
- [ ] Includes examples for non-trivial functions
- [ ] Documents panics and safety requirements

### ✅ Example Quality
- [ ] Examples are complete and runnable
- [ ] Examples demonstrate real-world usage
- [ ] Examples show error handling
- [ ] Examples are tested with `cargo test --doc`

## 5. Public Trait Design (C-TRAIT)

### ✅ Trait Boundaries
- [ ] Traits have clear, focused responsibilities
- [ ] Avoid "god traits" with too many methods
- [ ] Use associated types over generic parameters when appropriate
- [ ] Provide blanket implementations where sensible

### ✅ Trait Methods
- [ ] Default implementations for common cases
- [ ] Methods take `&self` unless mutation is required
- [ ] Avoid requiring `Clone` unless necessary
- [ ] Use `impl Trait` for complex return types

## 6. Memory Management and Performance (C-PERF)

### ✅ Ownership Patterns
- [ ] Prefer borrowing over owned values in function parameters
- [ ] Use `Cow<str>` for strings that might be borrowed or owned
- [ ] Avoid unnecessary allocations in hot paths
- [ ] Use `Arc` for shared immutable data

### ✅ Async Patterns
- [ ] Async functions are `Send` unless documented otherwise
- [ ] Use `Pin<Box<dyn Future>>` sparingly
- [ ] Prefer `async fn` over manual `Future` implementations
- [ ] Document cancellation behavior

## 7. Integration Patterns (C-INTEGRATION)

### ✅ Framework Integration
- [ ] Server components work with multiple web frameworks
- [ ] No hard dependencies on specific HTTP libraries
- [ ] State management is externalized and user-controlled
- [ ] Middleware patterns are framework-agnostic

### ✅ Serialization
- [ ] Types implement `Serialize`/`Deserialize` where appropriate
- [ ] Use `#[serde(skip)]` for sensitive fields
- [ ] Provide stable serialization format
- [ ] Document serialization compatibility

## 8. Security Considerations (C-SECURITY)

### ✅ Sensitive Data Handling
- [ ] Sensitive types implement `Zeroize` trait
- [ ] No sensitive data in `Debug` implementations
- [ ] Clear documentation of security assumptions
- [ ] Constant-time operations where required

### ✅ API Safety
- [ ] No unsafe code in public APIs without clear justification
- [ ] Input validation at API boundaries
- [ ] Clear documentation of trust boundaries
- [ ] Fail-safe defaults

## 9. Versioning and Stability (C-STABLE)

### ✅ Semantic Versioning
- [ ] Breaking changes require major version bump
- [ ] New features use minor version bump
- [ ] Bug fixes use patch version bump
- [ ] Pre-1.0 versions clearly marked as unstable

### ✅ Deprecation
- [ ] Deprecated items have `#[deprecated]` attribute
- [ ] Deprecation messages suggest alternatives
- [ ] Deprecated items remain functional for one major version
- [ ] Clear migration path documented

## 10. Testing and Examples (C-TEST)

### ✅ Example Completeness
- [ ] Each crate has working examples in `examples/` directory
- [ ] Examples demonstrate real-world usage patterns
- [ ] Examples include error handling
- [ ] Examples are documented and tested

### ✅ Integration Examples
- [ ] Framework integration examples (Axum, Actix, Warp)
- [ ] Client-server communication examples
- [ ] File transfer examples
- [ ] State persistence examples

## 11. 3-Line Integration Verification

### ✅ Basic Messaging Test
The following pattern should work for basic messaging:

```rust
// Line 1: Configuration with sensible defaults
let config = ClientConfig::new(server_url)?;

// Line 2: Client creation
let client = Client::new(config)?;

// Line 3: Send message
client.send_message(&peer_id, "Hello, World!").await?;
```

### ✅ Verification Checklist
- [ ] No more than 3 lines for basic messaging
- [ ] Minimal required configuration
- [ ] Clear error messages for common mistakes
- [ ] Works with default settings

## 12. Cross-Crate Consistency

### ✅ Naming Consistency
- [ ] Similar concepts use same names across crates
- [ ] Error handling patterns are consistent
- [ ] Configuration patterns are consistent
- [ ] Documentation style is consistent

### ✅ Type Compatibility
- [ ] Types can be easily converted between crates
- [ ] Shared types are defined in appropriate crate
- [ ] No unnecessary type conversions required
- [ ] Clear ownership boundaries

## Review Process

1. **Automated Checks**: Run `cargo clippy`, `cargo fmt`, and `cargo doc`
2. **Manual Review**: Go through each checklist item systematically
3. **Integration Test**: Verify 3-line integration works
4. **Documentation Review**: Ensure examples are complete and tested
5. **Cross-Crate Review**: Check consistency across all crates

## Common Anti-Patterns to Avoid

- ❌ **Configuration Overload**: Too many required parameters
- ❌ **Stringly Typed**: Using strings where enums would be better
- ❌ **Leaky Abstractions**: Exposing internal implementation details
- ❌ **Inconsistent Naming**: Different names for same concepts
- ❌ **Poor Error Messages**: Vague or unhelpful error descriptions
- ❌ **Framework Lock-in**: Hard dependencies on specific web frameworks
- ❌ **State Coupling**: Mixing business logic with state persistence

## Success Criteria

An API passes review when:
- [ ] All checklist items are verified
- [ ] 3-line integration example works
- [ ] Documentation is complete and accurate
- [ ] Examples run successfully
- [ ] Cross-crate consistency is maintained
- [ ] Security considerations are addressed