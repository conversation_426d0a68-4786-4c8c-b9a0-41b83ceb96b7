# Code Coverage Guide

This document explains the code coverage reporting and quality gate system implemented in this project.

## Overview

The project uses `cargo-tarpaulin` to generate code coverage reports and enforce minimum coverage thresholds in CI.

## Current Status

- **Current Coverage**: ~38.79%
- **Minimum Threshold**: 35.0%
- **Target Coverage**: 85.0%

## CI Integration

The coverage system runs automatically in CI with the following steps:

1. **Generate Coverage Report**: Runs `cargo-tarpaulin` on all library and binary code
2. **Quality Gate Check**: Enforces minimum coverage threshold
3. **Upload Artifacts**: Stores coverage reports for download
4. **Optional Codecov**: Uploads to external coverage service

## Quality Gate

The quality gate prevents merging code that reduces test coverage below the minimum threshold.

### Current Configuration

```yaml
min_coverage=35.0  # Current minimum threshold
target_coverage=85.0  # Long-term target
```

### Adjusting the Threshold

As test coverage improves, the minimum threshold should be gradually increased:

1. **Monitor current coverage** in CI logs and artifacts
2. **Increase threshold** when coverage consistently exceeds current minimum
3. **Update the CI configuration** in `.github/workflows/ci.yml`
4. **Recommended increments**: 5-10% at a time

### Example Progression

```
Phase 1: 35% → 40% (add basic unit tests)
Phase 2: 40% → 50% (add integration tests)
Phase 3: 50% → 65% (add edge case tests)
Phase 4: 65% → 80% (comprehensive testing)
Phase 5: 80% → 85% (target achieved)
```

## Coverage Reports

### Viewing Reports

1. **CI Artifacts**: Download `coverage-report` artifact from CI runs
2. **Local Generation**: Run `cargo tarpaulin --workspace --lib --bins --out Html`
3. **Codecov Dashboard**: View online at codecov.io (if configured)

### Understanding Metrics

- **Line Coverage**: Percentage of code lines executed during tests
- **Branch Coverage**: Percentage of conditional branches tested
- **Function Coverage**: Percentage of functions called during tests

## Best Practices

### For Contributors

1. **Run coverage locally** before submitting PRs
2. **Add tests for new code** to maintain or improve coverage
3. **Focus on critical paths** first (error handling, security features)
4. **Don't game the system** - write meaningful tests, not just coverage

### For Maintainers

1. **Review coverage trends** in CI reports
2. **Gradually increase thresholds** as coverage improves
3. **Identify low-coverage areas** for targeted testing efforts
4. **Balance coverage goals** with development velocity

## Commands

### Local Coverage Generation

```bash
# Generate HTML report for local viewing
cargo tarpaulin --workspace --lib --bins --out Html

# Generate XML report (same as CI)
cargo tarpaulin --workspace --lib --bins --out Xml --exclude-files "tests/*"

# Generate both HTML and XML
cargo tarpaulin --workspace --lib --bins --out Html --out Xml
```

### Coverage Analysis

```bash
# View coverage summary
cargo tarpaulin --workspace --lib --bins

# Exclude specific files or directories
cargo tarpaulin --workspace --lib --bins --exclude-files "examples/*" "benches/*"

# Include only specific packages
cargo tarpaulin --packages indidus_signal_protocol indidus_e2ee_client
```

## Troubleshooting

### Common Issues

**"Coverage below threshold"**
- Add more unit tests for uncovered code
- Focus on high-impact areas first
- Consider if threshold is too aggressive

**"Tarpaulin fails to run"**
- Ensure all dependencies compile
- Check for platform-specific issues
- Try running with `--timeout` flag

**"Coverage report not generated"**
- Verify tests are passing
- Check for compilation errors
- Ensure proper workspace configuration

### Getting Help

- Check the [cargo-tarpaulin documentation](https://github.com/xd009642/tarpaulin)
- Review CI logs for detailed error messages
- Open an issue for project-specific coverage problems

## Integration with Development Workflow

### Pre-commit Checks

Consider adding coverage checks to pre-commit hooks:

```bash
#!/bin/bash
# .git/hooks/pre-commit
cargo tarpaulin --workspace --lib --bins --fail-under 35
```

### PR Reviews

- Review coverage changes in PR descriptions
- Ensure new features include appropriate tests
- Discuss coverage strategy for complex features

---

For more information about testing in this project, see the main [README.md](README.md) and [FUZZING.md](FUZZING.md).