//! # Actix Integration Example for Indidus E2EE Server
//!
//! This example demonstrates the **standardized framework-specific handler pattern**
//! for integrating the `indidus_e2ee_server` library with the Actix Web framework.
//!
//! ## Key Integration Patterns
//!
//! - **Application State**: Centralized state management with E2EE services
//! - **WebSocket Handlers**: Framework-native async fn handlers for real-time messaging
//! - **Message Processing**: Direct use of E2EE library types (ClientMessage/ServerMessage)
//! - **Connection Management**: Routing table integration with proper cleanup
//! - **Error Handling**: Framework-specific error types and responses
//!
//! ## Usage
//!
//! ```bash
//! cargo run --example actix_integration
//! ```
//!
//! The server will start on `http://localhost:8080` with a WebSocket endpoint at `/ws`.
//!
//! ## Testing the Server
//!
//! You can test the WebSocket connection using a WebSocket client or the provided
//! basic messaging example:
//!
//! ```bash
//! # In another terminal, run the basic messaging example
//! cargo run --example basic_messaging -- --server-url ws://localhost:8080/ws
//! ```
//!
//! ## Integration Pattern
//!
//! This example shows the standard integration pattern for Actix Web:
//!
//! 1. **Initialize Server State**: Create shared state including routing table
//! 2. **Configure WebSocket Route**: Set up the `/ws` endpoint with proper upgrade handling
//! 3. **Handle Connections**: Implement WebSocket message handling using the E2EE library's types
//! 4. **Error Management**: Implement proper error handling and logging
//!
//! The key integration points are:
//! - Creating a `RoutingTable` for managing client connections
//! - Using `actix-ws` to handle WebSocket upgrades
//! - Implementing the message handling logic using the E2EE library's message types

use actix_web::{
    middleware::Logger,
    web::{self, Data},
    App, HttpRequest, HttpResponse, HttpServer, Result as ActixResult,
};
use actix_ws::{Message, Session};
use anyhow::Result;
use futures_util::StreamExt;
use indidus_e2ee_server::handlers::{
    error::HandlerError,
    websocket::{ClientMessage, RoutingTable, ServerMessage},
};
use indidus_shared::validation::PeerId;
use tokio::sync::mpsc;
use tracing::{error, info, warn};

/// Application state that combines E2EE services for Actix integration
/// 
/// This follows the standardized pattern of centralizing all E2EE-related
/// services in a single state structure that can be shared across handlers.
#[derive(Clone)]
pub struct AppState {
    /// Routing table for managing WebSocket connections and message relay
    pub routing_table: RoutingTable,
}

impl AppState {
    /// Create new application state with initialized E2EE services
    pub fn new() -> Self {
        Self {
            routing_table: RoutingTable::new(),
        }
    }
}

/// WebSocket handler using the standardized framework-specific pattern
/// 
/// This demonstrates the recommended integration approach:
/// 1. Extract the application state using Actix's Data extractor
/// 2. Upgrade the HTTP connection to WebSocket using Actix's native types
/// 3. Handle the connection using framework-specific message processing
/// 4. Use E2EE library types directly (ClientMessage/ServerMessage)
pub async fn websocket_handler(
    req: HttpRequest,
    stream: web::Payload,
    app_state: Data<AppState>,
) -> ActixResult<HttpResponse> {
    info!("WebSocket connection request received from: {:?}", req.peer_addr());
    
    // Upgrade the connection to WebSocket using Actix's native upgrade
    let (response, session, msg_stream) = actix_ws::handle(&req, stream)?;
    
    // Clone the routing table for this connection
    let routing_table = app_state.routing_table.clone();
    
    // Handle the WebSocket connection using the standardized pattern
    actix_web::rt::spawn(async move {
        if let Err(e) = handle_websocket_connection(session, msg_stream, routing_table).await {
            error!("WebSocket connection handler error: {}", e);
        }
    });
    
    Ok(response)
}

/// Handle an individual WebSocket connection using the standardized pattern
///
/// This function implements the E2EE message handling logic using the library's
/// message types and routing table, following the framework-specific approach.
/// It demonstrates the complete lifecycle of a WebSocket connection:
/// 1. Connection setup with message channels
/// 2. Peer registration enforcement
/// 3. Message processing loop using E2EE library types
/// 4. Proper cleanup and error handling
async fn handle_websocket_connection(
    mut session: Session,
    mut msg_stream: actix_ws::MessageStream,
    routing_table: RoutingTable,
) -> Result<()> {
    info!("New WebSocket connection established");
    
    // Create a channel for sending messages to this client
    let (message_tx, mut message_rx) = mpsc::unbounded_channel::<ServerMessage>();
    
    // Track the peer ID and registration state
    let mut peer_id: Option<PeerId> = None;
    let mut is_registered = false;
    
    // Spawn a task to handle outgoing messages
    let outgoing_task = tokio::spawn(async move {
        while let Some(message) = message_rx.recv().await {
            if let Err(e) = send_server_message(&mut session, message).await {
                error!("Failed to send message to client: {}", e);
                break;
            }
        }
    });
    
    // Main message processing loop
    while let Some(message_result) = msg_stream.next().await {
        match message_result {
            Ok(message) => {
                match process_websocket_message(
                    message,
                    &message_tx,
                    &routing_table,
                    &mut peer_id,
                    &mut is_registered,
                )
                .await
                {
                    Ok(should_continue) => {
                        if !should_continue {
                            info!("Client requested disconnection");
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Error processing message: {}", e);
                        // Send error response to client
                        let error_msg = ServerMessage::Error {
                            reason: e.to_string(),
                        };
                        if let Err(send_err) = message_tx.send(error_msg) {
                            error!("Failed to send error message: {}", send_err);
                            break;
                        }
                        // For critical errors (like unregistered client), disconnect
                        if matches!(e, HandlerError::AuthenticationFailed(_)) {
                            break;
                        }
                    }
                }
            }
            Err(e) => {
                error!("WebSocket error: {}", e);
                break;
            }
        }
    }
    
    // Cleanup: remove peer from routing table
    if let Some(ref peer_id) = peer_id {
        info!("Removing peer from routing table: {}", peer_id);
        routing_table.remove(&peer_id).await;
    }
    
    // Cancel the outgoing task
    outgoing_task.abort();
    
    info!("WebSocket connection closed");
    Ok(())
}

/// Process a single WebSocket message using the standardized pattern
///
/// Returns Ok(true) to continue processing, Ok(false) to disconnect gracefully,
/// or Err for error conditions
async fn process_websocket_message(
    message: Message,
    message_tx: &mpsc::UnboundedSender<ServerMessage>,
    routing_table: &RoutingTable,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, HandlerError> {
    match message {
        Message::Text(text) => {
            // Deserialize the message using E2EE library types
            let client_message: ClientMessage = serde_json::from_str(&text)
                .map_err(|e| HandlerError::InvalidRequest(format!("Invalid JSON: {}", e)))?;
            
            process_client_message(client_message, message_tx, routing_table, peer_id, is_registered).await
        }
        Message::Binary(data) => {
            // For binary messages, try to deserialize as JSON
            let client_message: ClientMessage = serde_json::from_slice(&data)
                .map_err(|e| HandlerError::InvalidRequest(format!("Invalid binary JSON: {}", e)))?;
            
            process_client_message(client_message, message_tx, routing_table, peer_id, is_registered).await
        }
        Message::Ping(_data) => {
            // For WebSocket ping frames, we need to handle this at the WebSocket level
            // This is typically handled automatically by the WebSocket implementation
            Ok(true)
        }
        Message::Pong(_) => {
            // Pong received, continue processing
            Ok(true)
        }
        Message::Close(_) => {
            info!("Received close frame");
            Ok(false)
        }
        Message::Continuation(_) => {
            // Handle continuation frames
            Ok(true)
        }
        Message::Nop => {
            // No operation
            Ok(true)
        }
    }
}

/// Process a deserialized client message using E2EE library types
async fn process_client_message(
    message: ClientMessage,
    message_tx: &mpsc::UnboundedSender<ServerMessage>,
    routing_table: &RoutingTable,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, HandlerError> {
    match message {
        ClientMessage::Register { peer_id: new_peer_id } => {
            info!("Peer registration request: {}", new_peer_id);
            
            // Check if already registered
            if *is_registered {
                return Err(HandlerError::InvalidRequest(
                    "Peer is already registered. Multiple registrations not allowed.".to_string(),
                ));
            }
            
            // PeerId validation is already enforced at the type level during deserialization
            // No need for additional validation here
            
            // Check if peer_id is already taken
            if routing_table.contains_peer(&new_peer_id).await {
                return Err(HandlerError::InvalidRequest(format!(
                    "Peer ID '{}' is already in use",
                    new_peer_id
                )));
            }
            
            // Store the peer in the routing table
            routing_table.insert(new_peer_id.clone(), message_tx.clone()).await;
            
            // Update connection state
            *peer_id = Some(new_peer_id.clone());
            *is_registered = true;
            
            info!("Peer '{}' successfully registered", new_peer_id);
            
            // Send success response
            let response = ServerMessage::RegisterSuccess {
                peer_id: new_peer_id,
            };
            message_tx.send(response).map_err(|_| {
                HandlerError::InternalError("Failed to send registration response".to_string())
            })?;
            
            Ok(true)
        }
        ClientMessage::Relay { recipient_id, payload } => {
            // Enforce registration requirement
            if !*is_registered {
                return Err(HandlerError::AuthenticationFailed(
                    "Must register before sending messages".to_string(),
                ));
            }
            
            let sender_id = peer_id.as_ref().unwrap(); // Safe because is_registered is true
            info!("Message relay request from '{}' to '{}'", sender_id, recipient_id);
            
            // Construct the relayed message
            let relayed_message = ServerMessage::MessageRelayed {
                sender_id: sender_id.clone(),
                payload,
            };
            
            // Send the message to the recipient using the routing table
            if routing_table.send_to_peer(&recipient_id, relayed_message).await {
                info!("Successfully relayed message from '{}' to '{}'", sender_id, recipient_id);
            } else {
                // Recipient not found or connection failed
                warn!("Failed to relay message to recipient '{}' - not connected or connection error", recipient_id);
                
                // Remove the recipient from routing table in case of connection error
                routing_table.remove(&recipient_id).await;
                
                // Send error back to sender
                let error_response = ServerMessage::Error {
                    reason: format!("Recipient '{}' is not connected or unreachable", recipient_id),
                };
                message_tx.send(error_response).map_err(|_| {
                    HandlerError::InternalError("Failed to send error response".to_string())
                })?;
            }
            
            Ok(true)
        }
        ClientMessage::Ping => {
            // Ping is allowed even without registration
            let response = ServerMessage::Pong;
            message_tx.send(response).map_err(|_| {
                HandlerError::InternalError("Failed to send pong response".to_string())
            })?;
            Ok(true)
        }
        ClientMessage::Disconnect => {
            info!("Client requested disconnection");
            Ok(false)
        }
    }
}

/// Send a server message through the actix-ws session
async fn send_server_message(
    session: &mut Session,
    message: ServerMessage,
) -> Result<()> {
    let json_text = serde_json::to_string(&message)?;
    session.text(json_text).await?;
    Ok(())
}

/// Health check endpoint
async fn health_check() -> ActixResult<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "healthy",
        "service": "indidus-e2ee-actix-integration",
        "endpoints": {
            "websocket": "/ws",
            "health": "/health"
        }
    })))
}

/// Main server entry point
#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();
    
    info!("Starting Indidus E2EE Server with Actix Web integration");
    
    // Create application state
    let app_state = Data::new(AppState::new());
    
    // Start the HTTP server
    let server = HttpServer::new(move || {
        App::new()
            .app_data(app_state.clone())
            .wrap(Logger::default())
            .route("/health", web::get().to(health_check))
            .route("/ws", web::get().to(websocket_handler))
    })
    .bind("127.0.0.1:8080")?;
    
    info!("Server starting on http://127.0.0.1:8080");
    info!("WebSocket endpoint available at ws://127.0.0.1:8080/ws");
    info!("Health check available at http://127.0.0.1:8080/health");
    
    server.run().await?;
    
    Ok(())
}