[package]
name = "actix_integration"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core E2EE server dependency
indidus_e2ee_server = { path = "../../indidus_e2ee_server" }
indidus_shared = { path = "../../indidus_shared" }

# Web framework and async runtime
actix-web = { version = "4.0", features = ["rustls"] }
actix-ws = "0.2"
tokio = { version = "1.0", features = ["full"] }
futures-util = "0.3"

# Serialization and utilities
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
base64 = "0.21"

# Logging
env_logger = "0.10"
log = "0.4"
tracing = "0.1"

# Error handling
anyhow = "1.0"