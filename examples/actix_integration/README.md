# Actix Integration Example

This example demonstrates how to integrate the `indidus_e2ee_server` library into an existing Actix-web application, providing end-to-end encryption capabilities through both WebSocket and HTTP endpoints.

## Overview

The integration showcases a production-ready pattern for adding E2EE functionality to an existing web application without disrupting the current architecture. It demonstrates:

- **State Management**: Using Actix's `web::Data` for dependency injection
- **WebSocket Integration**: Real-time E2EE messaging capabilities
- **HTTP API Integration**: File transfer and message relay endpoints
- **Error Handling**: Proper conversion of E2EE errors to HTTP responses
- **Route Organization**: Clean separation of concerns using Actix scopes

## Architecture

### State Management Pattern

The example uses an `AppState` struct that combines all E2EE services:

```rust
#[derive(Clone)]
pub struct AppState {
    pub server_state: Arc<ServerState>,      // Core E2EE logic
    pub storage_service: Arc<ChunkStorageService>, // File storage
    pub routing_table: RoutingTable,         // WebSocket routing
}
```

This state is registered with Actix using `web::Data::new(app_state)` and becomes available to all handlers through dependency injection.

### Handler Adaptation Pattern

Each handler follows a consistent pattern:

1. **Extract State**: Use `web::Data<AppState>` to access services
2. **Validate Input**: Perform request validation and sanitization
3. **Delegate to Core Logic**: Call the appropriate E2EE server function
4. **Convert Response**: Transform results to HTTP responses

Example:
```rust
async fn handle_message_relay(
    app_state: web::Data<AppState>,
    message: web::Json<ApiMessage>,
) -> HttpResponse {
    // Validation
    if message.payload.is_empty() {
        return HttpResponse::BadRequest().json(/* error */);
    }
    
    // Delegate to E2EE logic
    let success = app_state.routing_table
        .send_to_peer(&message.recipient_id, server_message)
        .await;
    
    // Convert to HTTP response
    if success {
        HttpResponse::Ok().json(/* success response */)
    } else {
        HttpResponse::NotFound().json(/* error response */)
    }
}
```

## Available Endpoints

### Root Endpoint
- **GET /** - Application health check with endpoint documentation
- Returns HTML page showing the application is running with E2EE capabilities

### WebSocket Endpoint
- **GET /ws** - WebSocket upgrade for real-time E2EE messaging
- Provides echo functionality for demonstration (production would use full E2EE logic)

### HTTP API Endpoints

#### Message Relay
- **POST /api/message** - Relay encrypted messages between peers
- Request body: `{"recipient_id": "peer123", "payload": [1,2,3], "sender_id": "peer456"}`
- Response: Success confirmation or peer not found error

#### File Transfer
- **POST /api/file/initiate** - Start a new file transfer session
- **POST /api/file/upload/{transfer_id}** - Upload file chunks (base64 encoded)
- **GET /api/file/download/{transfer_id}/{chunk_index}** - Download file chunks

## Running the Example

### Prerequisites

1. Rust 1.70+ installed
2. All workspace dependencies available

### Build and Run

```bash
# From the project root
cargo run --example actix_integration

# Or from the example directory
cd examples/actix_integration
cargo run
```

The server will start on `http://127.0.0.1:8080`.

### Testing the Integration

#### 1. Health Check
```bash
curl http://127.0.0.1:8080/
```

#### 2. WebSocket Connection
```javascript
// In browser console or WebSocket client
const ws = new WebSocket('ws://127.0.0.1:8080/ws');
ws.onopen = () => ws.send('Hello E2EE Server!');
ws.onmessage = (event) => console.log('Received:', event.data);
```

#### 3. Message Relay
```bash
curl -X POST http://127.0.0.1:8080/api/message \
  -H "Content-Type: application/json" \
  -d '{"recipient_id": "peer123", "payload": [72,101,108,108,111], "sender_id": "peer456"}'
```

#### 4. File Transfer
```bash
# Initiate transfer
curl -X POST http://127.0.0.1:8080/api/file/initiate \
  -H "Content-Type: application/json" \
  -d '{"transfer_id": "file123", "total_chunks": 3, "metadata": "test.txt"}'

# Upload chunk (base64 encoded data)
curl -X POST http://127.0.0.1:8080/api/file/upload/file123 \
  -H "Content-Type: application/json" \
  -d '{"chunk_index": 0, "chunk_data": "SGVsbG8gV29ybGQ="}'

# Download chunk
curl http://127.0.0.1:8080/api/file/download/file123/0
```

## Integration Patterns

### Adding to Existing Applications

To integrate E2EE capabilities into your existing Actix application:

1. **Add Dependencies**: Include `indidus_e2ee_server` in your `Cargo.toml`

2. **Create State Adapter**: Define an `AppState` struct combining your existing state with E2EE services

3. **Add Route Handlers**: Create wrapper functions that extract E2EE services and delegate to the core logic

4. **Register Routes**: Add the E2EE endpoints to your existing route configuration

5. **Handle Errors**: Implement proper error conversion from E2EE errors to HTTP responses

### Production Considerations

- **WebSocket Scaling**: For production, implement proper WebSocket connection management and load balancing
- **Storage Backend**: Configure the `ChunkStorageService` with appropriate storage backends (filesystem, S3, etc.)
- **Authentication**: Add authentication middleware to protect E2EE endpoints
- **Rate Limiting**: Implement rate limiting for file upload endpoints
- **Monitoring**: Add metrics and logging for E2EE operations
- **TLS**: Ensure all connections use HTTPS/WSS in production

## Key Benefits

1. **Non-Intrusive**: Adds E2EE capabilities without modifying existing application structure
2. **Type Safety**: Leverages Rust's type system for safe state management
3. **Performance**: Minimal overhead through efficient Arc-based state sharing
4. **Maintainable**: Clear separation between web framework and E2EE logic
5. **Testable**: Each handler can be tested independently with mock state

## Comparison with Other Frameworks

This Actix integration demonstrates similar patterns to the Axum integration but with Actix-specific features:

- **State Management**: Uses `web::Data` instead of Axum's `State` extractor
- **Route Organization**: Leverages Actix's `scope()` for better route organization
- **WebSocket Handling**: Uses `actix-ws` instead of Axum's built-in WebSocket support
- **Error Handling**: Follows Actix's error handling patterns and response builders

The core E2EE logic remains the same, showcasing the framework-agnostic design of the `indidus_e2ee_server` library.