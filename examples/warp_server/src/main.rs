//! # Warp Integration Example for Indidus E2EE Server
//!
//! This example demonstrates how to integrate the Indidus E2EE Server with the Warp framework.
//! It shows the minimal setup required to create a WebSocket-based E2EE messaging server using Warp's
//! filter-based approach.
//!
//! ## Features Demonstrated
//!
//! - **WebSocket Integration**: Setting up WebSocket endpoints with Warp filters
//! - **Message Relay**: Relaying encrypted messages between connected clients
//! - **Connection Management**: Managing client connections and routing
//! - **Filter Composition**: Using Warp's filter-based architecture
//!
//! ## Usage
//!
//! ```bash
//! cargo run --example warp_server
//! ```
//!
//! The server will start on `http://localhost:3030` with a WebSocket endpoint at `/ws`.
//!
//! ## Testing the Server
//!
//! You can test the WebSocket connection using a WebSocket client or the provided
//! basic messaging example:
//!
//! ```bash
//! # In another terminal, run the basic messaging example
//! cargo run --example basic_messaging -- --server-url ws://localhost:3030/ws
//! ```
//!
//! ## Integration Pattern
//!
//! This example shows the standard integration pattern for Warp:
//!
//! 1. **Initialize Server State**: Create shared state including routing table
//! 2. **Create Filters**: Set up Warp filters for WebSocket and other endpoints
//! 3. **Handle Connections**: Implement WebSocket message handling using the E2EE library's types
//! 4. **Error Management**: Implement proper error handling and logging
//!
//! The key integration points are:
//! - Creating a `RoutingTable` for managing client connections
//! - Using Warp filters to handle WebSocket upgrades
//! - Implementing the message handling logic using the E2EE library's message types

use anyhow::Result;
use futures_util::{SinkExt, StreamExt};
use indidus_e2ee_server::{RoutingTable, ClientMessage, ServerMessage};
use indidus_shared::validation::PeerId;
use serde_json;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{error, info, warn};
use warp::ws::{Message, WebSocket};
use warp::Filter;

/// Application state that combines all E2EE services for Warp integration
#[derive(Clone)]
pub struct AppState {
    /// Routing table for managing WebSocket connections
    pub routing_table: RoutingTable,
}

impl AppState {
    /// Create new application state with initialized services
    pub fn new() -> Self {
        Self {
            routing_table: RoutingTable::new(),
        }
    }
}

/// Main server entry point
#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();
    
    info!("Starting Indidus E2EE Server with Warp integration");
    
    // Create application state
    let app_state = Arc::new(AppState::new());
    
    // Create Warp filters
    let routes = create_routes(app_state);
    
    info!("Server starting on http://127.0.0.1:3030");
    info!("WebSocket endpoint available at ws://127.0.0.1:3030/ws");
    info!("Health check available at http://127.0.0.1:3030/health");
    
    // Start the server
    warp::serve(routes)
        .run(([127, 0, 0, 1], 3030))
        .await;
    
    Ok(())
}

/// Create all Warp routes and filters
fn create_routes(
    app_state: Arc<AppState>,
) -> impl Filter<Extract = impl warp::Reply, Error = warp::Rejection> + Clone {
    // Health check endpoint
    let health = warp::path("health")
        .and(warp::get())
        .map(|| {
            warp::reply::json(&serde_json::json!({
                "status": "healthy",
                "service": "indidus-e2ee-warp-integration",
                "endpoints": {
                    "websocket": "/ws",
                    "health": "/health"
                }
            }))
        });
    
    // WebSocket endpoint
    let websocket = warp::path("ws")
        .and(warp::ws())
        .and(with_app_state(app_state))
        .map(|ws: warp::ws::Ws, app_state: Arc<AppState>| {
            ws.on_upgrade(move |socket| handle_websocket_connection(socket, app_state))
        });
    
    // Root endpoint
    let root = warp::path::end()
        .and(warp::get())
        .map(|| "Warp server with E2EE capabilities is running!");
    
    // Combine all routes
    health.or(websocket).or(root)
}

/// Helper filter to inject app state
fn with_app_state(
    app_state: Arc<AppState>,
) -> impl Filter<Extract = (Arc<AppState>,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || app_state.clone())
}

/// Handle an individual WebSocket connection
/// 
/// This function implements the E2EE message handling logic using the library's
/// message types and routing table, adapted for Warp WebSockets.
async fn handle_websocket_connection(ws: WebSocket, app_state: Arc<AppState>) {
    info!("New WebSocket connection established");
    
    // Split the WebSocket into read and write halves
    let (mut ws_tx, mut ws_rx) = ws.split();
    
    // Create a channel for sending messages to this client
    let (message_tx, mut message_rx) = mpsc::unbounded_channel::<ServerMessage>();
    
    // Track the peer ID and registration state
    let mut peer_id: Option<PeerId> = None;
    let mut is_registered = false;
    
    let routing_table = app_state.routing_table.clone();
    
    // Spawn a task to handle outgoing messages
    let outgoing_task = tokio::spawn(async move {
        while let Some(message) = message_rx.recv().await {
            if let Err(e) = send_server_message(&mut ws_tx, message).await {
                error!("Failed to send message to client: {}", e);
                break;
            }
        }
    });
    
    // Main message processing loop
    while let Some(message_result) = ws_rx.next().await {
        match message_result {
            Ok(message) => {
                match process_websocket_message(
                    message, 
                    &message_tx, 
                    &routing_table, 
                    &mut peer_id, 
                    &mut is_registered
                ).await {
                    Ok(should_continue) => {
                        if !should_continue {
                            info!("Client requested disconnection");
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Error processing message: {}", e);
                        // Send error response to client
                        let error_msg = ServerMessage::Error {
                            reason: e.to_string(),
                        };
                        if let Err(send_err) = message_tx.send(error_msg) {
                            error!("Failed to send error message: {}", send_err);
                            break;
                        }
                        // For critical errors (like unregistered client), disconnect
                        if e.contains("Must register") {
                            break;
                        }
                    }
                }
            }
            Err(e) => {
                error!("WebSocket error: {}", e);
                break;
            }
        }
    }
    
    // Cleanup: remove peer from routing table
    if let Some(ref peer_id) = peer_id {
        info!("Removing peer from routing table: {}", peer_id);
        routing_table.remove(&peer_id).await;
    }
    
    // Cancel the outgoing task
    outgoing_task.abort();
    
    info!("WebSocket connection closed");
}

/// Process a single WebSocket message
/// 
/// Returns Ok(true) to continue processing, Ok(false) to disconnect gracefully,
/// or Err for error conditions
async fn process_websocket_message(
    message: Message,
    message_tx: &mpsc::UnboundedSender<ServerMessage>,
    routing_table: &RoutingTable,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, String> {
    if message.is_text() {
        // Handle text messages
        let text = message.to_str().map_err(|_| "Invalid UTF-8 in text message")?;
        let client_message: ClientMessage = serde_json::from_str(text)
            .map_err(|e| format!("Invalid JSON: {}", e))?;
        
        process_client_message(client_message, message_tx, routing_table, peer_id, is_registered).await
    } else if message.is_binary() {
        // Handle binary messages
        let data = message.as_bytes();
        let client_message: ClientMessage = serde_json::from_slice(data)
            .map_err(|e| format!("Invalid binary JSON: {}", e))?;
        
        process_client_message(client_message, message_tx, routing_table, peer_id, is_registered).await
    } else if message.is_ping() {
        // Handle ping frames
        Ok(true)
    } else if message.is_pong() {
        // Handle pong frames
        Ok(true)
    } else if message.is_close() {
        // Handle close frames
        info!("Received close frame");
        Ok(false)
    } else {
        // Unknown message type
        Ok(true)
    }
}

/// Process a deserialized client message
async fn process_client_message(
    message: ClientMessage,
    message_tx: &mpsc::UnboundedSender<ServerMessage>,
    routing_table: &RoutingTable,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, String> {
    match message {
        ClientMessage::Register { peer_id: new_peer_id } => {
            info!("Peer registration request: {}", new_peer_id);
            
            // Check if already registered
            if *is_registered {
                return Err("Peer is already registered. Multiple registrations not allowed.".to_string());
            }
            
            // PeerId validation is already enforced at the type level during deserialization
            // No need for additional validation here
            
            // Check if peer_id is already taken
            if routing_table.contains_peer(&new_peer_id).await {
                return Err(format!("Peer ID '{}' is already in use", new_peer_id));
            }
            
            // Store the peer in the routing table
            routing_table.insert(new_peer_id.clone(), message_tx.clone()).await;
            
            // Update connection state
            *peer_id = Some(new_peer_id.clone());
            *is_registered = true;
            
            info!("Peer '{}' successfully registered", new_peer_id);
            
            // Send success response
            let response = ServerMessage::RegisterSuccess {
                peer_id: new_peer_id,
            };
            message_tx.send(response)
                .map_err(|_| "Failed to send registration response".to_string())?;
            
            Ok(true)
        }
        ClientMessage::Relay { recipient_id, payload } => {
            // Enforce registration requirement
            if !*is_registered {
                return Err("Must register before sending messages".to_string());
            }
            
            let sender_id = peer_id.as_ref().unwrap(); // Safe because is_registered is true
            info!("Message relay request from '{}' to '{}'", sender_id, recipient_id);
            
            // Construct the relayed message
            let relayed_message = ServerMessage::MessageRelayed {
                sender_id: sender_id.clone(),
                payload,
            };
            
            // Send the message to the recipient using the routing table
            if routing_table.send_to_peer(&recipient_id, relayed_message).await {
                info!("Successfully relayed message from '{}' to '{}'", sender_id, recipient_id);
            } else {
                // Recipient not found or connection failed
                warn!("Failed to relay message to recipient '{}' - not connected or connection error", recipient_id);
                
                // Remove the recipient from routing table in case of connection error
                routing_table.remove(&recipient_id).await;
                
                // Send error back to sender
                let error_response = ServerMessage::Error {
                    reason: format!("Recipient '{}' is not connected or unreachable", recipient_id),
                };
                message_tx.send(error_response)
                    .map_err(|_| "Failed to send error response".to_string())?;
            }
            
            Ok(true)
        }
        ClientMessage::Ping => {
            // Ping is allowed even without registration
            let response = ServerMessage::Pong;
            message_tx.send(response)
                .map_err(|_| "Failed to send pong response".to_string())?;
            Ok(true)
        }
        ClientMessage::Disconnect => {
            info!("Client requested disconnection");
            Ok(false)
        }
    }
}

/// Send a server message through the Warp WebSocket
async fn send_server_message(
    ws_tx: &mut futures_util::stream::SplitSink<WebSocket, Message>,
    message: ServerMessage,
) -> Result<()> {
    let json_text = serde_json::to_string(&message)?;
    ws_tx.send(Message::text(json_text)).await?;
    Ok(())
}