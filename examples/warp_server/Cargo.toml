[package]
name = "warp_server"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core E2EE server dependency
indidus_e2ee_server = { path = "../../indidus_e2ee_server" }
indidus_shared = { path = "../../indidus_shared" }

# Web framework and async runtime
warp = "0.3"
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.21"
futures = "0.3"

# Serialization and utilities
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
anyhow = "1.0"

# Additional utilities for Warp integration
futures-util = "0.3"
bytes = "1.0"
base64 = "0.21"
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }