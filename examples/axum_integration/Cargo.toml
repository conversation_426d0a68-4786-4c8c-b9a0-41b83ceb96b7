[package]
name = "axum_integration"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core E2EE server dependency
indidus_e2ee_server = { path = "../../indidus_e2ee_server" }
indidus_shared = { path = "../../indidus_shared" }

# Web framework and async runtime
axum = { version = "0.7", features = ["ws", "multipart"] }
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.21"
futures-util = "0.3"

# Serialization and utilities
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
anyhow = "1.0"