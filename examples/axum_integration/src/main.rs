//! # Axum Integration Example for Indidus E2EE Server
//!
//! This example demonstrates the **standardized framework-specific handler pattern**
//! for integrating the `indidus_e2ee_server` library into an Axum web application.
//!
//! ## Key Integration Patterns
//!
//! - **Application State**: Centralized state management with E2EE services
//! - **WebSocket Handlers**: Framework-native async fn handlers for real-time messaging
//! - **Message Processing**: Direct use of E2EE library types (ClientMessage/ServerMessage)
//! - **Connection Management**: Routing table integration with proper cleanup
//! - **Error Handling**: Framework-specific error types and responses
//!
//! ## Usage
//!
//! ```bash
//! cargo run --example axum_integration
//! ```
//!
//! The server will start on `http://127.0.0.1:3000` with the following endpoints:
//! - `GET /` - Simple health check showing the app is running
//! - `GET /ws` - WebSocket endpoint for E2EE messaging
//! - `POST /v1/files/chunk` - HTTP endpoint for file chunk uploads
use anyhow::Result;
use axum::{
    extract::{ws::{Message, WebSocket, WebSocketUpgrade}, State},
    response::Response,
    routing::{get, post},
    Router,
};
use futures_util::{sink::SinkExt, stream::StreamExt};
use indidus_e2ee_server::{
    handle_file_chunk,
    handlers::{
        error::HandlerError,
        websocket::{ClientMessage, RoutingTable, ServerMessage},
    },
    storage::{ChunkStorageService, StorageConfig},
    FileChunkError, FileChunkResponse,
};
use indidus_shared::validation::PeerId;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{error, info, warn};

/// Application state that combines E2EE services for Axum integration
/// 
/// This follows the standardized pattern of centralizing all E2EE-related
/// services in a single state structure that can be shared across handlers.
#[derive(Clone)]
struct AppState {
    /// Routing table for managing WebSocket connections and message relay
    routing_table: RoutingTable,
    /// Storage service for handling file chunk uploads
    storage_service: Arc<ChunkStorageService>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize structured logging for the application
    tracing_subscriber::fmt::init();

    info!("Starting Axum integration example with standardized handler pattern...");

    // Step 1: Initialize E2EE services
    // Create routing table for managing WebSocket connections and message relay
    let routing_table = RoutingTable::new();
    info!("Routing table initialized for WebSocket connection management");

    // Create storage service for handling file chunk uploads
    let storage_config = StorageConfig::default();
    let storage_service = Arc::new(ChunkStorageService::new(storage_config));
    info!("Storage service initialized for file transfer operations");

    // Step 2: Combine services into shared application state
    // This follows the standardized pattern of centralizing E2EE services
    // while maintaining type safety with Axum's State extractor
    let app_state = AppState {
        routing_table,
        storage_service,
    };

    // Step 3: Create Axum router with standardized handler pattern
    // This demonstrates the recommended integration approach for E2EE capabilities
    let app: Router = Router::new()
        // Root route to show this is an existing application
        .route("/", get(root_handler))
        // WebSocket endpoint using standardized framework-specific handler
        .route("/ws", get(websocket_handler))
        // HTTP endpoint for secure file chunk uploads
        .route("/v1/files/chunk", post(file_chunk_handler_wrapper))
        // Health check endpoint
        .route("/health", get(health_check))
        // Inject our combined application state using Axum's with_state pattern
        .with_state(app_state);

    info!("Axum router created with standardized E2EE handler pattern");
    info!("Available endpoints:");
    info!("  GET  /           - Application health check");
    info!("  GET  /ws         - WebSocket for E2EE messaging (standardized pattern)");
    info!("  POST /v1/files/chunk - File chunk upload endpoint");
    info!("  GET  /health     - Server health status");

    // Step 4: Start the server
    info!("✅ Starting Axum server with standardized E2EE handler pattern!");

    let listener = tokio::net::TcpListener::bind("127.0.0.1:3000").await?;
    info!("Server listening on http://127.0.0.1:3000");
    info!("WebSocket endpoint: ws://127.0.0.1:3000/ws");

    axum::serve(listener, app.into_make_service()).await?;

    Ok(())
}

/// Root handler demonstrating that this is an existing Axum application
/// with E2EE capabilities added to it
async fn root_handler() -> &'static str {
    "My Axum App is running with E2EE capabilities!"
}

/// Health check endpoint
async fn health_check() -> axum::Json<serde_json::Value> {
    axum::Json(serde_json::json!({
        "status": "healthy",
        "service": "indidus-e2ee-axum-integration",
        "endpoints": {
            "websocket": "/ws",
            "file_chunks": "/v1/files/chunk",
            "health": "/health"
        }
    }))
}

/// WebSocket handler using the standardized framework-specific pattern
///
/// This demonstrates the recommended integration approach:
/// 1. Extract the application state using Axum's State extractor
/// 2. Upgrade the HTTP connection to WebSocket using Axum's native types
/// 3. Handle the connection using framework-specific message processing
/// 4. Use E2EE library types directly (ClientMessage/ServerMessage)
async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(app_state): State<AppState>,
) -> Response {
    info!("WebSocket connection request received");
    
    // Clone the routing table for this connection
    let routing_table = app_state.routing_table.clone();
    
    // Upgrade the HTTP connection to WebSocket using Axum's native upgrade
    ws.on_upgrade(move |socket| async move {
        if let Err(e) = handle_websocket_connection(socket, routing_table).await {
            error!("WebSocket connection handler error: {}", e);
        }
    })
}

/// Handle an individual WebSocket connection using the standardized pattern
///
/// This function implements the E2EE message handling logic using the library's
/// message types and routing table, following the framework-specific approach.
/// It demonstrates the complete lifecycle of a WebSocket connection:
/// 1. Connection setup with message channels
/// 2. Peer registration enforcement
/// 3. Message processing loop using E2EE library types
/// 4. Proper cleanup and error handling
async fn handle_websocket_connection(
    socket: WebSocket,
    routing_table: RoutingTable,
) -> Result<()> {
    info!("New WebSocket connection established");
    
    // Split the WebSocket into read and write halves for concurrent processing
    let (mut sink, mut stream) = socket.split();
    
    // Create a channel for sending messages to this client
    let (message_tx, mut message_rx) = mpsc::unbounded_channel::<ServerMessage>();
    
    // Track the peer ID and registration state
    let mut peer_id: Option<PeerId> = None;
    let mut is_registered = false;
    
    // Spawn a task to handle outgoing messages
    let outgoing_task = tokio::spawn(async move {
        while let Some(message) = message_rx.recv().await {
            if let Err(e) = send_server_message(&mut sink, message).await {
                error!("Failed to send message to client: {}", e);
                break;
            }
        }
    });
    
    // Main message processing loop
    while let Some(message_result) = stream.next().await {
        match message_result {
            Ok(message) => {
                match process_websocket_message(
                    message,
                    &message_tx,
                    &routing_table,
                    &mut peer_id,
                    &mut is_registered,
                )
                .await
                {
                    Ok(should_continue) => {
                        if !should_continue {
                            info!("Client requested disconnection");
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Error processing message: {}", e);
                        // Send error response to client
                        let error_msg = ServerMessage::Error {
                            reason: e.to_string(),
                        };
                        if let Err(send_err) = message_tx.send(error_msg) {
                            error!("Failed to send error message: {}", send_err);
                            break;
                        }
                        // For critical errors (like unregistered client), disconnect
                        if matches!(e, HandlerError::AuthenticationFailed(_)) {
                            break;
                        }
                    }
                }
            }
            Err(e) => {
                error!("WebSocket error: {}", e);
                break;
            }
        }
    }
    
    // Cleanup: remove peer from routing table
    if let Some(ref peer_id) = peer_id {
        info!("Removing peer from routing table: {}", peer_id);
        routing_table.remove(&peer_id).await;
    }
    
    // Cancel the outgoing task
    outgoing_task.abort();
    
    info!("WebSocket connection closed");
    Ok(())
}

/// Process a single WebSocket message using the standardized pattern
///
/// Returns Ok(true) to continue processing, Ok(false) to disconnect gracefully,
/// or Err for error conditions
async fn process_websocket_message(
    message: Message,
    message_tx: &mpsc::UnboundedSender<ServerMessage>,
    routing_table: &RoutingTable,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, HandlerError> {
    match message {
        Message::Text(text) => {
            // Deserialize the message using E2EE library types
            let client_message: ClientMessage = serde_json::from_str(&text)
                .map_err(|e| HandlerError::InvalidRequest(format!("Invalid JSON: {}", e)))?;
            
            process_client_message(
                client_message,
                message_tx,
                routing_table,
                peer_id,
                is_registered,
            )
            .await
        }
        Message::Binary(data) => {
            // For binary messages, try to deserialize as JSON
            let client_message: ClientMessage = serde_json::from_slice(&data)
                .map_err(|e| HandlerError::InvalidRequest(format!("Invalid binary JSON: {}", e)))?;
            
            process_client_message(
                client_message,
                message_tx,
                routing_table,
                peer_id,
                is_registered,
            )
            .await
        }
        Message::Ping(_data) => {
            // WebSocket ping frames are handled automatically by Axum
            Ok(true)
        }
        Message::Pong(_) => {
            // Pong received, continue processing
            Ok(true)
        }
        Message::Close(_) => {
            info!("Received close frame");
            Ok(false)
        }
    }
}

/// Process a deserialized client message using E2EE library types
async fn process_client_message(
    message: ClientMessage,
    message_tx: &mpsc::UnboundedSender<ServerMessage>,
    routing_table: &RoutingTable,
    peer_id: &mut Option<PeerId>,
    is_registered: &mut bool,
) -> Result<bool, HandlerError> {
    match message {
        ClientMessage::Register {
            peer_id: new_peer_id,
        } => {
            info!("Peer registration request: {}", new_peer_id);
            
            // Check if already registered
            if *is_registered {
                return Err(HandlerError::InvalidRequest(
                    "Peer is already registered. Multiple registrations not allowed.".to_string(),
                ));
            }
            
            // PeerId validation is already enforced at the type level during deserialization
            // No need for additional validation here
            
            // Check if peer_id is already taken
            if routing_table.contains_peer(&new_peer_id).await {
                return Err(HandlerError::InvalidRequest(format!(
                    "Peer ID '{}' is already in use",
                    new_peer_id
                )));
            }
            
            // Store the peer in the routing table
            routing_table
                .insert(new_peer_id.clone(), message_tx.clone())
                .await;
            
            // Update connection state
            *peer_id = Some(new_peer_id.clone());
            *is_registered = true;
            
            info!("Peer '{}' successfully registered", new_peer_id);
            
            // Send success response
            let response = ServerMessage::RegisterSuccess {
                peer_id: new_peer_id,
            };
            message_tx.send(response).map_err(|_| {
                HandlerError::InternalError("Failed to send registration response".to_string())
            })?;
            
            Ok(true)
        }
        ClientMessage::Relay {
            recipient_id,
            payload,
        } => {
            // Enforce registration requirement
            if !*is_registered {
                return Err(HandlerError::AuthenticationFailed(
                    "Must register before sending messages".to_string(),
                ));
            }
            
            let sender_id = peer_id.as_ref().unwrap(); // Safe because is_registered is true
            info!(
                "Message relay request from '{}' to '{}'",
                sender_id, recipient_id
            );
            
            // Construct the relayed message
            let relayed_message = ServerMessage::MessageRelayed {
                sender_id: sender_id.clone(),
                payload,
            };
            
            // Send the message to the recipient using the routing table
            if routing_table
                .send_to_peer(&recipient_id, relayed_message)
                .await
            {
                info!(
                    "Successfully relayed message from '{}' to '{}'",
                    sender_id, recipient_id
                );
            } else {
                // Recipient not found or connection failed
                warn!(
                    "Failed to relay message to recipient '{}' - not connected or connection error",
                    recipient_id
                );
                
                // Remove the recipient from routing table in case of connection error
                routing_table.remove(&recipient_id).await;
                
                // Send error back to sender
                let error_response = ServerMessage::Error {
                    reason: format!(
                        "Recipient '{}' is not connected or unreachable",
                        recipient_id
                    ),
                };
                message_tx.send(error_response).map_err(|_| {
                    HandlerError::InternalError("Failed to send error response".to_string())
                })?;
            }
            
            Ok(true)
        }
        ClientMessage::Ping => {
            // Ping is allowed even without registration
            let response = ServerMessage::Pong;
            message_tx.send(response).map_err(|_| {
                HandlerError::InternalError("Failed to send pong response".to_string())
            })?;
            Ok(true)
        }
        ClientMessage::Disconnect => {
            info!("Client requested disconnection");
            Ok(false)
        }
    }
}

/// Helper function to send a ServerMessage to the client using Axum WebSocket
async fn send_server_message(
    sink: &mut futures_util::stream::SplitSink<WebSocket, Message>,
    message: ServerMessage,
) -> Result<()> {
    let json = serde_json::to_string(&message)?;
    sink.send(Message::Text(json)).await?;
    Ok(())
}

/// File chunk handler wrapper that extracts storage service from AppState
///
/// This demonstrates the integration pattern for HTTP endpoints:
/// 1. Extract the application state using Axum's State extractor
/// 2. Extract the specific service needed (storage_service in this case)
/// 3. Delegate to the indidus_e2ee_server's handle_file_chunk function
/// 4. Return the result with proper error handling
async fn file_chunk_handler_wrapper(
    State(app_state): State<AppState>,
    multipart: axum::extract::Multipart,
) -> Result<
    axum::response::Json<FileChunkResponse>,
    (axum::http::StatusCode, axum::response::Json<FileChunkError>),
> {
    // Delegate to the original handler with the extracted storage service
    // This maintains the original function signature while adapting to our state structure
    handle_file_chunk(axum::extract::State(app_state.storage_service), multipart).await
}
