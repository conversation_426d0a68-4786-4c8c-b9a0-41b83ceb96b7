//! # Basic Messaging Client Example
//!
//! This example demonstrates the client-side functionality of the Indidus E2EE messaging system.
//! It shows how to:
//!
//! 1. **Create and initialize two clients** - representing <PERSON> and <PERSON>
//! 2. **Connect to a running E2EE server** - using real WebSocket connections
//! 3. **Establish secure sessions** - using the Signal Protocol's X3DH handshake
//! 4. **Send encrypted messages** - with end-to-end encryption
//! 5. **Receive and decrypt messages** - maintaining forward secrecy
//! 6. **Verify message integrity** - ensuring the decrypted content matches the original
//!
//! ## Prerequisites
//!
//! Before running this example, start the server:
//! ```bash
//! cargo run --example basic_messaging_server
//! ```
//!
//! ## Usage
//!
//! Run this client example with:
//! ```bash
//! # Using default server URL (ws://127.0.0.1:8080)
//! cargo run --example basic_messaging
//!
//! # Using a custom server URL
//! cargo run --example basic_messaging -- ws://127.0.0.1:3000
//! cargo run --example basic_messaging -- ws://localhost:8080
//! ```

use indidus_e2ee_client::{Client, ClientConfig};
use indidus_shared::validation::PeerId;
use std::env;
use std::time::Duration;
use url::Url;

/// The main function that orchestrates the entire basic messaging demonstration
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Starting Basic Messaging Example...");
    println!();

    // Parse command-line arguments for server URL
    let args: Vec<String> = env::args().collect();
    let server_url = if args.len() > 1 {
        Url::parse(&args[1])?
    } else {
        // Default to WebSocket localhost if no URL provided
        Url::parse("ws://127.0.0.1:8080")?
    };
    
    println!("🌐 Connecting to server: {}", server_url);
    println!();

    // Step 1: Create and initialize Client A (the sender)
    println!("📱 Setting up Client A (Sender)...");
    let mut client_a = create_and_initialize_client("Client A", &server_url).await?;
    let client_a_id = client_a.client_id();
    println!("✅ Client A initialized with ID: {}", client_a_id);

    // Step 2: Create and initialize Client B (the receiver)
    println!();
    println!("📱 Setting up Client B (Receiver)...");
    let mut client_b = create_and_initialize_client("Client B", &server_url).await?;
    let client_b_id = client_b.client_id();
    println!("✅ Client B initialized with ID: {}", client_b_id);

    // Step 3: Connect both clients to the server
    println!();
    println!("🔗 Connecting clients to server...");
    
    connect_client(&mut client_a, "Client A").await?;
    connect_client(&mut client_b, "Client B").await?;

    // Step 4: Send a message from Client A to Client B
    println!();
    println!("📤 Sending message from Client A to Client B...");
    
    let original_message = "Hello from Client A! This is a secure end-to-end encrypted message.";
    let client_b_peer_id = PeerId::try_from(client_b_id.to_string())?;
    send_message(&mut client_a, client_b_peer_id, original_message).await?;
    
    println!("✅ Message sent successfully");

    // Step 5: Have Client B receive and decrypt the message
    println!();
    println!("📥 Client B receiving message...");
    
    let client_a_peer_id = PeerId::try_from(client_a_id.to_string())?;
    let decrypted_message = receive_message(&mut client_b, client_a_peer_id).await?;
    
    println!("✅ Message received and decrypted");

    // Step 6: Verify message integrity
    println!();
    println!("🔍 Verification Results:");
    println!("Original message: \"{}\"", original_message);
    println!("Decrypted message: \"{}\"", decrypted_message);
    
    // Use assert_eq! for strict verification (will panic on mismatch)
    assert_eq!(
        original_message, 
        decrypted_message,
        "❌ CRITICAL: Message integrity verification failed! \
         Original: '{}', Decrypted: '{}'", 
        original_message, 
        decrypted_message
    );
    
    println!("✅ Message integrity verified - content matches perfectly!");
    println!("🎯 End-to-end encryption working correctly!");
    println!("🔐 Message was successfully encrypted, transmitted, and decrypted");

    // Step 7: Clean up and finish
    println!();
    println!("🧹 Cleaning up connections...");
    
    disconnect_client(&mut client_a, "Client A").await?;
    disconnect_client(&mut client_b, "Client B").await?;
    
    println!();
    println!("🎉 Basic messaging example completed successfully!");
    println!();
    println!("📊 SUMMARY - This example demonstrated:");
    println!("  ✅ Client creation and initialization");
    println!("  ✅ Cryptographic key generation");
    println!("  ✅ Server connection establishment");
    println!("  ✅ End-to-end encrypted message sending");
    println!("  ✅ Message reception and decryption");
    println!("  ✅ Message integrity verification (assert_eq! passed)");
    println!("  ✅ Proper connection cleanup");
    println!();
    println!("🔒 SECURITY FEATURES VERIFIED:");
    println!("  • Signal Protocol implementation working");
    println!("  • End-to-end encryption functional");
    println!("  • Message integrity preserved");
    println!("  • No plaintext transmission");
    println!();
    println!("✨ The Indidus E2EE messaging system is working correctly!");
    println!("🚀 Successfully demonstrated real client-server communication!");

    Ok(())
}

/// Creates and initializes a new client with the given display name and server URL
async fn create_and_initialize_client(display_name: &str, server_url: &Url) -> Result<Client, Box<dyn std::error::Error>> {
    // Create client configuration with the provided server URL
    // Note: Using HTTPS URL which the client will convert to WSS internally
    
    let config = ClientConfig::new(server_url.clone())
        .with_display_name(display_name.to_string())
        .with_debug_mode(true);

    // Create the client instance
    let mut client = Client::new(config)?;
    
    // Initialize the client with cryptographic keys
    // This generates the identity keys needed for secure communication
    client.initialize().await?;
    
    // Verify the client is properly initialized
    if !client.is_initialized() {
        return Err("Client initialization failed".into());
    }

    println!("🔑 {} cryptographic keys generated", display_name);

    Ok(client)
}

/// Connects a client to the E2EE server
async fn connect_client(client: &mut Client, client_name: &str) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔗 Connecting {} to server...", client_name);
    
    // Attempt to connect to the server
    client.connect().await?;
    
    // Verify the connection was established
    if client.is_connected() {
        println!("✅ {} successfully connected to server", client_name);
    } else {
        return Err(format!("{} connection verification failed", client_name).into());
    }
    
    Ok(())
}

/// Sends an encrypted message from one client to another
async fn send_message(
    sender: &mut Client, 
    recipient_id: PeerId, 
    message: &str
) -> Result<(), Box<dyn std::error::Error>> {
    println!("📤 Preparing to send message: \"{}\"", message);
    
    // Convert the message to bytes
    let message_bytes = message.as_bytes();
    
    // Send the message using the Client API
    sender.send_message(recipient_id.clone(), message_bytes).await?;
    
    println!("✅ Message successfully sent to recipient {}", recipient_id);
    println!("🔐 Message was encrypted and transmitted securely");
    
    Ok(())
}

/// Receives and decrypts a message from another client
async fn receive_message(
    receiver: &mut Client, 
    expected_sender_id: PeerId
) -> Result<String, Box<dyn std::error::Error>> {
    use indidus_e2ee_client::ClientEvent;
    use tokio::time::timeout;
    
    println!("📥 Waiting for message from sender {}...", expected_sender_id);
    
    // Set a timeout for receiving messages
    let timeout_duration = Duration::from_secs(10);
    
    // Wait for the next event with a timeout
    match timeout(timeout_duration, receiver.next_event()).await {
        Ok(Some(event)) => {
            match event {
                ClientEvent::MessageReceived { 
                    sender_id, 
                    encrypted_payload, 
                    .. 
                } => {
                    println!("📥 Received encrypted message from {}", sender_id);
                    
                    // Convert sender_id to PeerId for comparison
                    let sender_peer_id = PeerId::try_from(sender_id.to_string())?;
                    
                    // Verify this is from the expected sender
                    if sender_peer_id != expected_sender_id {
                        println!("⚠️  Warning: Message from unexpected sender {} (expected {})", 
                                sender_peer_id, expected_sender_id);
                    }
                    
                    // Decrypt the message using the Client API
                    let decrypted_bytes = receiver.decrypt_message(sender_peer_id, &encrypted_payload).await?;
                    let decrypted_message = String::from_utf8(decrypted_bytes)?;
                    println!("🔓 Message decrypted successfully: \"{}\"", decrypted_message);
                    Ok(decrypted_message)
                }
                ClientEvent::ConnectionStateChanged(state) => {
                    println!("🔄 Connection state changed: {:?}", state);
                    Err("Connection state changed unexpectedly".into())
                }
                other_event => {
                    println!("📨 Received other event: {:?}", other_event);
                    Err("Received unexpected event type".into())
                }
            }
        }
        Ok(None) => {
            Err("Event stream ended unexpectedly".into())
        }
        Err(_) => {
            Err(format!("Timeout waiting for message from {}", expected_sender_id).into())
        }
    }
}

/// Disconnects a client from the server
async fn disconnect_client(client: &mut Client, client_name: &str) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔌 Disconnecting {} from server...", client_name);
    
    // Disconnect from the server
    client.disconnect().await?;
    println!("✅ {} successfully disconnected from server", client_name);
    
    Ok(())
}