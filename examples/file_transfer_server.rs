//! # File Transfer Server Example
//!
//! This example demonstrates how to create a WebSocket server for file transfers using the
//! Indidus E2EE server components. It shows how to:
//!
//! 1. **Initialize the routing table** - for managing client connections
//! 2. **Set up a TCP listener** - to accept incoming connections
//! 3. **Handle WebSocket upgrades** - using the reusable MessageHandler
//! 4. **Process file transfer messages** - with full registration and relay functionality
//!
//! ## Usage
//!
//! Run this server with:
//! ```bash
//! # Using default port (8080)
//! cargo run --example file_transfer_server
//!
//! # Using a custom port
//! cargo run --example file_transfer_server -- --port 3000
//! ```
//!
//! Then connect clients using the file_transfer client example:
//! ```bash
//! cargo run --example file_transfer -- ws://127.0.0.1:8080
//! ```

use async_trait::async_trait;
use indidus_e2ee_server::{
    handle_connection, Message, RoutingTable, WebSocketError, WebSocketStream,
};
use std::env;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio_tungstenite::{
    accept_async, tungstenite::protocol::Message as TungsteniteMessage,
    WebSocketStream as TungsteniteWebSocketStream,
};
use tracing::{error, info};

/// Wrapper around tokio-tungstenite WebSocketStream to implement our WebSocketStream trait
#[derive(Debug)]
struct TungsteniteWebSocketWrapper {
    stream: TungsteniteWebSocketStream<TcpStream>,
    is_active: bool,
}

impl TungsteniteWebSocketWrapper {
    fn new(stream: TungsteniteWebSocketStream<TcpStream>) -> Self {
        Self {
            stream,
            is_active: true,
        }
    }
}

#[async_trait]
impl WebSocketStream for TungsteniteWebSocketWrapper {
    async fn send(&mut self, message: Message) -> Result<(), WebSocketError> {
        if !self.is_active {
            return Err(WebSocketError::ConnectionClosed);
        }

        let tungstenite_msg = match message {
            Message::Text(text) => TungsteniteMessage::Text(text),
            Message::Binary(data) => TungsteniteMessage::Binary(data),
            Message::Close => {
                self.is_active = false;
                TungsteniteMessage::Close(None)
            }
            Message::Ping(data) => TungsteniteMessage::Ping(data),
            Message::Pong(data) => TungsteniteMessage::Pong(data),
        };

        use futures_util::SinkExt;
        self.stream
            .send(tungstenite_msg)
            .await
            .map_err(|e| WebSocketError::SendError(e.to_string()))?;

        Ok(())
    }

    async fn next(&mut self) -> Option<Result<Message, WebSocketError>> {
        if !self.is_active {
            return None;
        }

        use futures_util::StreamExt;
        match self.stream.next().await {
            Some(Ok(tungstenite_msg)) => {
                let message = match tungstenite_msg {
                    TungsteniteMessage::Text(text) => Message::Text(text),
                    TungsteniteMessage::Binary(data) => Message::Binary(data),
                    TungsteniteMessage::Close(_) => {
                        self.is_active = false;
                        Message::Close
                    }
                    TungsteniteMessage::Ping(data) => Message::Ping(data),
                    TungsteniteMessage::Pong(data) => Message::Pong(data),
                    TungsteniteMessage::Frame(_) => {
                        // Skip raw frames
                        return self.next().await;
                    }
                };
                Some(Ok(message))
            }
            Some(Err(e)) => Some(Err(WebSocketError::ReceiveError(e.to_string()))),
            None => {
                self.is_active = false;
                None
            }
        }
    }

    async fn close(&mut self) -> Result<(), WebSocketError> {
        self.is_active = false;
        self.stream
            .close(None)
            .await
            .map_err(|e| WebSocketError::SendError(e.to_string()))?;
        Ok(())
    }

    fn is_active(&self) -> bool {
        self.is_active
    }
}

/// Parse command line arguments for server configuration
fn parse_args() -> (String, u16) {
    let args: Vec<String> = env::args().collect();
    let mut host = "127.0.0.1".to_string();
    let mut port = 8080u16;

    let mut i = 1;
    while i < args.len() {
        match args[i].as_str() {
            "--host" => {
                if i + 1 < args.len() {
                    host = args[i + 1].clone();
                    i += 2;
                } else {
                    eprintln!("Error: --host requires a value");
                    std::process::exit(1);
                }
            }
            "--port" => {
                if i + 1 < args.len() {
                    port = args[i + 1].parse().unwrap_or_else(|_| {
                        eprintln!("Error: Invalid port number");
                        std::process::exit(1);
                    });
                    i += 2;
                } else {
                    eprintln!("Error: --port requires a value");
                    std::process::exit(1);
                }
            }
            _ => {
                eprintln!("Unknown argument: {}", args[i]);
                eprintln!("Usage: {} [--host HOST] [--port PORT]", args[0]);
                std::process::exit(1);
            }
        }
    }

    (host, port)
}

/// Handle a single TCP connection by upgrading it to WebSocket and processing messages
async fn handle_tcp_connection(
    tcp_stream: TcpStream,
    routing_table: Arc<RoutingTable>,
    peer_addr: SocketAddr,
) {
    info!("New TCP connection from: {}", peer_addr);

    // Upgrade the TCP connection to WebSocket
    let ws_stream = match accept_async(tcp_stream).await {
        Ok(ws_stream) => ws_stream,
        Err(e) => {
            error!("Failed to upgrade connection to WebSocket: {}", e);
            return;
        }
    };

    info!("WebSocket connection established with: {}", peer_addr);

    // Wrap the WebSocket stream to implement our trait
    let wrapped_stream = TungsteniteWebSocketWrapper::new(ws_stream);

    // Handle the connection using the reusable message handler
    if let Err(e) = handle_connection(wrapped_stream, routing_table).await {
        error!(
            "Error handling WebSocket connection from {}: {}",
            peer_addr, e
        );
    } else {
        info!("WebSocket connection from {} closed gracefully", peer_addr);
    }
}

/// Main server function
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing for logging
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    println!("🚀 Starting File Transfer Server...");

    // Parse command line arguments
    let (host, port) = parse_args();
    let addr = format!("{}:{}", host, port);

    // Create the routing table for managing client connections
    let routing_table = Arc::new(RoutingTable::new());
    info!("✅ Routing table initialized");

    // Bind to the specified address
    let listener = TcpListener::bind(&addr).await?;
    println!("🌐 Server listening on: {}", addr);
    println!("📡 WebSocket endpoint: ws://{}/", addr);
    println!();
    println!("💡 Connect clients using:");
    println!("   cargo run --example file_transfer -- ws://{}/", addr);
    println!();
    println!("🔄 Waiting for connections...");

    // Main server loop - accept connections and spawn handlers
    loop {
        match listener.accept().await {
            Ok((tcp_stream, peer_addr)) => {
                let routing_table_clone = Arc::clone(&routing_table);

                // Spawn a new task to handle this connection
                tokio::spawn(async move {
                    handle_tcp_connection(tcp_stream, routing_table_clone, peer_addr).await;
                });
            }
            Err(e) => {
                error!("Failed to accept TCP connection: {}", e);
                // Continue accepting other connections
            }
        }
    }
}
