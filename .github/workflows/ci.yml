name: CI

on:
  push:
    # branches: [ main, master ]
  pull_request:
    # branches: [ main, master ]

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        components: rustfmt, clippy
    
    - name: Cache cargo registry
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Check formatting
      run: cargo fmt --all -- --check
    
    - name: Run clippy
      run: cargo clippy --all-targets --all-features -- -D warnings
    
    - name: Run tests
      run: cargo test --all-features
    
    - name: Run file transfer example
      run: |
        echo "Running file transfer example to verify end-to-end functionality..."
        timeout 60s cargo run --example file_transfer || {
          exit_code=$?
          if [ $exit_code -eq 124 ]; then
            echo "File transfer example timed out after 60 seconds"
            exit 1
          else
            echo "File transfer example failed with exit code $exit_code"
            exit $exit_code
          fi
        }
        echo "File transfer example completed successfully!"
    
    - name: Build all examples
      run: cargo build --examples
    
    - name: Check documentation
      run: cargo doc --no-deps --all-features

  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
    
    - name: Cache cargo registry
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-integration-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Run integration tests
      run: |
        echo "Running comprehensive file transfer integration test..."
        # Run the file transfer example multiple times to ensure reliability
        for i in {1..3}; do
          echo "Integration test run $i/3..."
          timeout 90s cargo run --example file_transfer
          echo "Integration test run $i completed successfully"
        done
        echo "All integration tests passed!"

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
    
    - name: Cache cargo registry
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-security-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Install cargo-audit
      run: cargo install cargo-audit
    
    - name: Run cargo-audit
      run: cargo audit
    
    - name: Install cargo-geiger
      run: cargo install cargo-geiger
    
    - name: Run cargo-geiger
      run: cargo geiger --deny-unsound --forbid-unsafe

  fuzz-smoke-test:
    name: Fuzz Smoke Test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust nightly
      uses: dtolnay/rust-toolchain@nightly
    
    - name: Cache cargo registry
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-fuzz-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Install cargo-fuzz
      run: cargo install cargo-fuzz
    
    - name: Check fuzz targets compile
      run: |
        cd fuzz
        cargo check --bin server_message_parser
        cargo check --bin session_decrypt
    
    - name: Run fuzz smoke tests
      run: |
        cd fuzz
        echo "Running server_message_parser fuzz target for 30 seconds..."
        timeout 30s cargo fuzz run server_message_parser -- -max_total_time=30 || {
          exit_code=$?
          if [ $exit_code -eq 124 ]; then
            echo "server_message_parser fuzz test completed (timeout expected)"
          else
            echo "server_message_parser fuzz test failed with exit code $exit_code"
            exit $exit_code
          fi
        }
        
        echo "Running session_decrypt fuzz target for 30 seconds..."
        timeout 30s cargo fuzz run session_decrypt -- -max_total_time=30 || {
          exit_code=$?
          if [ $exit_code -eq 124 ]; then
            echo "session_decrypt fuzz test completed (timeout expected)"
          else
            echo "session_decrypt fuzz test failed with exit code $exit_code"
            exit $exit_code
          fi
        }
        
        echo "All fuzz smoke tests completed successfully!"

  coverage:
    name: Coverage
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
    
    - name: Cache cargo registry
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-coverage-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Install cargo-tarpaulin
      run: cargo install cargo-tarpaulin
    
    - name: Generate coverage report
      run: |
        echo "Generating code coverage report..."
        cargo tarpaulin --workspace --lib --bins --out Xml --timeout 120 --exclude-files "tests/*"
        echo "Coverage report generated successfully!"
        echo "Coverage summary:"
        if [ -f cobertura.xml ]; then
          echo "✅ XML coverage report generated: cobertura.xml"
          # Extract coverage percentage from XML for display
          coverage_percent=$(grep -o 'line-rate="[0-9.]*"' cobertura.xml | head -1 | grep -o '[0-9.]*' | awk '{printf "%.2f", $1*100}')
          echo "📊 Current coverage: ${coverage_percent}%"
        else
          echo "❌ Coverage report not found"
          exit 1
        fi
    
    - name: Enforce coverage quality gate
      run: |
        echo "🔍 Enforcing coverage quality gate..."
        # Extract coverage percentage from XML report
        if [ -f cobertura.xml ]; then
          coverage_percent=$(grep -o 'line-rate="[0-9.]*"' cobertura.xml | head -1 | grep -o '[0-9.]*' | awk '{printf "%.2f", $1*100}')
          echo "Current coverage: ${coverage_percent}%"
          
          # Set minimum coverage threshold
          # Note: Starting with 35% (below current baseline) and can be gradually increased
          # Target is to reach 85% over time through improved testing
          min_coverage=35.0
          echo "Minimum required coverage: ${min_coverage}%"
          
          # Compare coverage using awk for floating point comparison
          if awk "BEGIN {exit !(${coverage_percent} >= ${min_coverage})}"; then
            echo "✅ Coverage quality gate passed! (${coverage_percent}% >= ${min_coverage}%)"
            echo "🎯 Long-term target: 85% coverage"
            if awk "BEGIN {exit !(${coverage_percent} >= 85.0)}"; then
              echo "🎉 Excellent! You've reached the target coverage of 85%!"
            elif awk "BEGIN {exit !(${coverage_percent} >= 60.0)}"; then
              echo "📈 Good progress! Consider increasing the minimum threshold."
            fi
          else
            echo "❌ Coverage quality gate failed!"
            echo "   Current: ${coverage_percent}%"
            echo "   Required: ${min_coverage}%"
            echo "   Please add more tests to improve coverage."
            exit 1
          fi
        else
          echo "❌ No coverage report found for quality gate check"
          exit 1
        fi
    
    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: cobertura.xml
        retention-days: 30
    
    - name: Upload coverage to Codecov (optional)
      uses: codecov/codecov-action@v4
      with:
        file: ./cobertura.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false